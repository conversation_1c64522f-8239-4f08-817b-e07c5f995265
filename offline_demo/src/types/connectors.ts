export interface Connector {
  id: string
  name: string
  description: string
  type: 'source' | 'destination'
  category: 'database' | 'warehouse' | 'repository' | 'api' | 'file'
  icon: string
  status: 'connected' | 'disconnected' | 'error'
  connectionCount?: number
  lastConnected?: string
  configuration?: Record<string, any>
  features?: string[]
  popularity?: number
  isNew?: boolean
  isBeta?: boolean
}

export interface ConnectorConfig {
  id: string
  name: string
  host?: string
  port?: number
  database?: string
  username?: string
  password?: string
  apiKey?: string
  repository?: string
  branch?: string
  [key: string]: any
}

export const sourceConnectors: Omit<Connector, 'id' | 'status' | 'connectionCount' | 'lastConnected'>[] = [
  {
    name: 'PostgreSQL',
    description: 'Advanced open-source relational database with enterprise features',
    type: 'source',
    category: 'database',
    icon: '🐘',
    features: ['ACID Compliance', 'JSON Support', 'Full-text Search', 'Streaming Replication'],
    popularity: 95,
    isNew: false,
    isBeta: false
  },
  {
    name: 'MySQL',
    description: 'World\'s most popular open-source relational database',
    type: 'source',
    category: 'database',
    icon: '🐬',
    features: ['High Performance', 'Scalability', 'Security', 'Flexibility'],
    popularity: 92,
    isNew: false,
    isBeta: false
  },
  {
    name: 'Microsoft SQL Server',
    description: 'Enterprise-grade relational database management system',
    type: 'source',
    category: 'database',
    icon: '🏢',
    features: ['Enterprise Security', 'High Availability', 'Business Intelligence', 'Cloud Integration'],
    popularity: 88,
    isNew: false,
    isBeta: false
  },
  {
    name: 'GitHub',
    description: 'The world\'s leading software development platform',
    type: 'source',
    category: 'repository',
    icon: '🐙',
    features: ['Git Version Control', 'CI/CD', 'Issue Tracking', 'Code Review'],
    popularity: 98,
    isNew: false,
    isBeta: false
  },
  {
    name: 'GitLab',
    description: 'Complete DevOps platform delivered as a single application',
    type: 'source',
    category: 'repository',
    icon: '🦊',
    features: ['Built-in CI/CD', 'Security Scanning', 'Project Management', 'Monitoring'],
    popularity: 78,
    isNew: false,
    isBeta: false
  },
  {
    name: 'Snowflake',
    description: 'Cloud data platform for instant elasticity and global scale',
    type: 'source',
    category: 'warehouse',
    icon: '❄️',
    features: ['Instant Scaling', 'Data Sharing', 'Multi-cloud', 'Zero Management'],
    popularity: 85,
    isNew: false,
    isBeta: false
  },
  {
    name: 'Oracle Database',
    description: 'Multi-model database management system for enterprise workloads',
    type: 'source',
    category: 'database',
    icon: '🔶',
    features: ['Multi-model', 'In-Memory Processing', 'Advanced Analytics', 'Autonomous Features'],
    popularity: 82,
    isNew: false,
    isBeta: false
  },
  {
    name: 'Azure Cosmos DB',
    description: 'Globally distributed, multi-model database service',
    type: 'source',
    category: 'database',
    icon: '🌌',
    features: ['Global Distribution', 'Multi-model', 'Guaranteed SLAs', 'Elastic Scale'],
    popularity: 75,
    isNew: false,
    isBeta: false
  },
  {
    name: 'MongoDB',
    description: 'Document-oriented NoSQL database for modern applications',
    type: 'source',
    category: 'database',
    icon: '🍃',
    features: ['Document Model', 'Horizontal Scaling', 'Rich Query Language', 'ACID Transactions'],
    popularity: 89,
    isNew: false,
    isBeta: false
  },
  {
    name: 'Redis',
    description: 'In-memory data structure store for caching and real-time analytics',
    type: 'source',
    category: 'database',
    icon: '🔴',
    features: ['In-Memory Speed', 'Data Structures', 'Pub/Sub', 'Lua Scripting'],
    popularity: 86,
    isNew: false,
    isBeta: false
  },
  {
    name: 'Salesforce',
    description: 'World\'s leading customer relationship management platform',
    type: 'source',
    category: 'api',
    icon: '☁️',
    features: ['CRM Data', 'Real-time APIs', 'Custom Objects', 'Metadata API'],
    popularity: 84,
    isNew: false,
    isBeta: false
  },
  {
    name: 'CSV Files',
    description: 'Simple and universal comma-separated values file format',
    type: 'source',
    category: 'file',
    icon: '📊',
    features: ['Universal Format', 'Lightweight', 'Excel Compatible', 'Streaming Support'],
    popularity: 100,
    isNew: false,
    isBeta: false
  },
  {
    name: 'Apache Kafka',
    description: 'Distributed streaming platform for real-time data feeds',
    type: 'source',
    category: 'api',
    icon: '📡',
    features: ['Real-time Streaming', 'High Throughput', 'Fault Tolerant', 'Scalable'],
    popularity: 81,
    isNew: true,
    isBeta: false
  }
]

export const destinationConnectors: Omit<Connector, 'id' | 'status' | 'connectionCount' | 'lastConnected'>[] = [
  {
    name: 'Databricks',
    description: 'Unified analytics platform for big data and machine learning',
    type: 'destination',
    category: 'warehouse',
    icon: '🧱',
    features: ['Apache Spark', 'MLflow', 'Delta Lake', 'Collaborative Notebooks'],
    popularity: 88,
    isNew: false,
    isBeta: false
  },
  {
    name: 'Amazon Redshift',
    description: 'Fast, secure and reliable cloud data warehouse',
    type: 'destination',
    category: 'warehouse',
    icon: '🔴',
    features: ['Columnar Storage', 'Massively Parallel', 'Advanced Compression', 'Machine Learning'],
    popularity: 85,
    isNew: false,
    isBeta: false
  },
  {
    name: 'Google BigQuery',
    description: 'Serverless, highly scalable data warehouse for analytics',
    type: 'destination',
    category: 'warehouse',
    icon: '📊',
    features: ['Serverless', 'Real-time Analytics', 'Machine Learning', 'Geospatial Analysis'],
    popularity: 90,
    isNew: false,
    isBeta: false
  },
  {
    name: 'Snowflake',
    description: 'Cloud data platform with instant elasticity and data sharing',
    type: 'destination',
    category: 'warehouse',
    icon: '❄️',
    features: ['Instant Scaling', 'Data Sharing', 'Multi-cloud', 'Zero Management'],
    popularity: 87,
    isNew: false,
    isBeta: false
  },
  {
    name: 'Azure Synapse',
    description: 'Limitless analytics service for enterprise data warehousing',
    type: 'destination',
    category: 'warehouse',
    icon: '🔷',
    features: ['Unified Experience', 'On-demand/Provisioned', 'Integrated ML', 'Security & Compliance'],
    popularity: 82,
    isNew: false,
    isBeta: false
  },
  {
    name: 'dbt Cloud',
    description: 'Modern data transformation platform for analytics engineers',
    type: 'destination',
    category: 'warehouse',
    icon: '🔧',
    features: ['SQL Transformations', 'Version Control', 'Testing', 'Documentation'],
    popularity: 76,
    isNew: true,
    isBeta: false
  },
  {
    name: 'ClickHouse',
    description: 'Fast open-source column-oriented database for analytics',
    type: 'destination',
    category: 'warehouse',
    icon: '⚡',
    features: ['Columnar Storage', 'Real-time Queries', 'Compression', 'Distributed Processing'],
    popularity: 72,
    isNew: true,
    isBeta: false
  },
  {
    name: 'Elasticsearch',
    description: 'Distributed search and analytics engine for all types of data',
    type: 'destination',
    category: 'warehouse',
    icon: '🔍',
    features: ['Full-text Search', 'Real-time Analytics', 'Distributed', 'RESTful API'],
    popularity: 78,
    isNew: false,
    isBeta: false
  }
]

export const connectorCategories = [
  {
    id: 'database',
    name: 'Databases',
    description: 'Relational and NoSQL databases',
    icon: '🗄️',
    color: 'blue'
  },
  {
    id: 'warehouse',
    name: 'Data Warehouses',
    description: 'Analytics and data warehouse platforms',
    icon: '🏭',
    color: 'purple'
  },
  {
    id: 'repository',
    name: 'Code Repositories',
    description: 'Version control and source code management',
    icon: '📚',
    color: 'green'
  },
  {
    id: 'api',
    name: 'APIs & SaaS',
    description: 'Third-party APIs and SaaS platforms',
    icon: '🌐',
    color: 'yellow'
  },
  {
    id: 'file',
    name: 'File Systems',
    description: 'File-based data sources',
    icon: '📁',
    color: 'gray'
  }
]
