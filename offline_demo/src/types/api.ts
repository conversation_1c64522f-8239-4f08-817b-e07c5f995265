// Base interfaces
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  offset: number
  limit: number
}

export interface JobConfig {
  task_id: string
  configuration: Record<string, any>
}

export interface JobResponse {
  job_id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  created_at: string
}

// Job Status interfaces
export interface JobStatus {
  job_id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  steps: JobStep[]
  error_message?: string
  started_at?: string
  completed_at?: string
}

export interface JobStep {
  step_id: string
  title: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  content: string // Markdown format
  timestamp: string
  duration?: number
}

// Connector interfaces
export interface ConnectorResponse {
  id: string
  name: string
  type: 'source' | 'destination'
  status: 'connected' | 'disconnected' | 'error'
  configuration: Record<string, any>
  created_at: string
  updated_at: string
}

export interface ConnectorTestResponse {
  success: boolean
  message: string
  details?: any
}

// File upload interfaces
export interface FileUploadResponse {
  file_id: string
  filename: string
  size: number
  content_type: string
  upload_url?: string
}

export interface FilePreviewResponse {
  columns: string[]
  data: Record<string, any>[]
  total_rows: number
  preview_rows: number
}

// Business Value Questions interfaces
export interface BVQ {
  bvq_id: string
  bvq_content: string
  bvq_relevancy: 'none' | 'low' | 'medium' | 'high'
  bvq_purpose: string
  created_at: string
}

export interface BVQGenerationRequest {
  business_objective: string
}

export interface BVQGenerationResponse {
  bvqs: BVQ[]
}

// Data Model interfaces
export interface DataModel {
  data_model_id: string
  name: string
  description: string
  industry: string
  official_website?: string
  example_usage?: string
  explanation?: string
}

export interface DataModelSearchResponse {
  models: DataModel[]
  total: number
}

// Connection interfaces
export interface Connection {
  id: string
  name: string
  source_id: string
  destination_id: string
  status: 'active' | 'paused' | 'error'
  last_sync: string
  next_sync: string
  records_synced: number
  created_at: string
}

export interface ConnectionTestRequest {
  source_id: string
  destination_id: string
}

export interface ConnectionCreateRequest {
  name: string
  source_id: string
  destination_id: string
  source_type: 'database' | 'file'
  file_id?: string
  sync_frequency?: string
}

// Business Objectives interfaces
export interface BusinessObjective {
  id: string
  title: string
  description: string
  priority: 'high' | 'medium' | 'low'
  category: 'data_quality' | 'performance' | 'compliance' | 'cost_optimization' | 'analytics' | 'security'
  status: 'draft' | 'active' | 'completed' | 'archived'
  metrics: ObjectiveMetric[]
  timeline: {
    start_date: string
    target_date: string
    completion_date?: string
  }
  stakeholders: string[]
  business_impact: string
  technical_requirements: string[]
  created_at: string
  updated_at: string
}

export interface ObjectiveMetric {
  id: string
  name: string
  description: string
  target_value: number
  current_value: number
  unit: string
  type: 'kpi' | 'sla' | 'threshold'
}

export interface ObjectiveTemplate {
  id: string
  name: string
  description: string
  category: string
  metrics: Omit<ObjectiveMetric, 'id' | 'current_value'>[]
  estimated_timeline: number // days
}

// Data Model Mapping interfaces
export interface DataModel {
  id: string
  name: string
  version: string
  description: string
  type: 'industry_standard' | 'custom' | 'template'
  category: 'healthcare' | 'finance' | 'retail' | 'manufacturing' | 'logistics' | 'generic'
  entities: DataEntity[]
  relationships: DataRelationship[]
  compliance_frameworks: string[]
  created_at: string
  updated_at: string
}

export interface DataEntity {
  id: string
  name: string
  description: string
  table_name: string
  attributes: DataAttribute[]
  primary_key: string[]
  indexes: DataIndex[]
  constraints: DataConstraint[]
}

export interface DataAttribute {
  id: string
  name: string
  description: string
  data_type: string
  nullable: boolean
  default_value?: any
  constraints: string[]
  business_rules: string[]
  sensitivity_level: 'public' | 'internal' | 'confidential' | 'restricted'
}

export interface DataRelationship {
  id: string
  name: string
  type: 'one_to_one' | 'one_to_many' | 'many_to_many'
  source_entity: string
  target_entity: string
  source_attributes: string[]
  target_attributes: string[]
  cardinality: string
}

export interface DataIndex {
  name: string
  type: 'primary' | 'unique' | 'index' | 'clustered'
  columns: string[]
}

export interface DataConstraint {
  name: string
  type: 'check' | 'foreign_key' | 'unique' | 'not_null'
  definition: string
}

export interface MappingRule {
  id: string
  source_field: string
  target_field: string
  transformation: string
  validation_rules: string[]
  confidence_score: number
  status: 'suggested' | 'approved' | 'rejected' | 'manual'
}

export interface DataMapping {
  id: string
  name: string
  description: string
  source_schema: string
  target_model: string
  mapping_rules: MappingRule[]
  validation_status: 'pending' | 'valid' | 'invalid'
  completion_percentage: number
  ai_suggestions: AISuggestion[]
  created_at: string
  updated_at: string
}

export interface AISuggestion {
  id: string
  type: 'mapping' | 'transformation' | 'validation'
  suggestion: string
  confidence: number
  reasoning: string
  accepted: boolean
}

// Settings interfaces
export interface UserProfile {
  id: string
  username: string
  email: string
  full_name: string
  avatar_url?: string
  role: 'admin' | 'data_engineer' | 'analyst' | 'viewer'
  preferences: UserPreferences
  created_at: string
  last_login: string
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  notifications: NotificationSettings
  dashboard_layout: DashboardLayout
  default_page_size: number
  date_format: string
  number_format: string
}

export interface NotificationSettings {
  email_enabled: boolean
  push_enabled: boolean
  job_completion: boolean
  job_failure: boolean
  system_updates: boolean
  security_alerts: boolean
  weekly_reports: boolean
}

export interface DashboardLayout {
  widgets: DashboardWidget[]
  layout: 'grid' | 'list'
  columns: number
}

export interface DashboardWidget {
  id: string
  type: string
  position: { x: number; y: number; w: number; h: number }
  config: Record<string, any>
}

export interface WorkspaceSettings {
  id: string
  name: string
  description: string
  default_retention_days: number
  security_policy: SecurityPolicy
  integration_settings: IntegrationSettings
  audit_settings: AuditSettings
}

export interface SecurityPolicy {
  password_policy: PasswordPolicy
  session_timeout: number
  mfa_required: boolean
  ip_whitelist: string[]
  encryption_at_rest: boolean
  encryption_in_transit: boolean
}

export interface PasswordPolicy {
  min_length: number
  require_uppercase: boolean
  require_lowercase: boolean
  require_numbers: boolean
  require_symbols: boolean
  expiry_days: number
}

export interface IntegrationSettings {
  ai_models: AIModelConfig[]
  external_apis: ExternalAPIConfig[]
  data_sources: DataSourceConfig[]
}

export interface AIModelConfig {
  provider: string
  model_name: string
  api_key: string
  endpoint: string
  parameters: Record<string, any>
}

export interface ExternalAPIConfig {
  name: string
  base_url: string
  auth_type: 'api_key' | 'oauth' | 'basic'
  credentials: Record<string, string>
}

export interface DataSourceConfig {
  name: string
  type: string
  connection_string: string
  ssl_enabled: boolean
  timeout: number
}

export interface AuditSettings {
  enabled: boolean
  retention_days: number
  log_level: 'basic' | 'detailed' | 'verbose'
  events: AuditEvent[]
}

export interface AuditEvent {
  type: string
  enabled: boolean
  description: string
}
