'use client'
import { useAppStore } from '@/store/useAppStore'
import Layout from '@/components/common/Layout'
import DashboardPage from '@/components/dashboard/DashboardPage'
import ConnectorsPage from '@/components/connectors/ConnectorsPage'
import IngestionsPage from '@/components/ingestion/IngestionsPage'
import MigrationPage from '@/components/migration/MigrationPage'
import ObjectivesPage from '@/components/objectives/ObjectivesPage'
import MappingPage from '@/components/mapping/MappingPage'
import SettingsPage from '@/components/settings/SettingsPage'

export default function HomePage() {
  const { activeTab } = useAppStore()

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardPage />
      case 'connectors':
        return <ConnectorsPage />
      case 'ingestions':
        return <IngestionsPage />
      case 'migration':
        return <MigrationPage />
      case 'objectives':
        return <ObjectivesPage />
      case 'mapping':
        return <MappingPage />
      case 'settings':
        return <SettingsPage />
      default:
        return <DashboardPage />
    }
  }

  return (
    <Layout>
      <div className="animate-fade-in">
        {renderActiveTab()}
      </div>
    </Layout>
  )
}
