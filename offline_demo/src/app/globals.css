@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

@layer base {
  * {
    @apply border-neutral-200;
  }
  
  html {
    @apply scroll-smooth;
  }
  
  body {
    @apply bg-gradient-to-br from-neutral-50 via-white to-neutral-100;
    @apply text-neutral-900 font-sans antialiased;
    @apply selection:bg-primary-200 selection:text-primary-900;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    font-variation-settings: 'opsz' auto;
  }

  /* Dark mode support */
  .dark body {
    @apply bg-gradient-to-br from-neutral-950 via-neutral-900 to-neutral-800;
    @apply text-neutral-100;
    @apply selection:bg-primary-800 selection:text-primary-100;
  }

  /* Scrollbar styling */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-neutral-100 rounded-full;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-neutral-300 rounded-full hover:bg-neutral-400;
    @apply transition-colors duration-200;
  }

  .dark ::-webkit-scrollbar-track {
    @apply bg-neutral-800;
  }

  .dark ::-webkit-scrollbar-thumb {
    @apply bg-neutral-600 hover:bg-neutral-500;
  }

  /* Focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-white;
    @apply transition-all duration-200;
  }

  .dark :focus-visible {
    @apply ring-offset-neutral-900;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center;
    @apply px-4 py-2 text-sm font-medium;
    @apply rounded-xl transition-all duration-200;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
    @apply relative overflow-hidden;
  }

  .btn-primary {
    @apply btn bg-gradient-to-r from-primary-500 to-primary-600;
    @apply text-white shadow-lg hover:shadow-xl;
    @apply hover:from-primary-600 hover:to-primary-700;
    @apply focus-visible:ring-primary-500;
    @apply transform hover:scale-[1.02] active:scale-[0.98];
  }

  .btn-secondary {
    @apply btn bg-white text-neutral-900;
    @apply border border-neutral-200 shadow-sm;
    @apply hover:bg-neutral-50 hover:shadow-md;
    @apply focus-visible:ring-neutral-500;
    @apply transform hover:scale-[1.02] active:scale-[0.98];
  }

  .btn-ghost {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200;
    @apply text-neutral-700 hover:text-neutral-900;
    @apply hover:bg-neutral-100 active:bg-neutral-200;
    @apply border border-transparent hover:border-neutral-200;
  }

  .btn-gradient {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200;
    @apply text-white shadow-lg;
    background: linear-gradient(135deg, theme('colors.primary.500') 0%, theme('colors.primary.600') 100%);
  }

  .btn-gradient:hover {
    background: linear-gradient(135deg, theme('colors.primary.600') 0%, theme('colors.primary.700') 100%);
    @apply shadow-xl transform -translate-y-0.5;
  }

  .btn-primary-sm {
    @apply btn-primary px-3 py-1.5 text-xs;
  }

  .btn-secondary-sm {
    @apply btn-secondary px-3 py-1.5 text-xs;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-2xl shadow-sm border border-neutral-200;
    @apply transition-all duration-300 ease-out;
    @apply backdrop-blur-sm;
  }

  .card-hover {
    @apply card hover:shadow-xl hover:shadow-neutral-200/50;
    @apply hover:-translate-y-1 hover:border-neutral-300;
    @apply cursor-pointer;
  }

  .card-glass {
    @apply bg-white/80 backdrop-blur-xl;
    @apply border border-white/20 shadow-2xl;
    @apply rounded-2xl;
  }

  .card-elevated {
    @apply card shadow-xl;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  }

  .card-gradient {
    @apply card;
    background: linear-gradient(145deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.8) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.2);
  }

  .card-neon {
    @apply card;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  .card-neon:hover {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
  }

  /* Form Components */
  .input {
    @apply w-full px-3 py-2 border border-neutral-300 rounded-lg;
    @apply focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
    @apply placeholder-neutral-400 transition-all duration-200;
    @apply hover:border-neutral-400;
  }

  .input:focus {
    @apply outline-none;
  }

  .input-error {
    @apply input border-error-300 focus:ring-error-500 focus:border-error-500;
    @apply bg-error-50;
  }

  .label {
    @apply block text-sm font-medium text-neutral-700 mb-2;
  }

  /* Toggle switch component */
  .switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
  }

  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: theme('colors.neutral.300');
    transition: 0.3s;
    border-radius: 12px;
  }

  .slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  input:checked + .slider {
    background-color: theme('colors.primary.500');
  }

  input:focus + .slider {
    box-shadow: 0 0 0 3px theme('colors.primary.100');
  }

  input:checked + .slider:before {
    transform: translateX(20px);
  }

  /* Status Components */
  .status-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
    @apply transition-all duration-200;
  }

  .status-success {
    @apply status-badge bg-success-100 text-success-800;
    @apply ring-1 ring-success-200;
  }

  .status-error {
    @apply status-badge bg-error-100 text-error-800;
    @apply ring-1 ring-error-200;
  }

  .status-warning {
    @apply status-badge bg-warning-100 text-warning-800;
    @apply ring-1 ring-warning-200;
  }

  /* Navigation Components */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-xl;
    @apply text-neutral-600 hover:text-neutral-900;
    @apply hover:bg-neutral-100 transition-all duration-200;
    @apply relative group;
  }

  .nav-link-active {
    @apply nav-link bg-primary-50 text-primary-700;
    @apply shadow-sm border border-primary-100;
  }

  /* Loading Components */
  .skeleton {
    @apply bg-gradient-to-r from-neutral-200 via-neutral-300 to-neutral-200;
    @apply animate-skeleton;
    background-size: 200% 100%;
  }

  .spinner {
    @apply animate-spin rounded-full border-2 border-neutral-200;
    @apply border-t-primary-500;
  }

  /* Status indicators */
  .status-dot {
    @apply w-2 h-2 rounded-full;
  }

  .status-dot-pulse {
    @apply status-dot animate-pulse;
  }

  .status-connected {
    @apply bg-success-500;
    box-shadow: 0 0 8px rgba(34, 197, 94, 0.4);
  }

  .status-error {
    @apply bg-error-500;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
  }

  .status-warning {
    @apply bg-warning-500;
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
  }

  /* Progress bars */
  .progress-bar {
    @apply w-full bg-neutral-200 rounded-full h-2 overflow-hidden;
  }

  .progress-fill {
    @apply h-full bg-gradient-to-r from-primary-500 to-primary-600 rounded-full;
    transition: width 0.3s ease-in-out;
  }

  .progress-fill-success {
    @apply h-full bg-gradient-to-r from-success-500 to-success-600 rounded-full;
  }

  .progress-fill-warning {
    @apply h-full bg-gradient-to-r from-warning-500 to-warning-600 rounded-full;
  }

  .progress-fill-error {
    @apply h-full bg-gradient-to-r from-error-500 to-error-600 rounded-full;
  }

  /* Loading states */
  .skeleton {
    @apply bg-gradient-to-r from-neutral-200 via-neutral-300 to-neutral-200;
    @apply animate-skeleton;
    background-size: 200% 100%;
  }

  .loading-shimmer {
    position: relative;
    overflow: hidden;
  }

  .loading-shimmer::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    animation: shimmer 1.5s infinite;
  }

  /* Advanced hover effects */
  .hover-lift {
    @apply transition-all duration-200;
  }

  .hover-lift:hover {
    @apply transform -translate-y-1 shadow-lg;
  }

  .hover-glow {
    @apply transition-all duration-300;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  /* Interactive elements */
  .interactive {
    @apply transition-all duration-200 cursor-pointer;
  }

  .interactive:hover {
    @apply transform scale-105;
  }

  .interactive:active {
    @apply transform scale-95;
  }

  /* Modern badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-800 border border-primary-200;
  }

  .badge-success {
    @apply badge bg-success-100 text-success-800 border border-success-200;
  }

  .badge-warning {
    @apply badge bg-warning-100 text-warning-800 border border-warning-200;
  }

  .badge-error {
    @apply badge bg-error-100 text-error-800 border border-error-200;
  }

  .badge-neutral {
    @apply badge bg-neutral-100 text-neutral-800 border border-neutral-200;
  }

  /* Glassmorphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }
}

@layer utilities {
  /* Glass morphism utilities */
  .glass-light {
    @apply bg-white/20 backdrop-blur-md border border-white/30;
  }

  .glass-dark {
    @apply bg-black/20 backdrop-blur-md border border-white/10;
  }

  /* Text utilities */
  .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-primary {
    background: linear-gradient(135deg, theme('colors.primary.500') 0%, theme('colors.primary.700') 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Animation utilities */
  .animate-in {
    @apply animate-fade-in-up;
  }

  @keyframes fadeInScale {
    0% {
      opacity: 0;
      transform: scale(0.95);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  .animate-fade-in-scale {
    animation: fadeInScale 0.3s ease-out;
  }

  /* Stagger animation utility */
  .stagger-animation > * {
    opacity: 0;
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
  .stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
  .stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
  .stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
  .stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
  .stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }

  /* Custom scrollbar */
  .scrollbar-none {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }

  /* Enhanced modern scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.neutral.300') transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: theme('colors.neutral.300');
    border-radius: 2px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: theme('colors.neutral.400');
  }
}

/* Modern focus styles for accessibility */
@media (prefers-reduced-motion: no-preference) {
  .motion-safe\:animate-spin {
    animation: spin 1s linear infinite;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
