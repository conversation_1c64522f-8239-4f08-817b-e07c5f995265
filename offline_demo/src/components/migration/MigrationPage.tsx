'use client'
import { useState } from 'react'
import { 
  Upload, 
  Github, 
  FileText, 
  Zap, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Code2,
  ArrowRight,
  Download,
  Play,
  RotateCcw,
  Target,
  Database
} from 'lucide-react'
import { formatDate, cn } from '@/lib/utils'

interface MigrationProject {
  id: string
  name: string
  description: string
  status: 'analyzing' | 'ready' | 'migrating' | 'completed' | 'error'
  progress: number
  sourceType: string
  targetType: string
  filesAnalyzed: number
  complexity: 'low' | 'medium' | 'high'
  estimatedHours: number
  createdAt: string
  completedAt?: string
}

export default function MigrationPage() {
  const [activeTab, setActiveTab] = useState<'projects' | 'new'>('projects')

  // Mock migration projects
  const projects: MigrationProject[] = [
    {
      id: 'mig-1',
      name: 'Legacy ETL Modernization',
      description: 'Migrate Apache Airflow DAGs to Databricks workflows with PySpark optimization',
      status: 'completed',
      progress: 100,
      sourceType: 'Apache Airflow',
      targetType: 'Databricks',
      filesAnalyzed: 45,
      complexity: 'high',
      estimatedHours: 120,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(),
      completedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString()
    },
    {
      id: 'mig-2',
      name: 'Data Warehouse Migration',
      description: 'Convert SQL Server stored procedures to Snowflake native functions',
      status: 'migrating',
      progress: 65,
      sourceType: 'SQL Server',
      targetType: 'Snowflake',
      filesAnalyzed: 89,
      complexity: 'medium',
      estimatedHours: 80,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString()
    },
    {
      id: 'mig-3',
      name: 'Streaming Pipeline Upgrade',
      description: 'Modernize Kafka Streams applications to use Confluent Cloud native features',
      status: 'ready',
      progress: 0,
      sourceType: 'Kafka Streams',
      targetType: 'Confluent Cloud',
      filesAnalyzed: 23,
      complexity: 'low',
      estimatedHours: 40,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString()
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'analyzing':
        return <Clock className="w-4 h-4 text-warning-500 animate-pulse" />
      case 'ready':
        return <CheckCircle className="w-4 h-4 text-success-500" />
      case 'migrating':
        return <Zap className="w-4 h-4 text-primary-500 animate-pulse" />
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-success-500" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-error-500" />
      default:
        return <Clock className="w-4 h-4 text-neutral-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'analyzing':
        return 'bg-warning-100 text-warning-700 border-warning-200'
      case 'ready':
        return 'bg-success-100 text-success-700 border-success-200'
      case 'migrating':
        return 'bg-primary-100 text-primary-700 border-primary-200'
      case 'completed':
        return 'bg-success-100 text-success-700 border-success-200'
      case 'error':
        return 'bg-error-100 text-error-700 border-error-200'
      default:
        return 'bg-neutral-100 text-neutral-700 border-neutral-200'
    }
  }

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low':
        return 'bg-success-100 text-success-700'
      case 'medium':
        return 'bg-warning-100 text-warning-700'
      case 'high':
        return 'bg-error-100 text-error-700'
      default:
        return 'bg-neutral-100 text-neutral-700'
    }
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-neutral-900">Pipeline Migration</h1>
          <p className="text-neutral-600 mt-1">AI-powered analysis and automated modernization of legacy data systems</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-neutral-200">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('projects')}
            className={cn(
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === 'projects' 
                ? 'border-primary-500 text-primary-600' 
                : 'border-transparent text-neutral-500 hover:text-neutral-700'
            )}
          >
            Migration Projects
          </button>
          <button
            onClick={() => setActiveTab('new')}
            className={cn(
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === 'new' 
                ? 'border-primary-500 text-primary-600' 
                : 'border-transparent text-neutral-500 hover:text-neutral-700'
            )}
          >
            Create New Migration
          </button>
        </nav>
      </div>

      {activeTab === 'projects' && (
        <div className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="card p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-neutral-100 rounded-lg flex items-center justify-center">
                  <Code2 className="w-5 h-5 text-neutral-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-neutral-900">{projects.length}</p>
                  <p className="text-sm text-neutral-600">Total Projects</p>
                </div>
              </div>
            </div>
            
            <div className="card p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-primary-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-neutral-900">1</p>
                  <p className="text-sm text-neutral-600">In Progress</p>
                </div>
              </div>
            </div>
            
            <div className="card p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-success-100 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-success-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-neutral-900">1</p>
                  <p className="text-sm text-neutral-600">Completed</p>
                </div>
              </div>
            </div>
            
            <div className="card p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-warning-100 rounded-lg flex items-center justify-center">
                  <Target className="w-5 h-5 text-warning-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-neutral-900">240h</p>
                  <p className="text-sm text-neutral-600">Total Effort</p>
                </div>
              </div>
            </div>
          </div>

          {/* Projects List */}
          <div className="space-y-4">
            {projects.map((project, index) => (
              <div 
                key={project.id} 
                className="card p-6 hover:shadow-lg transition-all duration-200"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start space-x-4">
                    {getStatusIcon(project.status)}
                    <div>
                      <h3 className="font-semibold text-neutral-900">{project.name}</h3>
                      <p className="text-sm text-neutral-600 mt-1">{project.description}</p>
                      <div className="flex items-center space-x-4 mt-2">
                        <div className="flex items-center space-x-2 text-sm">
                          <span className="text-neutral-600">From:</span>
                          <span className="font-medium text-neutral-900">{project.sourceType}</span>
                          <ArrowRight className="w-3 h-3 text-neutral-400" />
                          <span className="font-medium text-neutral-900">{project.targetType}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span className={cn(
                      'text-xs px-2 py-1 rounded-full border font-medium',
                      getStatusColor(project.status)
                    )}>
                      {project.status}
                    </span>
                    <span className={cn(
                      'text-xs px-2 py-1 rounded-full font-medium',
                      getComplexityColor(project.complexity)
                    )}>
                      {project.complexity}
                    </span>
                  </div>
                </div>

                {/* Progress Bar */}
                {(project.status === 'migrating' || project.status === 'analyzing') && (
                  <div className="mb-4">
                    <div className="flex items-center justify-between text-sm mb-2">
                      <span className="text-neutral-600">Progress</span>
                      <span className="text-neutral-900">{project.progress}%</span>
                    </div>
                    <div className="w-full bg-neutral-200 rounded-full h-2">
                      <div 
                        className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${project.progress}%` }}
                      />
                    </div>
                  </div>
                )}

                {/* Project Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-neutral-600">Files Analyzed</p>
                    <p className="font-medium text-neutral-900">{project.filesAnalyzed}</p>
                  </div>
                  <div>
                    <p className="text-neutral-600">Estimated Effort</p>
                    <p className="font-medium text-neutral-900">{project.estimatedHours}h</p>
                  </div>
                  <div>
                    <p className="text-neutral-600">Created</p>
                    <p className="font-medium text-neutral-900">{formatDate(project.createdAt, 'relative')}</p>
                  </div>
                  <div>
                    <p className="text-neutral-600">
                      {project.status === 'completed' ? 'Completed' : 'Status'}
                    </p>
                    <p className="font-medium text-neutral-900">
                      {project.completedAt ? formatDate(project.completedAt, 'relative') : project.status}
                    </p>
                  </div>
                </div>

                {/* Actions */}
                <div className="mt-4 pt-4 border-t border-neutral-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 text-sm text-neutral-600">
                      <Code2 className="w-4 h-4" />
                      <span>Complexity: {project.complexity}</span>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {project.status === 'ready' && (
                        <button className="btn-primary-sm flex items-center space-x-1">
                          <Play className="w-3 h-3" />
                          <span>Start Migration</span>
                        </button>
                      )}
                      {project.status === 'completed' && (
                        <button className="btn-secondary-sm flex items-center space-x-1">
                          <Download className="w-3 h-3" />
                          <span>Download</span>
                        </button>
                      )}
                      {project.status === 'error' && (
                        <button className="btn-secondary-sm flex items-center space-x-1">
                          <RotateCcw className="w-3 h-3" />
                          <span>Retry</span>
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'new' && (
        <div className="max-w-4xl">
          <div className="card p-8">
            <h2 className="text-xl font-semibold text-neutral-900 mb-6">Create New Migration Project</h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              {/* Upload Method */}
              <div className="space-y-4">
                <h3 className="font-medium text-neutral-900">Upload Source Code</h3>
                
                <div className="border-2 border-dashed border-neutral-300 rounded-lg p-8 text-center hover:border-primary-400 transition-colors">
                  <Upload className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                  <p className="text-neutral-600 mb-2">Drop your files here or click to browse</p>
                  <p className="text-sm text-neutral-500">Supports .zip, .tar.gz, or individual files</p>
                  <button className="btn-primary mt-4">Choose Files</button>
                </div>
              </div>

              {/* Select from Connected Source Connectors */}
              <div className="space-y-4">
                <h3 className="font-medium text-neutral-900">Select Source Repository</h3>
                <p className="text-sm text-neutral-600">Choose from your connected source connectors</p>
                
                <div className="space-y-3">
                  {/* GitHub Repository Option */}
                  <div className="border border-neutral-300 rounded-lg p-4 hover:border-primary-400 transition-colors cursor-pointer">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Github className="w-6 h-6 text-neutral-900" />
                        <div>
                          <h4 className="font-medium text-neutral-900">GitHub Repository</h4>
                          <p className="text-sm text-neutral-600">github.com/company/data-pipeline-legacy</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                            <span className="text-xs text-success-600">Connected</span>
                            <span className="text-xs text-neutral-500">• 5 active connections</span>
                          </div>
                        </div>
                      </div>
                      <input type="radio" name="source_connector" value="github" className="w-4 h-4 text-primary-600" />
                    </div>
                  </div>

                  {/* GitLab Repository Option */}
                  <div className="border border-neutral-300 rounded-lg p-4 hover:border-primary-400 transition-colors cursor-pointer">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-6 h-6 bg-orange-500 rounded flex items-center justify-center">
                          <span className="text-white font-bold text-xs">GL</span>
                        </div>
                        <div>
                          <h4 className="font-medium text-neutral-900">GitLab Enterprise</h4>
                          <p className="text-sm text-neutral-600">gitlab.company.com/data-team/etl-scripts</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                            <span className="text-xs text-success-600">Connected</span>
                            <span className="text-xs text-neutral-500">• 3 active connections</span>
                          </div>
                        </div>
                      </div>
                      <input type="radio" name="source_connector" value="gitlab" className="w-4 h-4 text-primary-600" />
                    </div>
                  </div>

                  {/* PostgreSQL Production Option */}
                  {/* <div className="border border-neutral-300 rounded-lg p-4 hover:border-primary-400 transition-colors cursor-pointer">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Database className="w-6 h-6 text-blue-600" />
                        <div>
                          <h4 className="font-medium text-neutral-900">PostgreSQL Production</h4>
                          <p className="text-sm text-neutral-600">Primary production database with stored procedures</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                            <span className="text-xs text-success-600">Connected</span>
                            <span className="text-xs text-neutral-500">• 8 active connections</span>
                          </div>
                        </div>
                      </div>
                      <input type="radio" name="source_connector" value="postgresql" className="w-4 h-4 text-primary-600" />
                    </div>
                  </div> */}

                  {/* Manage Connectors Link */}
                  <div className="text-center pt-2">
                    <a href="/connectors" className="text-sm text-primary-600 hover:text-primary-700 underline">
                      See All Repositories
                    </a>
                  </div>
                </div>
              </div>
            </div>

            {/* Migration Configuration */}
            <div className="mt-8 pt-8 border-t border-neutral-200">
              <h3 className="font-medium text-neutral-900 mb-4">Migration Configuration</h3>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Target Technology
                  </label>
                  <select className="input w-full">
                    <option>Databricks</option>
                    <option>Snowflake</option>
                    <option>Confluent Cloud</option>
                    <option>AWS Glue</option>
                    <option>Azure Data Factory</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Target Branch for PR
                  </label>
                  <input 
                    type="text" 
                    placeholder="main"
                    className="input w-full"
                  />
                </div>
              </div>
              
              {/* Branch Selection */}
              <div className="grid md:grid-cols-2 gap-6 mt-4">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Source Branch
                  </label>
                  <select className="input w-full">
                    <option>main</option>
                    <option>develop</option>
                    <option>feature/data-pipeline</option>
                    <option>legacy/etl-scripts</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Migration Branch Name
                  </label>
                  <input 
                    type="text" 
                    placeholder="feat/migrate-to-databricks"
                    className="input w-full"
                  />
                </div>
              </div>
              
              <div className="mt-4">
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  Project Name
                </label>
                <input 
                  type="text" 
                  placeholder="Enter a descriptive name for your migration project"
                  className="input w-full"
                />
              </div>
              
              <div className="mt-6 flex justify-end space-x-3">
                <button className="btn-secondary">Save as Draft</button>
                <button className="btn-primary">Start Analysis</button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
