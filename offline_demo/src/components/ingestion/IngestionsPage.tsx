'use client'
import { useState, useMemo } from 'react'
import { Plus, Search, Play, Pause, RotateCcw, Calendar, Clock, CheckCircle, AlertCircle, Activity, Settings, Filter } from 'lucide-react'
import { formatDate, cn } from '@/lib/utils'

interface Ingestion {
  id: string
  name: string
  source: string
  destination: string
  status: 'running' | 'scheduled' | 'paused' | 'error' | 'completed'
  lastRun: string
  nextRun: string
  recordsProcessed: number
  frequency: string
  progress: number
  duration: number
}

export default function IngestionsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'running' | 'scheduled' | 'paused' | 'error' | 'completed'>('all')

  // Mock ingestion data
  const allIngestions: Ingestion[] = [
    {
      id: 'ing-1',
      name: 'Customer Data Pipeline',
      source: 'PostgreSQL Production',
      destination: 'Databricks Lakehouse',
      status: 'running',
      lastRun: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
      nextRun: new Date(Date.now() + 1000 * 60 * 60 * 2).toISOString(),
      recordsProcessed: 125000,
      frequency: 'Every 6 hours',
      progress: 75,
      duration: 1800
    },
    {
      id: 'ing-2',
      name: 'Sales Analytics ETL',
      source: 'Salesforce CRM',
      destination: 'Snowflake Warehouse',
      status: 'completed',
      lastRun: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),
      nextRun: new Date(Date.now() + 1000 * 60 * 60 * 20).toISOString(),
      recordsProcessed: 45000,
      frequency: 'Daily at 2:00 AM',
      progress: 100,
      duration: 900
    },
    {
      id: 'ing-3',
      name: 'Product Catalog Sync',
      source: 'MongoDB Atlas',
      destination: 'BigQuery',
      status: 'error',
      lastRun: new Date(Date.now() - 1000 * 60 * 60 * 8).toISOString(),
      nextRun: new Date(Date.now() + 1000 * 60 * 60 * 16).toISOString(),
      recordsProcessed: 0,
      frequency: 'Every 12 hours',
      progress: 0,
      duration: 0
    },
    {
      id: 'ing-4',
      name: 'User Activity Stream',
      source: 'Kafka Topic',
      destination: 'ClickHouse',
      status: 'paused',
      lastRun: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
      nextRun: new Date(Date.now() + 1000 * 60 * 60 * 1).toISOString(),
      recordsProcessed: 2500000,
      frequency: 'Real-time',
      progress: 0,
      duration: 0
    },
    {
      id: 'ing-5',
      name: 'Financial Reports',
      source: 'SAP ERP',
      destination: 'Databricks',
      status: 'scheduled',
      lastRun: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(),
      nextRun: new Date(Date.now() + 1000 * 60 * 60 * 4).toISOString(),
      recordsProcessed: 8500,
      frequency: 'Weekly',
      progress: 0,
      duration: 0
    }
  ]

  const ingestions = useMemo(() => {
    return allIngestions.filter((ingestion) => {
      const matchesSearch = ingestion.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ingestion.source.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ingestion.destination.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesStatus = statusFilter === 'all' || ingestion.status === statusFilter
      
      return matchesSearch && matchesStatus
    })
  }, [searchQuery, statusFilter])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Activity className="w-4 h-4 text-primary-500 animate-pulse" />
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-success-500" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-error-500" />
      case 'paused':
        return <Pause className="w-4 h-4 text-warning-500" />
      default:
        return <Clock className="w-4 h-4 text-neutral-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-primary-100 text-primary-700 border-primary-200'
      case 'completed':
        return 'bg-success-100 text-success-700 border-success-200'
      case 'error':
        return 'bg-error-100 text-error-700 border-error-200'
      case 'paused':
        return 'bg-warning-100 text-warning-700 border-warning-200'
      default:
        return 'bg-neutral-100 text-neutral-700 border-neutral-200'
    }
  }

  const formatDuration = (seconds: number) => {
    if (seconds === 0) return 'N/A'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-neutral-900">Data Ingestions</h1>
          <p className="text-neutral-600 mt-1">Monitor and manage your data pipeline executions</p>
        </div>
        <button className="btn-primary flex items-center space-x-2">
          <Plus className="w-4 h-4" />
          <span>Create Pipeline</span>
        </button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search pipelines..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="input pl-10 w-full"
          />
        </div>

        {/* Status Filter */}
        <select 
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value as any)}
          className="input w-auto"
        >
          <option value="all">All Status</option>
          <option value="running">Running</option>
          <option value="completed">Completed</option>
          <option value="scheduled">Scheduled</option>
          <option value="paused">Paused</option>
          <option value="error">Error</option>
        </select>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div className="card p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-neutral-100 rounded-lg flex items-center justify-center">
              <Activity className="w-5 h-5 text-neutral-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-neutral-900">5</p>
              <p className="text-sm text-neutral-600">Total Pipelines</p>
            </div>
          </div>
        </div>
        
        <div className="card p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
              <Activity className="w-5 h-5 text-primary-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-neutral-900">1</p>
              <p className="text-sm text-neutral-600">Running</p>
            </div>
          </div>
        </div>
        
        <div className="card p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-success-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-success-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-neutral-900">1</p>
              <p className="text-sm text-neutral-600">Completed</p>
            </div>
          </div>
        </div>
        
        <div className="card p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-warning-100 rounded-lg flex items-center justify-center">
              <Pause className="w-5 h-5 text-warning-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-neutral-900">1</p>
              <p className="text-sm text-neutral-600">Paused</p>
            </div>
          </div>
        </div>
        
        <div className="card p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-error-100 rounded-lg flex items-center justify-center">
              <AlertCircle className="w-5 h-5 text-error-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-neutral-900">1</p>
              <p className="text-sm text-neutral-600">Errors</p>
            </div>
          </div>
        </div>
      </div>

      {/* Ingestions List */}
      <div className="space-y-4">
        {ingestions.map((ingestion, index) => (
          <div 
            key={ingestion.id} 
            className="card p-6 hover:shadow-lg transition-all duration-200"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                {getStatusIcon(ingestion.status)}
                <div>
                  <h3 className="font-semibold text-neutral-900">{ingestion.name}</h3>
                  <p className="text-sm text-neutral-600">
                    {ingestion.source} → {ingestion.destination}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className={cn(
                  'text-xs px-2 py-1 rounded-full border font-medium',
                  getStatusColor(ingestion.status)
                )}>
                  {ingestion.status}
                </span>
                <button className="text-neutral-400 hover:text-neutral-600">
                  <Settings className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Progress Bar for Running Ingestions */}
            {ingestion.status === 'running' && (
              <div className="mb-4">
                <div className="flex items-center justify-between text-sm mb-2">
                  <span className="text-neutral-600">Progress</span>
                  <span className="text-neutral-900">{ingestion.progress}%</span>
                </div>
                <div className="w-full bg-neutral-200 rounded-full h-2">
                  <div 
                    className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${ingestion.progress}%` }}
                  />
                </div>
              </div>
            )}

            {/* Stats Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="text-neutral-600">Records Processed</p>
                <p className="font-medium text-neutral-900">{formatNumber(ingestion.recordsProcessed)}</p>
              </div>
              <div>
                <p className="text-neutral-600">Frequency</p>
                <p className="font-medium text-neutral-900">{ingestion.frequency}</p>
              </div>
              <div>
                <p className="text-neutral-600">Last Run</p>
                <p className="font-medium text-neutral-900">{formatDate(ingestion.lastRun, 'relative')}</p>
              </div>
              <div>
                <p className="text-neutral-600">Duration</p>
                <p className="font-medium text-neutral-900">{formatDuration(ingestion.duration)}</p>
              </div>
            </div>

            {/* Next Run */}
            <div className="mt-4 pt-4 border-t border-neutral-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 text-sm">
                  <Calendar className="w-4 h-4 text-neutral-400" />
                  <span className="text-neutral-600">Next run:</span>
                  <span className="text-neutral-900">{formatDate(ingestion.nextRun, 'relative')}</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  {ingestion.status === 'paused' && (
                    <button className="text-success-600 hover:text-success-700 text-sm font-medium">
                      <Play className="w-4 h-4" />
                    </button>
                  )}
                  {ingestion.status === 'running' && (
                    <button className="text-warning-600 hover:text-warning-700 text-sm font-medium">
                      <Pause className="w-4 h-4" />
                    </button>
                  )}
                  {ingestion.status === 'error' && (
                    <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                      <RotateCcw className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {ingestions.length === 0 && (
        <div className="text-center py-12">
          <Activity className="w-12 h-12 text-neutral-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-neutral-900 mb-2">No pipelines found</h3>
          <p className="text-neutral-600 mb-6">Try adjusting your search or filter criteria</p>
          <button className="btn-primary">Create Your First Pipeline</button>
        </div>
      )}
    </div>
  )
}
