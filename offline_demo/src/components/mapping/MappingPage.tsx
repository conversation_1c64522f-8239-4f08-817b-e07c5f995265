'use client'
import { useState, useEffect } from 'react'
import { 
  GitBranch, 
  Plus, 
  Search, 
  Filter, 
  ArrowRight, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Zap,
  Database,
  Table,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  AlertTriangle,
  ThumbsUp,
  ThumbsDown,
  Brain,
  MapPin,
  Link2,
  Target,
  FileText,
  TrendingUp
} from 'lucide-react'
import { DataMapping, MappingRule, AISuggestion } from '@/types/api'
import { mockDataService } from '@/lib/mockData'
import { cn } from '@/lib/utils'
import CreateMappingModal from './CreateMappingModal'

export default function MappingPage() {
  const [mappings, setMappings] = useState<DataMapping[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [selectedMapping, setSelectedMapping] = useState<DataMapping | null>(null)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showMappingDetail, setShowMappingDetail] = useState(false)
  const [processingAI, setProcessingAI] = useState(false)

  useEffect(() => {
    loadMappings()
  }, [])

  const loadMappings = async () => {
    try {
      setLoading(true)
      const mappingsData = await mockDataService.getDataMappings()
      setMappings(mappingsData)
    } catch (error) {
      console.error('Error loading mappings:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateAISuggestions = async (mappingId: string) => {
    try {
      setProcessingAI(true)
      const updatedMapping = await mockDataService.generateMappingSuggestions(mappingId)
      setMappings(prev => prev.map(m => m.id === mappingId ? updatedMapping : m))
    } catch (error) {
      console.error('Error generating suggestions:', error)
    } finally {
      setProcessingAI(false)
    }
  }

  const filteredMappings = mappings.filter(mapping => {
    const matchesSearch = mapping.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      mapping.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = filterStatus === 'all' || mapping.validation_status === filterStatus
    
    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'valid':
        return 'bg-success-100 text-success-800 border-success-200'
      case 'invalid':
        return 'bg-error-100 text-error-800 border-error-200'
      case 'pending':
        return 'bg-warning-100 text-warning-800 border-warning-200'
      default:
        return 'bg-neutral-100 text-neutral-700 border-neutral-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'valid':
        return <CheckCircle className="w-4 h-4 text-success-600" />
      case 'invalid':
        return <XCircle className="w-4 h-4 text-error-600" />
      case 'pending':
        return <Clock className="w-4 h-4 text-warning-600" />
      default:
        return <Clock className="w-4 h-4 text-neutral-600" />
    }
  }

  const getRuleStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-success-600 bg-success-50'
      case 'suggested':
        return 'text-warning-600 bg-warning-50'
      case 'rejected':
        return 'text-error-600 bg-error-50'
      case 'manual':
        return 'text-info-600 bg-info-50'
      default:
        return 'text-neutral-600 bg-neutral-50'
    }
  }

  const stats = {
    total: mappings.length,
    valid: mappings.filter(m => m.validation_status === 'valid').length,
    pending: mappings.filter(m => m.validation_status === 'pending').length,
    invalid: mappings.filter(m => m.validation_status === 'invalid').length,
    avgCompletion: mappings.length > 0 
      ? Math.round(mappings.reduce((acc, m) => acc + m.completion_percentage, 0) / mappings.length)
      : 0
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-neutral-200 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-neutral-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-48 bg-neutral-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6 relative">
      {/* Modern background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-secondary-50/30 via-transparent to-primary-50/20 pointer-events-none" />
      
      {/* Header */}
      <div className="flex items-center justify-between relative z-10">
        <div>
          <h1 className="text-3xl font-bold text-gradient-primary">Data Model Mapping</h1>
          <p className="text-neutral-600 mt-2">Automated mapping to industry-standard data models</p>
        </div>
        <div className="flex items-center space-x-3">
          <button 
            onClick={() => setShowCreateModal(true)}
            className="btn-gradient flex items-center space-x-2 hover-lift"
          >
            <Plus className="w-4 h-4" />
            <span>New Mapping</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 relative z-10">
        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">Total Mappings</p>
              <p className="text-2xl font-bold text-neutral-900">{stats.total}</p>
            </div>
            <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
              <GitBranch className="w-6 h-6 text-primary-600" />
            </div>
          </div>
        </div>

        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">Valid</p>
              <p className="text-2xl font-bold text-success-600">{stats.valid}</p>
            </div>
            <div className="w-12 h-12 bg-success-100 rounded-xl flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-success-600" />
            </div>
          </div>
        </div>

        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">Pending</p>
              <p className="text-2xl font-bold text-warning-600">{stats.pending}</p>
            </div>
            <div className="w-12 h-12 bg-warning-100 rounded-xl flex items-center justify-center">
              <Clock className="w-6 h-6 text-warning-600" />
            </div>
          </div>
        </div>

        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">Avg Completion</p>
              <p className="text-2xl font-bold text-primary-600">{stats.avgCompletion}%</p>
            </div>
            <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-primary-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 relative z-10">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search mappings..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="input pl-10 w-full"
          />
        </div>

        {/* Status Filter */}
        <select 
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="input w-auto"
        >
          <option value="all">All Status</option>
          <option value="valid">Valid</option>
          <option value="pending">Pending</option>
          <option value="invalid">Invalid</option>
        </select>
      </div>

      {/* Mappings Grid */}
      <div className="space-y-6 relative z-10">
        {filteredMappings.map((mapping, index) => (
          <div 
            key={mapping.id}
            className="card-gradient p-6 hover-lift group relative overflow-hidden"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            {/* Header */}
            <div className="flex items-start justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-xl flex items-center justify-center">
                  <GitBranch className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-neutral-900 mb-1">{mapping.name}</h3>
                  <p className="text-neutral-600 text-sm">{mapping.description}</p>
                  <div className="flex items-center space-x-3 mt-2">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(mapping.validation_status)}
                      <span className={cn("badge text-xs", getStatusColor(mapping.validation_status))}>
                        {mapping.validation_status}
                      </span>
                    </div>
                    <span className="text-xs text-neutral-500">
                      {mapping.completion_percentage}% complete
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <button 
                  onClick={() => generateAISuggestions(mapping.id)}
                  disabled={processingAI}
                  className="btn-ghost flex items-center space-x-2 text-sm"
                >
                  <Brain className={cn("w-4 h-4", processingAI && "animate-spin")} />
                  <span>AI Suggest</span>
                </button>
                <button 
                  onClick={() => {
                    setSelectedMapping(mapping)
                    setShowMappingDetail(true)
                  }}
                  className="btn-primary-sm"
                >
                  <Eye className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Mapping Flow */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
              {/* Source */}
              <div className="bg-neutral-50 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Database className="w-4 h-4 text-neutral-600" />
                  <span className="text-sm font-medium text-neutral-700">Source Schema</span>
                </div>
                <div className="bg-white rounded border p-3">
                  <p className="font-medium text-neutral-900">{mapping.source_schema}</p>
                  <p className="text-xs text-neutral-500 mt-1">
                    {mapping.mapping_rules.length} fields
                  </p>
                </div>
              </div>

              {/* Arrow */}
              <div className="flex items-center justify-center">
                <div className="flex items-center space-x-2">
                  <ArrowRight className="w-5 h-5 text-neutral-400" />
                  <span className="text-xs text-neutral-500">Mapping</span>
                  <ArrowRight className="w-5 h-5 text-neutral-400" />
                </div>
              </div>

              {/* Target */}
              <div className="bg-primary-50 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Target className="w-4 h-4 text-primary-600" />
                  <span className="text-sm font-medium text-primary-700">Target Model</span>
                </div>
                <div className="bg-white rounded border p-3">
                  <p className="font-medium text-neutral-900">{mapping.target_model}</p>
                  <p className="text-xs text-neutral-500 mt-1">
                    Industry standard
                  </p>
                </div>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex items-center justify-between text-sm mb-2">
                <span className="text-neutral-700">Mapping Progress</span>
                <span className="text-neutral-900 font-medium">{mapping.completion_percentage}%</span>
              </div>
              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ width: `${mapping.completion_percentage}%` }}
                />
              </div>
            </div>

            {/* Mapping Rules Preview */}
            <div className="space-y-3 mb-6">
              <h4 className="text-sm font-medium text-neutral-900">Mapping Rules</h4>
              <div className="space-y-2">
                {mapping.mapping_rules.slice(0, 3).map((rule) => (
                  <div key={rule.id} className="flex items-center justify-between bg-neutral-50 rounded-lg p-3">
                    <div className="flex items-center space-x-3">
                      <div className="text-sm">
                        <span className="font-mono text-neutral-700">{rule.source_field}</span>
                        <ArrowRight className="w-3 h-3 text-neutral-400 mx-2 inline" />
                        <span className="font-mono text-neutral-900">{rule.target_field}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={cn(
                        "px-2 py-1 text-xs rounded-full",
                        getRuleStatusColor(rule.status)
                      )}>
                        {rule.status}
                      </span>
                      <span className="text-xs text-neutral-500">
                        {Math.round(rule.confidence_score * 100)}%
                      </span>
                    </div>
                  </div>
                ))}
                {mapping.mapping_rules.length > 3 && (
                  <div className="text-center">
                    <span className="text-xs text-neutral-500">
                      +{mapping.mapping_rules.length - 3} more rules
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* AI Suggestions */}
            {mapping.ai_suggestions.length > 0 && (
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Brain className="w-4 h-4 text-primary-600" />
                  <h4 className="text-sm font-medium text-neutral-900">AI Suggestions</h4>
                </div>
                <div className="space-y-2">
                  {mapping.ai_suggestions.slice(0, 2).map((suggestion) => (
                    <div key={suggestion.id} className="bg-primary-50 border border-primary-200 rounded-lg p-3">
                      <div className="flex items-start justify-between mb-2">
                        <p className="text-sm text-neutral-700">{suggestion.suggestion}</p>
                        <span className="text-xs text-primary-600 font-medium">
                          {Math.round(suggestion.confidence * 100)}%
                        </span>
                      </div>
                      <p className="text-xs text-neutral-600 mb-3">{suggestion.reasoning}</p>
                      <div className="flex items-center space-x-2">
                        <button className="btn-ghost text-xs py-1 px-2">
                          <ThumbsUp className="w-3 h-3 mr-1" />
                          Accept
                        </button>
                        <button className="btn-ghost text-xs py-1 px-2">
                          <ThumbsDown className="w-3 h-3 mr-1" />
                          Reject
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Footer */}
            <div className="flex items-center justify-between text-xs text-neutral-500 mt-6 pt-4 border-t border-neutral-200">
              <span>Updated {new Date(mapping.updated_at).toLocaleDateString()}</span>
              <span>{mapping.mapping_rules.filter(r => r.status === 'approved').length} approved rules</span>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredMappings.length === 0 && (
        <div className="text-center py-12 relative z-10">
          <div className="w-24 h-24 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <GitBranch className="w-12 h-12 text-neutral-400" />
          </div>
          <h3 className="text-lg font-medium text-neutral-900 mb-2">No mappings found</h3>
          <p className="text-neutral-600 mb-6">
            {searchQuery || filterStatus !== 'all' 
              ? 'Try adjusting your search or filters'
              : 'Create your first data mapping to get started'
            }
          </p>
          <button 
            onClick={() => setShowCreateModal(true)}
            className="btn-gradient"
          >
            Create Mapping
          </button>
        </div>
      )}
      
      {/* Create Mapping Modal */}
      <CreateMappingModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={(newMapping) => {
          setMappings(prev => [...prev, newMapping])
          setShowCreateModal(false)
        }}
      />
    </div>
  )
}
