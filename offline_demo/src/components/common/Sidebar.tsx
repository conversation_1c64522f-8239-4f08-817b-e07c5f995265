'use client'
import { useAppStore } from '@/store/useAppStore'
import { 
  Home, 
  Database, 
  Cable, 
  RefreshCw, 
  Target, 
  Map, 
  Settings,
  Menu,
  ChevronDown,
  ChevronRight,
  Download,
  Upload,
  Zap,
  Activity
} from 'lucide-react'
import { cn } from '@/lib/utils'

const navigationItems = [
  { id: 'dashboard', label: 'Dashboard', icon: Home },
  { 
    id: 'connectors', 
    label: 'Connectors', 
    icon: Database, 
    hasSubmenu: true,
    subItems: [
      { id: 'sources', label: 'Sources', icon: Download },
      { id: 'destinations', label: 'Destinations', icon: Upload }
    ]
  },
  { id: 'ingestions', label: 'Ingestions', icon: Cable },
  { id: 'migration', label: 'Pipeline Migration', icon: RefreshCw },
  { id: 'objectives', label: 'Business Objectives', icon: Target },
  { id: 'mapping', label: 'Data Model Mapping', icon: Map },
  { id: 'settings', label: 'Settings', icon: Settings }
]

export default function Sidebar() {
  const { 
    sidebarCollapsed, 
    activeTab, 
    connectorsExpanded,
    toggleSidebar, 
    setActiveTab, 
    setConnectorsExpanded 
  } = useAppStore()

  const handleNavClick = (itemId: string) => {
    if (itemId === 'connectors') {
      setConnectorsExpanded(!connectorsExpanded)
      if (!connectorsExpanded) {
        setActiveTab('connectors')
      }
    } else {
      setActiveTab(itemId)
      setConnectorsExpanded(false)
    }
  }

  return (
    <div className={cn(
      'bg-white shadow-2xl transition-all duration-300 border-r border-neutral-200/80 flex flex-col',
      'backdrop-blur-xl bg-white/95',
      sidebarCollapsed ? 'w-16' : 'w-72'
    )}>
      {/* Header */}
      <div className="p-4">
        <div className="flex items-center justify-between">
          {!sidebarCollapsed && (
            <div className="flex items-center space-x-3 animate-fade-in-right">
              <div className="relative">
                <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Activity className="w-5 h-5 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-accent-400 rounded-full animate-pulse"></div>
              </div>
              <div>
                <span className="font-bold text-xl text-gradient-primary">Ironbook AI</span>
                <div className="text-xs text-neutral-500 font-medium">AI-Driven Migration Platform</div>
              </div>
            </div>
          )}
          <button 
            onClick={toggleSidebar}
            className={cn(
              "p-2 rounded-xl hover:bg-neutral-100 transition-all duration-200",
              "hover:scale-105 active:scale-95",
              sidebarCollapsed && "mx-auto"
            )}
          >
            <Menu className="w-5 h-5 text-neutral-600" />
          </button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="mt-2 px-3 flex-1 overflow-y-auto scrollbar-none">
        <div className="space-y-1">
          {navigationItems.map((item, index) => {
            const Icon = item.icon
            const isActive = activeTab === item.id || (item.id === 'connectors' && connectorsExpanded)
            
            return (
              <div key={item.id} className="relative">
                <button
                  onClick={() => handleNavClick(item.id)}
                  className={cn(
                    "w-full flex items-center px-3 py-3 rounded-xl transition-all duration-200 group",
                    "relative overflow-hidden",
                    isActive 
                      ? "bg-gradient-to-r from-primary-50 to-primary-100 text-primary-700 shadow-sm border border-primary-200/50" 
                      : "text-neutral-600 hover:bg-neutral-50 hover:text-neutral-900"
                  )}
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  {/* Active indicator */}
                  {isActive && (
                    <div className="absolute left-0 top-0 h-full w-1 bg-gradient-to-b from-primary-500 to-primary-600 rounded-r-full" />
                  )}
                  
                  <div className={cn(
                    "flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200",
                    isActive ? "bg-primary-200/50" : "group-hover:bg-neutral-200/50"
                  )}>
                    <Icon className="w-5 h-5" />
                  </div>
                  
                  {!sidebarCollapsed && (
                    <>
                      <span className="ml-3 text-sm font-medium flex-1 text-left">
                        {item.label}
                      </span>
                      {item.hasSubmenu && (
                        <div className={cn(
                          "transition-transform duration-200",
                          connectorsExpanded ? "rotate-180" : ""
                        )}>
                          <ChevronDown className="w-4 h-4" />
                        </div>
                      )}
                    </>
                  )}

                  {/* Tooltip for collapsed state */}
                  {sidebarCollapsed && (
                    <div className="absolute left-16 bg-neutral-900 text-white text-xs px-2 py-1 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                      {item.label}
                    </div>
                  )}
                </button>
                
                {/* Submenu for Connectors */}
                {item.id === 'connectors' && connectorsExpanded && !sidebarCollapsed && (
                  <div className="ml-8 mt-2 space-y-1 animate-fade-in-down">
                    {item.subItems?.map((subItem) => {
                      const SubIcon = subItem.icon
                      return (
                        <button
                          key={subItem.id}
                          onClick={() => setActiveTab('connectors')}
                          className="w-full flex items-center px-3 py-2 rounded-lg text-sm transition-all duration-200 text-neutral-600 hover:bg-neutral-50 hover:text-neutral-900 group"
                        >
                          <div className="flex items-center justify-center w-6 h-6 rounded-md group-hover:bg-neutral-200/50 transition-all duration-200">
                            <SubIcon className="w-4 h-4" />
                          </div>
                          <span className="ml-2 font-medium">{subItem.label}</span>
                        </button>
                      )
                    })}
                  </div>
                )}
              </div>
            )
          })}
        </div>
      </nav>

      {/* Footer */}
      {!sidebarCollapsed && (
        <div className="p-4 border-t border-neutral-200/50">
          <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-neutral-50 to-neutral-100 rounded-xl">
            <div className="w-8 h-8 bg-gradient-to-br from-accent-400 to-accent-500 rounded-lg flex items-center justify-center">
              <Zap className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1">
              <div className="text-xs font-semibold text-neutral-700">Demo Mode</div>
              <div className="text-xs text-neutral-500">Offline simulation</div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
