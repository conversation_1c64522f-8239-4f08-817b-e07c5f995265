'use client'
import { useState, useMemo } from 'react'
import { Plus, Search, Filter, Database, Check, X, AlertCircle, Settings, Github, GitBranch } from 'lucide-react'
import { mockDataService } from '@/lib/mockData'
import { cn } from '@/lib/utils'

interface Connector {
  id: string
  name: string
  type: 'source' | 'destination'
  status: 'connected' | 'disconnected' | 'error'
  connectionCount: number
  lastConnected: string
  description: string
  icon: string
  category: string
}

export default function ConnectorsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'source' | 'destination'>('all')
  const [filterStatus, setFilterStatus] = useState<'all' | 'connected' | 'disconnected' | 'error'>('all')

  // Mock connectors data - Enhanced with GitHub/GitLab and better categorization
  const allConnectors = [
    // Source Connectors
    {
      id: 'conn-1',
      name: 'PostgreSQL Production',
      type: 'source' as const,
      status: 'connected' as const,
      connectionCount: 8,
      lastConnected: '2 minutes ago',
      description: 'Primary production database with customer and order data',
      icon: 'database',
      category: 'database'
    },
    {
      id: 'conn-2',
      name: 'GitHub Repository',
      type: 'source' as const,
      status: 'connected' as const,
      connectionCount: 5,
      lastConnected: '10 minutes ago',
      description: 'Source code repository for pipeline migration and analysis',
      icon: 'github',
      category: 'repository'
    },
    {
      id: 'conn-3',
      name: 'GitLab Enterprise',
      type: 'source' as const,
      status: 'connected' as const,
      connectionCount: 3,
      lastConnected: '1 hour ago',
      description: 'Enterprise GitLab instance for DevOps and code management',
      icon: 'gitlab',
      category: 'repository'
    },
    {
      id: 'conn-4',
      name: 'Salesforce CRM',
      type: 'source' as const,
      status: 'error' as const,
      connectionCount: 3,
      lastConnected: '2 hours ago',
      description: 'Customer relationship management data',
      icon: 'cloud',
      category: 'crm'
    },
    {
      id: 'conn-5',
      name: 'MySQL Analytics',
      type: 'source' as const,
      status: 'connected' as const,
      connectionCount: 4,
      lastConnected: '30 minutes ago',
      description: 'Analytics database with business intelligence data',
      icon: 'database',
      category: 'database'
    },
    // Destination Connectors - Focus on Lakehouses
    {
      id: 'conn-6',
      name: 'Databricks Lakehouse',
      type: 'destination' as const,
      status: 'connected' as const,
      connectionCount: 12,
      lastConnected: '5 minutes ago',
      description: 'Unified analytics platform for data science and machine learning',
      icon: 'database',
      category: 'lakehouse'
    },
    {
      id: 'conn-7',
      name: 'Snowflake Data Cloud',
      type: 'destination' as const,
      status: 'connected' as const,
      connectionCount: 8,
      lastConnected: '15 minutes ago',
      description: 'Cloud data platform with lakehouse capabilities',
      icon: 'snowflake',
      category: 'lakehouse'
    },
    {
      id: 'conn-8',
      name: 'Azure Synapse Analytics',
      type: 'destination' as const,
      status: 'disconnected' as const,
      connectionCount: 0,
      lastConnected: '1 day ago',
      description: 'Enterprise data warehouse and analytics service',
      icon: 'cloud',
      category: 'lakehouse'
    },
    {
      id: 'conn-9',
      name: 'BigQuery',
      type: 'destination' as const,
      status: 'connected' as const,
      connectionCount: 6,
      lastConnected: '20 minutes ago',
      description: 'Serverless data warehouse for analytics and AI',
      icon: 'database',
      category: 'lakehouse'
    }
  ]

  const connectors = useMemo(() => {
    return allConnectors.filter((connector: Connector) => {
      const matchesSearch = connector.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        connector.description.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesType = filterType === 'all' || connector.type === filterType
      const matchesStatus = filterStatus === 'all' || connector.status === filterStatus
      
      return matchesSearch && matchesType && matchesStatus
    })
  }, [searchQuery, filterType, filterStatus])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <Check className="w-4 h-4 text-success-500" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-error-500" />
      default:
        return <X className="w-4 h-4 text-neutral-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'bg-success-100 text-success-700 border-success-200'
      case 'error':
        return 'bg-error-100 text-error-700 border-error-200'
      default:
        return 'bg-neutral-100 text-neutral-700 border-neutral-200'
    }
  }

  const sourceConnectors = allConnectors.filter(c => c.type === 'source')
  const destinationConnectors = allConnectors.filter(c => c.type === 'destination')
  const connectedCount = allConnectors.filter(c => c.status === 'connected').length
  const disconnectedCount = allConnectors.filter(c => c.status === 'disconnected').length
  const errorCount = allConnectors.filter(c => c.status === 'error').length

  const getConnectorIcon = (icon: string, category: string) => {
    switch (icon) {
      case 'github':
        return <Github className="w-6 h-6 text-white" />
      case 'gitlab':
        return <GitBranch className="w-6 h-6 text-white" />
      default:
        return <Database className="w-6 h-6 text-white" />
    }
  }

  const getConnectorColor = (type: string, category: string) => {
    if (type === 'source') {
      if (category === 'repository') {
        return 'from-neutral-800 to-neutral-900'
      }
      return 'from-primary-500 to-primary-600'
    }
    return 'from-secondary-500 to-secondary-600'
  }

  return (
    <div className="p-6 space-y-6 relative">
      {/* Modern background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-50/30 via-transparent to-secondary-50/20 pointer-events-none" />
      
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between relative z-10">
        <div>
          <h1 className="text-3xl font-bold text-gradient-primary">Data Connectors</h1>
          <p className="text-neutral-600 mt-2">Connect your data sources and destination lakehouses</p>
          
          {/* Quick Stats */}
          <div className="flex items-center space-x-6 mt-3">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
              <span className="text-sm text-neutral-600">{sourceConnectors.length} Sources</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-secondary-500 rounded-full"></div>
              <span className="text-sm text-neutral-600">{destinationConnectors.length} Destinations</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-success-500 rounded-full"></div>
              <span className="text-sm text-neutral-600">{connectedCount} Connected</span>
            </div>
          </div>
        </div>
        <button className="btn-gradient flex items-center space-x-2 hover-lift mt-4 sm:mt-0">
          <Plus className="w-4 h-4" />
          <span>Add Connector</span>
        </button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search connectors..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="input pl-10 w-full"
          />
        </div>

        {/* Type Filter */}
        <select 
          value={filterType}
          onChange={(e) => setFilterType(e.target.value as any)}
          className="input w-auto"
        >
          <option value="all">All Types</option>
          <option value="source">Sources</option>
          <option value="destination">Destinations</option>
        </select>

        {/* Status Filter */}
        <select 
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value as any)}
          className="input w-auto"
        >
          <option value="all">All Status</option>
          <option value="connected">Connected</option>
          <option value="disconnected">Disconnected</option>
          <option value="error">Error</option>
        </select>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 relative z-10">
        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">Total Connectors</p>
              <p className="text-2xl font-bold text-neutral-900">{allConnectors.length}</p>
            </div>
            <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
              <Database className="w-6 h-6 text-primary-600" />
            </div>
          </div>
        </div>
        
        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">Connected</p>
              <p className="text-2xl font-bold text-success-600">{connectedCount}</p>
            </div>
            <div className="w-12 h-12 bg-success-100 rounded-xl flex items-center justify-center">
              <Check className="w-6 h-6 text-success-600" />
            </div>
          </div>
        </div>
        
        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">Source Connectors</p>
              <p className="text-2xl font-bold text-primary-600">{sourceConnectors.length}</p>
            </div>
            <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
              <div className="w-6 h-6 bg-primary-600 rounded flex items-center justify-center text-white text-xs font-bold">S</div>
            </div>
          </div>
        </div>
        
        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">Lakehouses</p>
              <p className="text-2xl font-bold text-secondary-600">{destinationConnectors.length}</p>
            </div>
            <div className="w-12 h-12 bg-secondary-100 rounded-xl flex items-center justify-center">
              <div className="w-6 h-6 bg-secondary-600 rounded flex items-center justify-center text-white text-xs font-bold">L</div>
            </div>
          </div>
        </div>
      </div>

      {/* Connectors Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {connectors.map((connector, index) => (
          <div 
            key={connector.id} 
            className="card-gradient p-6 hover-lift group cursor-pointer relative overflow-hidden"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            {/* Status indicator */}
            <div className="absolute top-4 right-4">
              <div className={cn(
                "status-dot-pulse",
                connector.status === 'connected' && "status-connected",
                connector.status === 'error' && "status-error",
                connector.status === 'disconnected' && "bg-neutral-400"
              )} />
            </div>
            
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className={cn(
                  "w-12 h-12 bg-gradient-to-br rounded-lg flex items-center justify-center hover-glow group-hover:scale-110 transition-all duration-300",
                  getConnectorColor(connector.type, connector.category)
                )}>
                  {getConnectorIcon(connector.icon, connector.category)}
                </div>
                <div>
                  <h3 className="font-semibold text-neutral-900">{connector.name}</h3>
                  <div className="flex items-center space-x-2">
                    <span className={cn(
                      "text-xs px-2 py-1 rounded-full font-medium",
                      connector.type === 'source' 
                        ? "bg-primary-100 text-primary-700" 
                        : "bg-secondary-100 text-secondary-700"
                    )}>
                      {connector.type}
                    </span>
                    <span className="text-xs text-neutral-500 capitalize">{connector.category}</span>
                  </div>
                </div>
              </div>
              <button className="text-neutral-400 hover:text-neutral-600">
                <Settings className="w-4 h-4" />
              </button>
            </div>

            <p className="text-sm text-neutral-600 mb-4">{connector.description}</p>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getStatusIcon(connector.status)}
                <span className={cn(
                  'text-xs px-2 py-1 rounded-full border font-medium',
                  getStatusColor(connector.status)
                )}>
                  {connector.status}
                </span>
              </div>
              
              <div className="text-right">
                <p className="text-sm font-medium text-neutral-900">{connector.connectionCount}</p>
                <p className="text-xs text-neutral-500">connections</p>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-neutral-100">
              <div className="flex items-center justify-between text-sm">
                <span className="text-neutral-600">Last connected:</span>
                <span className="text-neutral-900">{connector.lastConnected}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {connectors.length === 0 && (
        <div className="text-center py-12">
          <Database className="w-12 h-12 text-neutral-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-neutral-900 mb-2">No connectors found</h3>
          <p className="text-neutral-600 mb-6">Try adjusting your search or filter criteria</p>
          <button className="btn-primary">Add Your First Connector</button>
        </div>
      )}
    </div>
  )
}
