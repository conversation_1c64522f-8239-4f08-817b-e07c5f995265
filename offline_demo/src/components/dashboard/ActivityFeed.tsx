'use client'
import { Clock, CheckCircle, AlertCircle, Users, Database } from 'lucide-react'
import { formatDate } from '@/lib/utils'

export default function ActivityFeed() {
  const activities = [
    {
      id: '1',
      type: 'connection',
      title: 'PostgreSQL connection established',
      description: 'Successfully connected to production database',
      timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 minutes ago
      icon: Database,
      color: 'text-success-500'
    },
    {
      id: '2',
      type: 'job',
      title: 'Data ingestion completed',
      description: '2.3M records processed successfully',
      timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
      icon: CheckCircle,
      color: 'text-success-500'
    },
    {
      id: '3',
      type: 'alert',
      title: 'High latency detected',
      description: 'API response time increased to 850ms',
      timestamp: new Date(Date.now() - 1000 * 60 * 32).toISOString(), // 32 minutes ago
      icon: AlertCircle,
      color: 'text-warning-500'
    },
    {
      id: '4',
      type: 'user',
      title: 'New team member added',
      description: '<EMAIL> joined the workspace',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
      icon: Users,
      color: 'text-info-500'
    }
  ]

  return (
    <div className="card p-6">
      <h3 className="text-lg font-semibold text-neutral-900 mb-4">Activity Feed</h3>
      
      <div className="space-y-4">
        {activities.map((activity, index) => {
          const Icon = activity.icon
          return (
            <div
              key={activity.id}
              className="flex items-start space-x-3 p-3 hover:bg-neutral-50 rounded-xl transition-colors duration-200"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <div className={`w-8 h-8 rounded-lg bg-neutral-100 flex items-center justify-center flex-shrink-0`}>
                <Icon className={`w-4 h-4 ${activity.color}`} />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-neutral-900 text-sm">{activity.title}</h4>
                <p className="text-sm text-neutral-600 mt-1">{activity.description}</p>
                <p className="text-xs text-neutral-500 mt-2">
                  {formatDate(activity.timestamp, 'relative')}
                </p>
              </div>
            </div>
          )
        })}
      </div>
      
      <button className="w-full mt-4 text-sm text-primary-600 hover:text-primary-700 font-medium">
        View all activities
      </button>
    </div>
  )
}
