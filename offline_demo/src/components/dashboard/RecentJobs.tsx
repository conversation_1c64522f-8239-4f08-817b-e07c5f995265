'use client'
import { Activity, Clock, CheckCircle, AlertCircle } from 'lucide-react'
import { mockDataService } from '@/lib/mockData'
import { cn } from '@/lib/utils'

export default function RecentJobs() {
  const recentJobs = mockDataService.getRecentJobs().slice(0, 8)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-success-500" />
      case 'running':
        return <Activity className="w-4 h-4 text-warning-500 animate-pulse" />
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-error-500" />
      default:
        return <Clock className="w-4 h-4 text-neutral-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-success-100 text-success-700 border-success-200'
      case 'running':
        return 'bg-warning-100 text-warning-700 border-warning-200'
      case 'failed':
        return 'bg-error-100 text-error-700 border-error-200'
      default:
        return 'bg-neutral-100 text-neutral-700 border-neutral-200'
    }
  }

  return (
    <div className="card p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-neutral-900">Recent Jobs</h3>
          <p className="text-sm text-neutral-600 mt-1">Latest pipeline executions and their status</p>
        </div>
      </div>

      <div className="space-y-4">
        {recentJobs.map((job, index) => (
          <div
            key={job.job_id}
            className="flex items-center justify-between p-4 bg-neutral-50 rounded-xl hover:bg-neutral-100 transition-colors duration-200"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            <div className="flex items-center space-x-4">
              {getStatusIcon(job.status)}
              <div>
                <h4 className="font-medium text-neutral-900">{job.job_id}</h4>
                <p className="text-sm text-neutral-600">{job.steps[0]?.title || 'Processing...'}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <span className={cn(
                  'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border',
                  getStatusColor(job.status)
                )}>
                  {job.status}
                </span>
                <p className="text-xs text-neutral-500 mt-1">
                  {Math.round(job.progress)}% complete
                </p>
              </div>
              
              {job.status === 'running' && (
                <div className="w-16 bg-neutral-200 rounded-full h-2">
                  <div 
                    className="bg-warning-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${job.progress}%` }}
                  />
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
