'use client'
import { useState } from 'react'
import { X, ChevronRight, CheckCircle, Play, Users, Zap, Target, BarChart3 } from 'lucide-react'
import { useAppStore } from '@/store/useAppStore'
import { cn } from '@/lib/utils'

const onboardingSteps = [
  {
    id: 1,
    title: 'Connect Data Sources',
    description: 'Link your databases, APIs, and systems',
    action: 'Add Connectors',
    targetTab: 'connectors',
    icon: Users,
    color: 'from-blue-500 to-blue-600'
  },
  {
    id: 2,
    title: 'Create Ingestion Pipelines',
    description: 'Set up automated data flows',
    action: 'Create Pipeline',
    targetTab: 'ingestions',
    icon: Zap,
    color: 'from-emerald-500 to-emerald-600'
  },
  {
    id: 3,
    title: 'AI-Powered Migration',
    description: 'Modernize legacy systems automatically',
    action: 'Start Migration',
    targetTab: 'migration',
    icon: BarChart3,
    color: 'from-purple-500 to-purple-600'
  },
  {
    id: 4,
    title: 'Define Business Goals',
    description: 'Let AI generate pipelines from objectives',
    action: 'Set Objectives',
    targetTab: 'objectives',
    icon: Target,
    color: 'from-orange-500 to-orange-600'
  },
  {
    id: 5,
    title: 'Data Model Mapping',
    description: 'Standardize with industry models',
    action: 'Map Models',
    targetTab: 'mapping',
    icon: Play,
    color: 'from-pink-500 to-pink-600'
  }
]

export default function OnboardingGuide() {
  const { toggleOnboarding, setActiveTab } = useAppStore()
  const [completedSteps, setCompletedSteps] = useState<number[]>([])

  const handleStepAction = (targetTab: string, stepId: number) => {
    setActiveTab(targetTab)
    markStepComplete(stepId)
  }

  const markStepComplete = (stepId: number) => {
    if (!completedSteps.includes(stepId)) {
      setCompletedSteps([...completedSteps, stepId])
    }
  }

  const progressPercentage = (completedSteps.length / onboardingSteps.length) * 100

  return (
    <div className="card-glass p-8 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500" />
      </div>
      
      <div className="relative z-10">
        {/* Header */}
        <div className="flex justify-between items-start mb-8">
          <div className="space-y-2">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gradient-primary">
                  Welcome to IronBook.ai
                </h3>
                <p className="text-neutral-600 mt-1">
                  Get started with these essential steps to unlock the full potential of AI-driven data integration
                </p>
              </div>
            </div>
            
            {/* Progress Bar */}
            <div className="flex items-center space-x-4 mt-6">
              {/* <div className="flex-1">
                <div className="flex items-center justify-between text-sm text-neutral-600 mb-2">
                  <span>Setup Progress</span>
                  <span className="font-semibold">{completedSteps.length}/{onboardingSteps.length} completed</span>
                </div>
                <div className="w-full bg-neutral-200 rounded-full h-3 overflow-hidden">
                  <div 
                    className="bg-gradient-to-r from-primary-500 to-primary-600 h-3 rounded-full transition-all duration-700 ease-out"
                    style={{ width: `${progressPercentage}%` }}
                  />
                </div>
              </div> */}
              
              {completedSteps.length === onboardingSteps.length && (
                <div className="flex items-center space-x-2 text-success-600 animate-fade-in">
                  <CheckCircle className="w-5 h-5" />
                  <span className="text-sm font-semibold">Complete!</span>
                </div>
              )}
            </div>
          </div>
          
          {/* <button 
            onClick={toggleOnboarding}
            className="p-2 hover:bg-neutral-100 rounded-xl transition-colors duration-200"
          >
            <X className="w-5 h-5 text-neutral-500" />
          </button> */}
        </div>

        {/* Steps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {onboardingSteps.map((step, index) => {
            const isCompleted = completedSteps.includes(step.id)
            const Icon = step.icon
            
            return (
              <div 
                key={step.id} 
                className={cn(
                  "group relative p-6 rounded-2xl border-2 transition-all duration-300 cursor-pointer",
                  "hover:shadow-lg hover:-translate-y-1",
                  isCompleted 
                    ? "border-success-200 bg-success-50/50 shadow-success-100" 
                    : "border-neutral-200 bg-white/50 hover:border-primary-300 hover:bg-primary-50/50"
                )}
                style={{ animationDelay: `${index * 100}ms` }}
                onClick={() => !isCompleted && handleStepAction(step.targetTab, step.id)}
              >
                {/* Step Number / Check */}
                <div className="flex items-center justify-between mb-4">
                  <div className={cn(
                    "w-10 h-10 rounded-xl flex items-center justify-center text-sm font-bold transition-all duration-300",
                    isCompleted 
                      ? "bg-success-500 text-white" 
                      : `bg-gradient-to-br ${step.color} text-white group-hover:scale-110`
                  )}>
                    {isCompleted ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <Icon className="w-5 h-5" />
                    )}
                  </div>
                  
                  {!isCompleted && (
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <ChevronRight className="w-4 h-4 text-neutral-400" />
                    </div>
                  )}
                </div>
                
                {/* Content */}
                <div className="space-y-3">
                  <h4 className={cn(
                    "font-semibold text-base transition-colors duration-200",
                    isCompleted ? "text-success-700" : "text-neutral-900 group-hover:text-primary-700"
                  )}>
                    {step.title}
                  </h4>
                  <p className="text-sm text-neutral-600 leading-relaxed">
                    {step.description}
                  </p>
                  
                  {!isCompleted && (
                    <button 
                      onClick={(e) => {
                        e.stopPropagation()
                        handleStepAction(step.targetTab, step.id)
                      }}
                      className="flex items-center text-sm font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200 mt-4"
                    >
                      <span>{step.action}</span>
                      <ChevronRight className="w-3 h-3 ml-1" />
                    </button>
                  )}
                  
                  {isCompleted && (
                    <div className="flex items-center space-x-2 text-success-600 mt-4">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm font-medium">Completed</span>
                    </div>
                  )}
                </div>

                {/* Completion Badge */}
                {isCompleted && (
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-success-500 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-white" />
                  </div>
                )}
              </div>
            )
          })}
        </div>

        {/* Call to Action */}
        {completedSteps.length === onboardingSteps.length && (
          <div className="mt-8 p-6 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl border border-primary-200">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-success-700 text-lg">Congratulations! 🎉</h4>
                <p className="text-success-600 mt-1">You've completed the onboarding process. You're ready to leverage the full power of AIM DataFlow!</p>
              </div>
              <button
                onClick={toggleOnboarding}
                className="btn-primary"
              >
                Get Started
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
