import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { JobStatus, JobStep } from '@/types/api'
import type { User } from '@/types/auth'

export interface AppNotification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  read: boolean
  actions?: Array<{
    label: string
    onClick: () => void
  }>
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  sidebarCollapsed: boolean
  showOnboarding: boolean
  language: string
  timezone: string
  dateFormat: string
  notifications: {
    email: boolean
    push: boolean
    jobUpdates: boolean
    systemAlerts: boolean
  }
}

const defaultUserPreferences: UserPreferences = {
  theme: 'light',
  sidebarCollapsed: false,
  showOnboarding: true,
  language: 'en',
  timezone: 'UTC',
  dateFormat: 'MM/dd/yyyy',
  notifications: {
    email: true,
    push: true,
    jobUpdates: true,
    systemAlerts: true,
  },
}

export interface AppState {
  // UI State
  sidebarCollapsed: boolean
  activeTab: string
  showOnboarding: boolean
  connectorsExpanded: boolean
  activeModal: string | null
  loading: Record<string, boolean>
  
  // User State
  currentUser: User | null
  userPreferences: UserPreferences
  
  // Job Management
  activeJobs: Map<string, JobStatus>
  jobPollingIntervals: Map<string, ReturnType<typeof setInterval>>
  jobHistory: JobStatus[]
  
  // Form Management
  formCheckpoints: Map<string, any>
  formErrors: Map<string, Record<string, string>>
  
  // Notifications
  notifications: AppNotification[]
  unreadCount: number
  
  // Search & Filters
  searchQuery: string
  filters: Record<string, any>
  
  // Actions
  // UI Actions
  toggleSidebar: () => void
  setActiveTab: (tab: string) => void
  toggleOnboarding: () => void
  setConnectorsExpanded: (expanded: boolean) => void
  setActiveModal: (modal: string | null) => void
  setLoading: (key: string, loading: boolean) => void
  
  // User Actions
  setCurrentUser: (user: User | null) => void
  updateUserPreferences: (preferences: Partial<UserPreferences>) => void
  
  // Job Actions
  addActiveJob: (jobId: string, jobStatus: JobStatus) => void
  updateJobStatus: (jobId: string, jobStatus: Partial<JobStatus>) => void
  removeActiveJob: (jobId: string) => void
  addToJobHistory: (jobStatus: JobStatus) => void
  startJobPolling: (jobId: string) => void
  stopJobPolling: (jobId: string) => void
  clearJobHistory: () => void
  
  // Form Actions
  saveFormCheckpoint: (taskId: string, data: any) => Promise<void>
  loadFormCheckpoint: (taskId: string) => any
  clearFormCheckpoint: (taskId: string) => void
  setFormErrors: (taskId: string, errors: Record<string, string>) => void
  clearFormErrors: (taskId: string) => void
  
  // Notification Actions
  addNotification: (notification: Omit<AppNotification, 'id' | 'timestamp' | 'read'>) => void
  markNotificationRead: (id: string) => void
  markAllNotificationsRead: () => void
  removeNotification: (id: string) => void
  clearNotifications: () => void
  
  // Search & Filter Actions
  setSearchQuery: (query: string) => void
  setFilters: (filters: Record<string, any>) => void
  clearFilters: () => void
}

export const useAppStore = create<AppState>()(
  persist(
    immer((set, get) => ({
      // Initial State
      sidebarCollapsed: false,
      activeTab: 'dashboard',
      showOnboarding: true,
      connectorsExpanded: false,
      activeModal: null,
      loading: {},
      
      currentUser: null,
      userPreferences: defaultUserPreferences,
      
      activeJobs: new Map(),
      jobPollingIntervals: new Map(),
      jobHistory: [],
      
      formCheckpoints: new Map(),
      formErrors: new Map(),
      
      notifications: [],
      unreadCount: 0,
      
      searchQuery: '',
      filters: {},
      
      // UI Actions
      toggleSidebar: () => set((state) => {
        state.sidebarCollapsed = !state.sidebarCollapsed
      }),
      
      setActiveTab: (tab) => set((state) => {
        state.activeTab = tab
      }),
      
      toggleOnboarding: () => set((state) => {
        state.showOnboarding = !state.showOnboarding
      }),
      
      setConnectorsExpanded: (expanded) => set((state) => {
        state.connectorsExpanded = expanded
      }),
      
      setActiveModal: (modal) => set((state) => {
        state.activeModal = modal
      }),
      
      setLoading: (key, loading) => set((state) => {
        state.loading[key] = loading
      }),
      
      // User Actions
      setCurrentUser: (user) => set((state) => {
        state.currentUser = user
      }),
      
      updateUserPreferences: (preferences) => set((state) => {
        state.userPreferences = { ...state.userPreferences, ...preferences }
      }),
      
      // Job Management Actions
      addActiveJob: (jobId, jobStatus) => set((state) => {
        state.activeJobs.set(jobId, jobStatus)
      }),
      
      updateJobStatus: (jobId, jobStatusUpdate) => set((state) => {
        const existingJob = state.activeJobs.get(jobId)
        if (existingJob) {
          const updatedJob = { ...existingJob, ...jobStatusUpdate, updated_at: new Date().toISOString() }
          state.activeJobs.set(jobId, updatedJob)
          
          // If job is completed or failed, add to history
          if (updatedJob.status === 'completed' || updatedJob.status === 'failed') {
            state.jobHistory.unshift(updatedJob)
            // Keep only last 50 jobs in history
            if (state.jobHistory.length > 50) {
              state.jobHistory = state.jobHistory.slice(0, 50)
            }
          }
        }
      }),
      
      removeActiveJob: (jobId) => set((state) => {
        state.activeJobs.delete(jobId)
        // Stop polling if active
        const interval = state.jobPollingIntervals.get(jobId)
        if (interval) {
          clearInterval(interval)
          state.jobPollingIntervals.delete(jobId)
        }
      }),
      
      addToJobHistory: (jobStatus) => set((state) => {
        state.jobHistory.unshift(jobStatus)
        if (state.jobHistory.length > 50) {
          state.jobHistory = state.jobHistory.slice(0, 50)
        }
      }),
      
      startJobPolling: (jobId) => {
        const interval = setInterval(() => {
          // In a real app, this would call the API
          // For demo, we'll simulate progress updates
          const currentJob = get().activeJobs.get(jobId)
          if (currentJob && currentJob.status === 'running') {
            const newProgress = Math.min(currentJob.progress + Math.random() * 10, 95)
            get().updateJobStatus(jobId, { progress: newProgress })
            
            // Simulate completion
            if (newProgress > 90 && Math.random() > 0.7) {
              get().updateJobStatus(jobId, { 
                status: 'completed', 
                progress: 100 
              })
              get().stopJobPolling(jobId)
            }
          }
        }, 2000)
        
        set((state) => {
          state.jobPollingIntervals.set(jobId, interval)
        })
      },
      
      stopJobPolling: (jobId) => set((state) => {
        const interval = state.jobPollingIntervals.get(jobId)
        if (interval) {
          clearInterval(interval)
          state.jobPollingIntervals.delete(jobId)
        }
      }),
      
      clearJobHistory: () => set((state) => {
        state.jobHistory = []
      }),
      
      // Form Management Actions
      saveFormCheckpoint: async (taskId, data) => {
        try {
          // In real app, save to backend
          set((state) => {
            state.formCheckpoints.set(taskId, data)
          })
        } catch (error) {
          console.error('Failed to save form checkpoint:', error)
          throw error
        }
      },
      
      loadFormCheckpoint: (taskId) => {
        return get().formCheckpoints.get(taskId) || null
      },
      
      clearFormCheckpoint: (taskId) => set((state) => {
        state.formCheckpoints.delete(taskId)
      }),
      
      setFormErrors: (taskId, errors) => set((state) => {
        state.formErrors.set(taskId, errors)
      }),
      
      clearFormErrors: (taskId) => set((state) => {
        state.formErrors.delete(taskId)
      }),
      
      // Notification Actions
      addNotification: (notification) => set((state) => {
        const newNotification: AppNotification = {
          ...notification,
          id: Math.random().toString(36).substr(2, 9),
          timestamp: new Date().toISOString(),
          read: false,
        }
        state.notifications.unshift(newNotification)
        state.unreadCount++
        
        // Keep only last 100 notifications
        if (state.notifications.length > 100) {
          state.notifications = state.notifications.slice(0, 100)
        }
      }),
      
      markNotificationRead: (id) => set((state) => {
        const notification = state.notifications.find(n => n.id === id)
        if (notification && !notification.read) {
          notification.read = true
          state.unreadCount = Math.max(0, state.unreadCount - 1)
        }
      }),
      
      markAllNotificationsRead: () => set((state) => {
        state.notifications.forEach(n => n.read = true)
        state.unreadCount = 0
      }),
      
      removeNotification: (id) => set((state) => {
        const index = state.notifications.findIndex(n => n.id === id)
        if (index > -1) {
          const notification = state.notifications[index]
          if (!notification.read) {
            state.unreadCount = Math.max(0, state.unreadCount - 1)
          }
          state.notifications.splice(index, 1)
        }
      }),
      
      clearNotifications: () => set((state) => {
        state.notifications = []
        state.unreadCount = 0
      }),
      
      // Search & Filter Actions
      setSearchQuery: (query) => set((state) => {
        state.searchQuery = query
      }),
      
      setFilters: (filters) => set((state) => {
        state.filters = { ...state.filters, ...filters }
      }),
      
      clearFilters: () => set((state) => {
        state.filters = {}
      }),
    })),
    {
      name: 'aim-app-store',
      partialize: (state) => ({
        sidebarCollapsed: state.sidebarCollapsed,
        showOnboarding: state.showOnboarding,
        userPreferences: state.userPreferences,
        jobHistory: state.jobHistory.slice(0, 10), // Only persist recent history
      }),
    }
  )
)

// Selector hooks for performance optimization
export const useActiveJobs = () => useAppStore((state) => state.activeJobs)
export const useNotifications = () => useAppStore((state) => ({
  notifications: state.notifications,
  unreadCount: state.unreadCount,
  markRead: state.markNotificationRead,
  markAllRead: state.markAllNotificationsRead,
  remove: state.removeNotification,
  clear: state.clearNotifications,
}))
export const useFormState = () => useAppStore((state) => ({
  checkpoints: state.formCheckpoints,
  errors: state.formErrors,
  saveCheckpoint: state.saveFormCheckpoint,
  loadCheckpoint: state.loadFormCheckpoint,
  clearCheckpoint: state.clearFormCheckpoint,
  setErrors: state.setFormErrors,
  clearErrors: state.clearFormErrors,
}))
export const useUIState = () => useAppStore((state) => ({
  sidebarCollapsed: state.sidebarCollapsed,
  activeTab: state.activeTab,
  activeModal: state.activeModal,
  loading: state.loading,
  toggleSidebar: state.toggleSidebar,
  setActiveTab: state.setActiveTab,
  setActiveModal: state.setActiveModal,
  setLoading: state.setLoading,
}))
