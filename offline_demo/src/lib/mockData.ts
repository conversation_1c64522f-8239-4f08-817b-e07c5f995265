import { 
  JobStatus, 
  JobStep, 
  ConnectorResponse, 
  Connection, 
  BVQ, 
  DataModel,
  FileUploadResponse,
  FilePreviewResponse,
  BusinessObjective,
  ObjectiveTemplate,
  DataMapping,
  MappingRule,
  UserProfile,
  WorkspaceSettings
} from '@/types/api'
import { sourceConnectors, destinationConnectors, Connector } from '@/types/connectors'
import { User } from '@/types/auth'

// Mock delay function for realistic API simulation
const delay = (ms: number = 1000) => new Promise(resolve => setTimeout(resolve, ms))

// Mock user data
export const mockUser: User = {
  id: 'user-1',
  email: '<EMAIL>',
  name: '<PERSON>',
  jobTitle: 'Senior Data Engineer',
  department: 'Data & Analytics',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
}

// Mock connectors with realistic data
export const mockConnectors: Connector[] = [
  ...sourceConnectors.map((conn, index) => ({
    ...conn,
    id: `source-${index}`,
    status: ['connected', 'disconnected', 'error'][index % 3] as 'connected' | 'disconnected' | 'error',
    connectionCount: Math.floor(Math.random() * 8) + 1,
    lastConnected: [
      '2 minutes ago',
      '1 hour ago', 
      '3 hours ago',
      '1 day ago',
      '2 days ago',
      '1 week ago'
    ][index % 6],
    configuration: {
      host: index % 3 === 0 ? 'localhost' : `${conn.name.toLowerCase().replace(/\s+/g, '-')}.company.com`,
      port: 5432 + index,
      database: `${conn.name.toLowerCase().replace(/\s+/g, '_')}_db`,
      username: 'admin',
      ssl: true,
      connection_timeout: 30
    }
  })),
  ...destinationConnectors.map((conn, index) => ({
    ...conn,
    id: `destination-${index}`,
    status: ['connected', 'disconnected', 'error'][index % 3] as 'connected' | 'disconnected' | 'error',
    connectionCount: Math.floor(Math.random() * 5) + 1,
    lastConnected: [
      '5 minutes ago',
      '30 minutes ago',
      '2 hours ago', 
      '6 hours ago',
      '1 day ago',
      '3 days ago'
    ][index % 6],
    configuration: {
      warehouse: `${conn.name.toLowerCase().replace(/\s+/g, '_')}_warehouse`,
      cluster_size: ['XS', 'S', 'M', 'L', 'XL'][index % 5],
      region: ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1'][index % 4],
      auto_suspend: true,
      auto_resume: true
    }
  }))
]

// Mock connections/ingestions
export const mockConnections: Connection[] = [
  {
    id: 'conn-1',
    name: 'PostgreSQL → Databricks ETL',
    source_id: 'source-0',
    destination_id: 'destination-0', 
    status: 'active',
    last_sync: '2024-03-15T10:30:00Z',
    next_sync: '2024-03-15T11:00:00Z',
    records_synced: 2847563,
    created_at: '2024-03-10T09:00:00Z'
  },
  {
    id: 'conn-2', 
    name: 'GitHub Analytics Pipeline',
    source_id: 'source-3',
    destination_id: 'destination-2',
    status: 'active',
    last_sync: '2024-03-15T10:15:00Z', 
    next_sync: '2024-03-15T12:00:00Z',
    records_synced: 156789,
    created_at: '2024-03-12T14:30:00Z'
  },
  {
    id: 'conn-3',
    name: 'Salesforce Data Sync',
    source_id: 'source-10',
    destination_id: 'destination-1', 
    status: 'error',
    last_sync: '2024-03-15T08:45:00Z',
    next_sync: '2024-03-15T11:30:00Z',
    records_synced: 89234,
    created_at: '2024-03-08T16:20:00Z'
  },
  {
    id: 'conn-4',
    name: 'MongoDB Analytics',
    source_id: 'source-8',
    destination_id: 'destination-2',
    status: 'paused', 
    last_sync: '2024-03-14T22:30:00Z',
    next_sync: '2024-03-16T09:00:00Z',
    records_synced: 1234567,
    created_at: '2024-03-05T11:15:00Z'
  }
]

// Mock job statuses and steps
export const mockJobSteps: Record<string, JobStep[]> = {
  'job-exploration-1': [
    {
      step_id: 'step-1',
      title: 'Repository Analysis',
      status: 'completed',
      content: '# Repository Analysis Complete\n\n✅ Scanned 245 Python files\n✅ Identified 12 data pipelines\n✅ Found 8 legacy ETL patterns\n\n**Key Findings:**\n- Apache Airflow DAGs detected\n- Pandas transformations identified\n- SQL queries extracted',
      timestamp: '2024-03-15T10:00:00Z',
      duration: 45
    },
    {
      step_id: 'step-2', 
      title: 'Dependency Mapping',
      status: 'completed',
      content: '# Dependency Analysis\n\n📦 **Dependencies Found:**\n- pandas==1.5.3\n- sqlalchemy==2.0.10\n- apache-airflow==2.6.1\n- psycopg2==2.9.6\n\n🔗 **Data Flow Mapping:**\n- 5 database connections\n- 3 file processing workflows\n- 2 API integrations',
      timestamp: '2024-03-15T10:02:30Z',
      duration: 120
    },
    {
      step_id: 'step-3',
      title: 'Code Complexity Analysis', 
      status: 'running',
      content: '# Analyzing Code Complexity\n\n⏳ Processing transformation logic...\n\n**Progress:**\n- Simple transforms: ✅ 15/15\n- Medium transforms: ⏳ 8/12\n- Complex transforms: ⏳ 2/8\n\n**Estimated migration effort:** 24-32 hours',
      timestamp: '2024-03-15T10:05:00Z'
    },
    {
      step_id: 'step-4',
      title: 'Generate Migration Plan',
      status: 'pending', 
      content: '',
      timestamp: '2024-03-15T10:08:00Z'
    }
  ],
  'job-codegen-1': [
    {
      step_id: 'step-1',
      title: 'Initialize Code Generation',
      status: 'completed',
      content: '# Code Generation Started\n\n🎯 **Target Platform:** Databricks PySpark\n📝 **Templates:** Modern data pipeline patterns\n🔧 **Configuration:** Production-ready setup\n\n✅ Environment prepared successfully',
      timestamp: '2024-03-15T11:00:00Z',
      duration: 30
    },
    {
      step_id: 'step-2',
      title: 'Transform Legacy Code',
      status: 'completed', 
      content: '# Legacy Code Transformation\n\n📄 **Files Processed:**\n- customer_etl.py → customer_pipeline.py\n- sales_reports.py → sales_analytics.py\n- data_quality.py → quality_checks.py\n\n🔄 **Transformations Applied:**\n- Pandas → PySpark DataFrame\n- SQLAlchemy → Spark SQL\n- Custom functions → UDFs',
      timestamp: '2024-03-15T11:02:00Z', 
      duration: 180
    },
    {
      step_id: 'step-3',
      title: 'Generate Tests & Documentation',
      status: 'running',
      content: '# Generating Tests & Documentation\n\n⏳ Creating comprehensive test suite...\n\n**Generated:**\n- Unit tests: ✅ 25/25\n- Integration tests: ⏳ 8/12\n- Documentation: ⏳ 60%\n- README files: ✅ Complete',
      timestamp: '2024-03-15T11:05:00Z'
    }
  ]
}

export const mockJobs: JobStatus[] = [
  {
    job_id: 'job-exploration-1',
    status: 'running',
    progress: 75,
    steps: mockJobSteps['job-exploration-1'],
    started_at: '2024-03-15T10:00:00Z'
  },
  {
    job_id: 'job-codegen-1', 
    status: 'running',
    progress: 60,
    steps: mockJobSteps['job-codegen-1'],
    started_at: '2024-03-15T11:00:00Z'
  },
  {
    job_id: 'job-bvq-gen-1',
    status: 'completed',
    progress: 100,
    steps: [
      {
        step_id: 'step-1',
        title: 'Business Context Analysis',
        status: 'completed',
        content: '# Business Context Analysis Complete\n\n✅ Analyzed business objective\n✅ Identified key metrics\n✅ Mapped data requirements\n\n**Generated 12 business value questions**',
        timestamp: '2024-03-15T09:30:00Z',
        duration: 90
      }
    ],
    started_at: '2024-03-15T09:30:00Z',
    completed_at: '2024-03-15T09:32:30Z'
  }
]

// Mock Business Value Questions
export const mockBVQs: BVQ[] = [
  {
    bvq_id: 'bvq-1',
    bvq_content: 'What is the customer acquisition cost trend over the last 12 months?',
    bvq_relevancy: 'high',
    bvq_purpose: 'Track marketing efficiency and ROI to optimize budget allocation',
    created_at: '2024-03-15T09:30:00Z'
  },
  {
    bvq_id: 'bvq-2', 
    bvq_content: 'Which product categories generate the highest profit margins?',
    bvq_relevancy: 'high',
    bvq_purpose: 'Identify most profitable products to focus inventory and marketing efforts',
    created_at: '2024-03-15T09:30:15Z'
  },
  {
    bvq_id: 'bvq-3',
    bvq_content: 'What is the average customer lifetime value by segment?',
    bvq_relevancy: 'medium', 
    bvq_purpose: 'Understand customer value to tailor retention strategies by segment',
    created_at: '2024-03-15T09:30:30Z'
  },
  {
    bvq_id: 'bvq-4',
    bvq_content: 'How does seasonal demand affect inventory turnover rates?',
    bvq_relevancy: 'medium',
    bvq_purpose: 'Optimize inventory management and reduce carrying costs',
    created_at: '2024-03-15T09:30:45Z'
  },
  {
    bvq_id: 'bvq-5',
    bvq_content: 'What percentage of customers are repeat buyers within 6 months?',
    bvq_relevancy: 'high',
    bvq_purpose: 'Measure customer loyalty and retention effectiveness',
    created_at: '2024-03-15T09:31:00Z'
  }
]

// Mock Data Models
export const mockDataModels: DataModel[] = [
  {
    data_model_id: 'model-1',
    name: 'Retail Analytics Standard',
    description: 'Comprehensive data model for retail analytics including customer behavior, inventory, and sales metrics',
    industry: 'Retail',
    official_website: 'https://retailanalytics.org/standard',
    example_usage: 'E-commerce platforms, brick-and-mortar stores, omnichannel retailers',
    explanation: 'Standardized schema covering customer journey, product catalog, inventory management, and financial reporting'
  },
  {
    data_model_id: 'model-2',
    name: 'Healthcare FHIR R4',
    description: 'Fast Healthcare Interoperability Resources standard for healthcare data exchange',
    industry: 'Healthcare', 
    official_website: 'https://hl7.org/fhir/',
    example_usage: 'Electronic health records, clinical systems, healthcare analytics',
    explanation: 'International standard for healthcare information exchange with comprehensive patient data models'
  },
  {
    data_model_id: 'model-3', 
    name: 'Financial Services MISMO',
    description: 'Mortgage Industry Standards Maintenance Organization data standards',
    industry: 'Financial Services',
    official_website: 'https://www.mismo.org/',
    example_usage: 'Mortgage lending, credit reporting, financial risk assessment',
    explanation: 'Industry standard for mortgage and real estate finance data interchange'
  },
  {
    data_model_id: 'model-4',
    name: 'Manufacturing ISA-95',
    description: 'International standard for enterprise-control system integration in manufacturing',
    industry: 'Manufacturing',
    official_website: 'https://www.isa.org/standards/isa-95/',
    example_usage: 'Production planning, quality management, supply chain optimization',
    explanation: 'Standard for integrating business and manufacturing systems with defined data models'
  }
]

// Mock file data
export const mockFileUpload: FileUploadResponse = {
  file_id: 'file-demo-1',
  filename: 'customer_data_sample.csv',
  size: 2048576, // 2MB
  content_type: 'text/csv'
}

export const mockFilePreview: FilePreviewResponse = {
  columns: ['customer_id', 'name', 'email', 'age', 'city', 'signup_date', 'total_orders', 'lifetime_value'],
  data: [
    {
      customer_id: 'CUST-001',
      name: 'Sarah Johnson', 
      email: '<EMAIL>',
      age: 28,
      city: 'San Francisco',
      signup_date: '2023-01-15',
      total_orders: 12,
      lifetime_value: 1247.50
    },
    {
      customer_id: 'CUST-002',
      name: 'Michael Chen',
      email: '<EMAIL>', 
      age: 35,
      city: 'New York',
      signup_date: '2023-02-03',
      total_orders: 8,
      lifetime_value: 892.25
    },
    {
      customer_id: 'CUST-003',
      name: 'Emily Davis',
      email: '<EMAIL>',
      age: 24,
      city: 'Los Angeles', 
      signup_date: '2023-02-18',
      total_orders: 15,
      lifetime_value: 1876.75
    },
    {
      customer_id: 'CUST-004',
      name: 'David Wilson',
      email: '<EMAIL>',
      age: 42,
      city: 'Chicago',
      signup_date: '2023-01-08',
      total_orders: 6,
      lifetime_value: 654.30
    },
    {
      customer_id: 'CUST-005',
      name: 'Lisa Rodriguez',
      email: '<EMAIL>',
      age: 31,
      city: 'Austin', 
      signup_date: '2023-03-02',
      total_orders: 9,
      lifetime_value: 1123.80
    }
  ],
  total_rows: 50000,
  preview_rows: 5
}

// Mock dashboard statistics
export const mockDashboardStats = {
  activeIngestions: 12,
  recordsSyncedToday: 847000,
  successRate: 98.2,
  failedJobs: 3,
  trends: {
    ingestions: '+2 this week',
    records: '+12% from yesterday', 
    successRate: '+0.3% this week',
    failures: '+1 needs attention'
  }
}

// Mock recent activity
export const mockRecentJobs = [
  {
    id: 'activity-1',
    type: 'ingestion',
    title: 'PostgreSQL → Databricks ETL',
    status: 'completed',
    timestamp: '2024-03-15T10:30:00Z',
    duration: 45,
    recordsProcessed: 125000
  },
  {
    id: 'activity-2', 
    type: 'migration',
    title: 'Legacy Pipeline Analysis',
    status: 'running',
    timestamp: '2024-03-15T10:00:00Z',
    progress: 75
  },
  {
    id: 'activity-3',
    type: 'objectives',
    title: 'Customer Analytics BVQ Generation',
    status: 'completed',
    timestamp: '2024-03-15T09:30:00Z',
    duration: 150,
    itemsGenerated: 12
  },
  {
    id: 'activity-4',
    type: 'mapping',
    title: 'Retail Standard Mapping Analysis', 
    status: 'failed',
    timestamp: '2024-03-15T09:15:00Z',
    error: 'Schema validation failed'
  }
]

// Mock recent activity for activity feed
export const mockRecentActivity = [
  {
    id: 'activity-1',
    type: 'connection',
    title: 'PostgreSQL connection established',
    description: 'Successfully connected to production database',
    timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 minutes ago
    icon: 'Database',
    color: 'text-success-500'
  },
  {
    id: 'activity-2',
    type: 'job',
    title: 'Data ingestion completed',
    description: '2.3M records processed successfully',
    timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
    icon: 'CheckCircle',
    color: 'text-success-500'
  },
  {
    id: 'activity-3',
    type: 'alert',
    title: 'High latency detected',
    description: 'API response time increased to 850ms',
    timestamp: new Date(Date.now() - 1000 * 60 * 32).toISOString(), // 32 minutes ago
    icon: 'AlertCircle',
    color: 'text-warning-500'
  },
  {
    id: 'activity-4',
    type: 'user',
    title: 'New team member added',
    description: '<EMAIL> joined the workspace',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
    icon: 'Users',
    color: 'text-info-500'
  }
]

// Mock Business Objectives
export const mockObjectives: BusinessObjective[] = [
  {
    id: 'obj-1',
    title: 'Improve Data Quality to 99.5%',
    description: 'Enhance data accuracy and completeness across all customer data sources to achieve 99.5% quality score',
    priority: 'high',
    category: 'data_quality',
    status: 'active',
    metrics: [
      {
        id: 'metric-1',
        name: 'Data Accuracy Score',
        description: 'Percentage of accurate records',
        target_value: 99.5,
        current_value: 94.2,
        unit: '%',
        type: 'kpi'
      },
      {
        id: 'metric-2',
        name: 'Completeness Rate',
        description: 'Percentage of complete records',
        target_value: 98.0,
        current_value: 92.8,
        unit: '%',
        type: 'kpi'
      }
    ],
    timeline: {
      start_date: '2024-01-15',
      target_date: '2024-06-30',
    },
    stakeholders: ['Alex Johnson', 'Sarah Chen', 'Michael Brown'],
    business_impact: 'Improved customer insights and reduced decision-making errors',
    technical_requirements: ['Data validation rules', 'Automated quality checks', 'Real-time monitoring'],
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-03-10T14:30:00Z'
  },
  {
    id: 'obj-2',
    title: 'Reduce Migration Time by 60%',
    description: 'Optimize data migration processes to reduce average migration time from 5 days to 2 days',
    priority: 'high',
    category: 'performance',
    status: 'active',
    metrics: [
      {
        id: 'metric-3',
        name: 'Average Migration Time',
        description: 'Time taken for complete data migration',
        target_value: 2.0,
        current_value: 4.8,
        unit: 'days',
        type: 'sla'
      }
    ],
    timeline: {
      start_date: '2024-02-01',
      target_date: '2024-08-15',
    },
    stakeholders: ['Data Engineering Team', 'DevOps Team'],
    business_impact: 'Faster time-to-market for data-driven initiatives',
    technical_requirements: ['Parallel processing', 'Optimized pipelines', 'Resource scaling'],
    created_at: '2024-02-01T09:00:00Z',
    updated_at: '2024-03-05T16:45:00Z'
  },
  {
    id: 'obj-3',
    title: 'Achieve GDPR Compliance',
    description: 'Implement comprehensive GDPR compliance across all data processing activities',
    priority: 'high',
    category: 'compliance',
    status: 'active',
    metrics: [
      {
        id: 'metric-4',
        name: 'Compliance Score',
        description: 'GDPR compliance percentage',
        target_value: 100.0,
        current_value: 78.5,
        unit: '%',
        type: 'threshold'
      }
    ],
    timeline: {
      start_date: '2024-01-10',
      target_date: '2024-05-25',
    },
    stakeholders: ['Legal Team', 'Privacy Officer', 'Data Engineering'],
    business_impact: 'Legal compliance and customer trust',
    technical_requirements: ['Data encryption', 'Access controls', 'Audit logging', 'Data anonymization'],
    created_at: '2024-01-10T08:00:00Z',
    updated_at: '2024-03-12T11:20:00Z'
  },
  {
    id: 'obj-4',
    title: 'Reduce Infrastructure Costs by 30%',
    description: 'Optimize data storage and processing costs through intelligent resource management',
    priority: 'medium',
    category: 'cost_optimization',
    status: 'draft',
    metrics: [
      {
        id: 'metric-5',
        name: 'Monthly Infrastructure Cost',
        description: 'Total monthly cloud infrastructure spend',
        target_value: 35000,
        current_value: 50000,
        unit: 'USD',
        type: 'kpi'
      }
    ],
    timeline: {
      start_date: '2024-04-01',
      target_date: '2024-12-31',
    },
    stakeholders: ['Finance Team', 'Infrastructure Team'],
    business_impact: 'Improved ROI and budget allocation',
    technical_requirements: ['Auto-scaling', 'Resource optimization', 'Cost monitoring'],
    created_at: '2024-03-01T10:30:00Z',
    updated_at: '2024-03-15T14:00:00Z'
  }
]

export const mockObjectiveTemplates: ObjectiveTemplate[] = [
  {
    id: 'template-1',
    name: 'Data Quality Improvement',
    description: 'Standard template for improving data quality metrics',
    category: 'data_quality',
    metrics: [
      {
        name: 'Accuracy Score',
        description: 'Percentage of accurate records',
        target_value: 99.0,
        unit: '%',
        type: 'kpi'
      },
      {
        name: 'Completeness Rate',
        description: 'Percentage of complete records',
        target_value: 95.0,
        unit: '%',
        type: 'kpi'
      }
    ],
    estimated_timeline: 120
  },
  {
    id: 'template-2',
    name: 'Performance Optimization',
    description: 'Template for improving system performance metrics',
    category: 'performance',
    metrics: [
      {
        name: 'Response Time',
        description: 'Average API response time',
        target_value: 500,
        unit: 'ms',
        type: 'sla'
      },
      {
        name: 'Throughput',
        description: 'Records processed per second',
        target_value: 1000,
        unit: 'rps',
        type: 'kpi'
      }
    ],
    estimated_timeline: 90
  }
]

// Mock Data Mappings
export const mockDataMappings: DataMapping[] = [
  {
    id: 'mapping-1',
    name: 'Customer Data Standardization',
    description: 'Map legacy customer data to standard customer entity model',
    source_schema: 'legacy_crm',
    target_model: 'standard_customer_v2',
    mapping_rules: [
      {
        id: 'rule-1',
        source_field: 'cust_id',
        target_field: 'customer_id',
        transformation: 'CAST(cust_id AS VARCHAR(50))',
        validation_rules: ['NOT NULL', 'UNIQUE'],
        confidence_score: 0.98,
        status: 'approved'
      },
      {
        id: 'rule-2',
        source_field: 'fname',
        target_field: 'first_name',
        transformation: 'TRIM(UPPER(fname))',
        validation_rules: ['LENGTH > 0', 'ALPHA_ONLY'],
        confidence_score: 0.95,
        status: 'approved'
      },
      {
        id: 'rule-3',
        source_field: 'lname',
        target_field: 'last_name',
        transformation: 'TRIM(UPPER(lname))',
        validation_rules: ['LENGTH > 0', 'ALPHA_ONLY'],
        confidence_score: 0.95,
        status: 'approved'
      },
      {
        id: 'rule-4',
        source_field: 'email_addr',
        target_field: 'email',
        transformation: 'LOWER(TRIM(email_addr))',
        validation_rules: ['EMAIL_FORMAT', 'UNIQUE'],
        confidence_score: 0.92,
        status: 'suggested'
      }
    ],
    validation_status: 'valid',
    completion_percentage: 85,
    ai_suggestions: [
      {
        id: 'suggestion-1',
        type: 'mapping',
        suggestion: 'Map phone_num to phone_number with format standardization',
        confidence: 0.87,
        reasoning: 'Field names are similar and both contain phone data',
        accepted: false
      },
      {
        id: 'suggestion-2',
        type: 'transformation',
        suggestion: 'Apply date standardization to created_date field',
        confidence: 0.91,
        reasoning: 'Date format inconsistency detected in source data',
        accepted: true
      }
    ],
    created_at: '2024-02-15T10:00:00Z',
    updated_at: '2024-03-10T14:30:00Z'
  },
  {
    id: 'mapping-2',
    name: 'Product Catalog Harmonization',
    description: 'Standardize product data across multiple e-commerce platforms',
    source_schema: 'ecommerce_products',
    target_model: 'unified_product_catalog',
    mapping_rules: [
      {
        id: 'rule-5',
        source_field: 'product_sku',
        target_field: 'sku',
        transformation: 'UPPER(TRIM(product_sku))',
        validation_rules: ['NOT NULL', 'UNIQUE', 'ALPHANUMERIC'],
        confidence_score: 0.99,
        status: 'approved'
      },
      {
        id: 'rule-6',
        source_field: 'item_name',
        target_field: 'product_name',
        transformation: 'TRIM(item_name)',
        validation_rules: ['LENGTH >= 3'],
        confidence_score: 0.94,
        status: 'suggested'
      }
    ],
    validation_status: 'pending',
    completion_percentage: 45,
    ai_suggestions: [
      {
        id: 'suggestion-3',
        type: 'validation',
        suggestion: 'Add price range validation for product_price field',
        confidence: 0.89,
        reasoning: 'Detected outlier prices that may indicate data quality issues',
        accepted: false
      }
    ],
    created_at: '2024-03-01T09:00:00Z',
    updated_at: '2024-03-15T16:45:00Z'
  }
]

// Mock User Profile
export const mockUserProfile: UserProfile = {
  id: 'user-1',
  username: 'alex.johnson',
  email: '<EMAIL>',
  full_name: 'Alex Johnson',
  avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  role: 'data_engineer',
  preferences: {
    theme: 'light',
    language: 'en-US',
    timezone: 'America/New_York',
    notifications: {
      email_enabled: true,
      push_enabled: true,
      job_completion: true,
      job_failure: true,
      system_updates: false,
      security_alerts: true,
      weekly_reports: true
    },
    dashboard_layout: {
      widgets: [
        { id: 'stats', type: 'stats_cards', position: { x: 0, y: 0, w: 12, h: 2 }, config: {} },
        { id: 'jobs', type: 'recent_jobs', position: { x: 0, y: 2, w: 8, h: 4 }, config: {} },
        { id: 'activity', type: 'activity_feed', position: { x: 8, y: 2, w: 4, h: 4 }, config: {} }
      ],
      layout: 'grid',
      columns: 12
    },
    default_page_size: 25,
    date_format: 'MM/DD/YYYY',
    number_format: 'en-US'
  },
  created_at: '2023-08-15T10:00:00Z',
  last_login: '2024-03-18T08:30:00Z'
}

// Mock Workspace Settings
export const mockWorkspaceSettings: WorkspaceSettings = {
  id: 'workspace-1',
  name: 'AIM DataFlow Workspace',
  description: 'Main workspace for data migration and integration projects',
  default_retention_days: 90,
  security_policy: {
    password_policy: {
      min_length: 12,
      require_uppercase: true,
      require_lowercase: true,
      require_numbers: true,
      require_symbols: true,
      expiry_days: 90
    },
    session_timeout: 480, // 8 hours
    mfa_required: true,
    ip_whitelist: ['10.0.0.0/8', '***********/16'],
    encryption_at_rest: true,
    encryption_in_transit: true
  },
  integration_settings: {
    ai_models: [
      {
        provider: 'OpenAI',
        model_name: 'gpt-4',
        api_key: '***masked***',
        endpoint: 'https://api.openai.com/v1',
        parameters: { temperature: 0.7, max_tokens: 2048 }
      }
    ],
    external_apis: [
      {
        name: 'Salesforce API',
        base_url: 'https://company.salesforce.com',
        auth_type: 'oauth',
        credentials: { client_id: '***masked***', client_secret: '***masked***' }
      }
    ],
    data_sources: [
      {
        name: 'Production PostgreSQL',
        type: 'postgresql',
        connection_string: '***masked***',
        ssl_enabled: true,
        timeout: 30
      }
    ]
  },
  audit_settings: {
    enabled: true,
    retention_days: 365,
    log_level: 'detailed',
    events: [
      { type: 'user_login', enabled: true, description: 'User authentication events' },
      { type: 'data_access', enabled: true, description: 'Data access and modification events' },
      { type: 'system_config', enabled: true, description: 'System configuration changes' },
      { type: 'job_execution', enabled: true, description: 'Job execution and status changes' }
    ]
  }
}

// API simulation functions
export class MockApiService {
  // Authentication
  static async login(credentials: { email: string; password: string }) {
    await delay(1500)
    return {
      user: mockUser,
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
      expires_in: 3600
    }
  }

  // Connectors
  static async getConnectors(type?: 'source' | 'destination') {
    await delay(800)
    const filtered = type ? mockConnectors.filter(c => c.type === type) : mockConnectors
    return {
      items: filtered,
      total: filtered.length,
      offset: 0,
      limit: 50
    }
  }

  static async testConnector(connectorId: string) {
    await delay(2000)
    const connector = mockConnectors.find(c => c.id === connectorId)
    const success = Math.random() > 0.2 // 80% success rate
    
    return {
      success,
      message: success 
        ? `${connector?.name} connection test successful`
        : `${connector?.name} connection failed: Invalid credentials`
    }
  }

  // Jobs
  static async triggerJob(taskId: string, config: any) {
    await delay(1000)
    const jobId = `job-${taskId}-${Date.now()}`
    
    // Add to mock jobs with initial status
    mockJobs.push({
      job_id: jobId,
      status: 'pending',
      progress: 0,
      steps: [],
      started_at: new Date().toISOString()
    })
    
    return jobId
  }

  static async getJobStatus(jobId: string) {
    await delay(500)
    const job = mockJobs.find(j => j.job_id === jobId)
    
    if (!job) {
      throw new Error('Job not found')
    }
    
    // Simulate job progress
    if (job.status === 'running' && job.progress < 100) {
      job.progress = Math.min(100, job.progress + Math.random() * 10)
      
      if (job.progress >= 100) {
        job.status = 'completed'
        job.completed_at = new Date().toISOString()
      }
    }
    
    return job
  }

  // Business Value Questions
  static async generateBVQs(objective: string) {
    await delay(3000)
    return {
      bvqs: mockBVQs.slice(0, Math.floor(Math.random() * 3) + 3) // 3-5 random BVQs
    }
  }

  // Data Models
  static async searchDataModels(query: string) {
    await delay(1200)
    const filtered = mockDataModels.filter(model => 
      model.name.toLowerCase().includes(query.toLowerCase()) ||
      model.description.toLowerCase().includes(query.toLowerCase()) ||
      model.industry.toLowerCase().includes(query.toLowerCase())
    )
    
    return {
      models: filtered,
      total: filtered.length
    }
  }

  // File Upload
  static async uploadFile(file: File, onProgress?: (progress: number) => void) {
    // Simulate upload progress
    for (let i = 0; i <= 100; i += 10) {
      await delay(200)
      onProgress?.(i)
    }
    
    return {
      ...mockFileUpload,
      filename: file.name,
      size: file.size,
      content_type: file.type
    }
  }

  static async getFilePreview(fileId: string) {
    await delay(800)
    return mockFilePreview
  }

  // Dashboard
  static async getDashboardStats() {
    await delay(600)
    return mockDashboardStats
  }

  getDashboardStats() {
    return {
      activeConnections: 12,
      dataProcessedTB: 2.4,
      activePipelines: 8,
      successRate: 96,
      avgLatencyMs: 145,
      activeAlerts: 2,
      totalRecords: 2847563,
      failedJobs: 1
    }
  }

  getRecentJobs() {
    return mockJobs
  }

  getQuickActions() {
    return [
      {
        id: 'add-connector',
        title: 'Add Connector',
        description: 'Connect new data source',
        action: 'connectors'
      },
      {
        id: 'create-pipeline',
        title: 'Create Pipeline', 
        description: 'Set up data ingestion',
        action: 'ingestions'
      },
      {
        id: 'start-migration',
        title: 'Start Migration',
        description: 'Modernize legacy systems',
        action: 'migration'
      },
      {
        id: 'view-analytics',
        title: 'View Analytics',
        description: 'Check performance metrics',
        action: 'dashboard'
      }
    ]
  }

  getActivityFeed() {
    return mockRecentActivity
  }

  static async getRecentActivity() {
    await delay(400)
    return mockRecentJobs
  }

  // Connections/Ingestions
  static async getConnections() {
    await delay(700)
    return {
      items: mockConnections,
      total: mockConnections.length,
      offset: 0,
      limit: 20
    }
  }

  static async createConnection(data: any) {
    await delay(2000)
    const newConnection: Connection = {
      id: `conn-${Date.now()}`,
      name: data.name,
      source_id: data.source_id,
      destination_id: data.destination_id,
      status: 'active',
      last_sync: new Date().toISOString(),
      next_sync: new Date(Date.now() + 3600000).toISOString(), // 1 hour from now
      records_synced: 0,
      created_at: new Date().toISOString()
    }
    
    mockConnections.push(newConnection)
    return newConnection
  }

  // Business Objectives methods
  async getObjectives(): Promise<BusinessObjective[]> {
    await delay(500)
    return mockObjectives
  }

  async getObjectiveById(id: string): Promise<BusinessObjective | null> {
    await delay(300)
    return mockObjectives.find(obj => obj.id === id) || null
  }

  async createObjective(data: Partial<BusinessObjective>): Promise<BusinessObjective> {
    await delay(800)
    const newObjective: BusinessObjective = {
      id: `obj-${Date.now()}`,
      title: data.title || 'New Objective',
      description: data.description || '',
      priority: data.priority || 'medium',
      category: data.category || 'data_quality',
      status: 'draft',
      metrics: data.metrics || [],
      timeline: data.timeline || {
        start_date: new Date().toISOString().split('T')[0],
        target_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      },
      stakeholders: data.stakeholders || [],
      business_impact: data.business_impact || '',
      technical_requirements: data.technical_requirements || [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    mockObjectives.push(newObjective)
    return newObjective
  }

  async getObjectiveTemplates(): Promise<ObjectiveTemplate[]> {
    await delay(400)
    return mockObjectiveTemplates
  }

  // Data Mapping methods
  async getDataMappings(): Promise<DataMapping[]> {
    await delay(600)
    return mockDataMappings
  }

  async getDataMappingById(id: string): Promise<DataMapping | null> {
    await delay(300)
    return mockDataMappings.find(mapping => mapping.id === id) || null
  }

  async createDataMapping(data: Partial<DataMapping>): Promise<DataMapping> {
    await delay(1000)
    const newMapping: DataMapping = {
      id: `mapping-${Date.now()}`,
      name: data.name || 'New Mapping',
      description: data.description || '',
      source_schema: data.source_schema || '',
      target_model: data.target_model || '',
      mapping_rules: data.mapping_rules || [],
      validation_status: 'pending',
      completion_percentage: 0,
      ai_suggestions: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    mockDataMappings.push(newMapping)
    return newMapping
  }

  async generateMappingSuggestions(mappingId: string): Promise<DataMapping> {
    await delay(2000) // Simulate AI processing time
    const mapping = mockDataMappings.find(m => m.id === mappingId)
    if (!mapping) throw new Error('Mapping not found')

    // Simulate AI-generated suggestions
    mapping.ai_suggestions.push({
      id: `suggestion-${Date.now()}`,
      type: 'mapping',
      suggestion: 'Suggested field mapping based on semantic analysis',
      confidence: 0.85 + Math.random() * 0.1,
      reasoning: 'Field names and data types show high similarity',
      accepted: false
    })
    mapping.updated_at = new Date().toISOString()
    return mapping
  }

  // Settings methods
  async getUserProfile(): Promise<UserProfile> {
    await delay(400)
    return mockUserProfile
  }

  async updateUserProfile(data: Partial<UserProfile>): Promise<UserProfile> {
    await delay(600)
    Object.assign(mockUserProfile, data)
    return mockUserProfile
  }

  async getWorkspaceSettings(): Promise<WorkspaceSettings> {
    await delay(500)
    return mockWorkspaceSettings
  }

  async updateWorkspaceSettings(data: Partial<WorkspaceSettings>): Promise<WorkspaceSettings> {
    await delay(800)
    Object.assign(mockWorkspaceSettings, data)
    return mockWorkspaceSettings
  }
}

// Export the service instance
export const mockDataService = new MockApiService()
