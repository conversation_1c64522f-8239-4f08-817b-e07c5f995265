# Complete API Integration Guide - Data Integration Platform

## Table of Contents
1. [API Client Setup](#api-client-setup)
2. [Authentication Integration](#authentication-integration)
3. [Async Job Management](#async-job-management)
4. [Connectors API](#connectors-api)
5. [Ingestion Pipelines API](#ingestion-pipelines-api)
6. [Pipeline Migration API](#pipeline-migration-api)
7. [Business Objectives API](#business-objectives-api)
8. [Data Model Mapping API](#data-model-mapping-api)
9. [File Upload & Management](#file-upload--management)
10. [Real-time Monitoring](#real-time-monitoring)
11. [Form Checkpoint Management](#form-checkpoint-management)
12. [Error Handling](#error-handling)
13. [Testing & Debugging](#testing--debugging)

## API Client Setup

### 1. Base API Client Configuration

Create `src/lib/apiClient.ts`:

```typescript
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { getSession, signOut } from 'next-auth/react'
import toast from 'react-hot-toast'

export interface ApiResponse<T = any> {
  data: T
  message?: string
  status: 'success' | 'error'
}

export interface ApiError {
  message: string
  code?: string
  details?: any
}

class ApiClient {
  private client: AxiosInstance
  private isRefreshing: boolean = false
  private failedQueue: Array<{
    resolve: (value?: any) => void
    reject: (reason?: any) => void
  }> = []

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_BACKEND_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // Request interceptor - Add auth token
    this.client.interceptors.request.use(
      async (config) => {
        const session = await getSession()
        if (session?.accessToken) {
          config.headers.Authorization = `Bearer ${session.accessToken}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Response interceptor - Handle token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config

        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            // Queue the request if token refresh is in progress
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject })
            })
              .then((token) => {
                originalRequest.headers.Authorization = `Bearer ${token}`
                return this.client(originalRequest)
              })
              .catch((err) => Promise.reject(err))
          }

          originalRequest._retry = true
          this.isRefreshing = true

          try {
            const session = await getSession()
            if (session?.refreshToken) {
              const refreshResponse = await this.refreshToken(session.refreshToken)
              const newAccessToken = refreshResponse.data.access_token

              // Process the failed queue
              this.failedQueue.forEach(({ resolve }) => resolve(newAccessToken))
              this.failedQueue = []

              originalRequest.headers.Authorization = `Bearer ${newAccessToken}`
              return this.client(originalRequest)
            }
          } catch (refreshError) {
            this.failedQueue.forEach(({ reject }) => reject(refreshError))
            this.failedQueue = []
            signOut()
            return Promise.reject(refreshError)
          } finally {
            this.isRefreshing = false
          }
        }

        return Promise.reject(error)
      }
    )
  }

  private async refreshToken(refreshToken: string) {
    return this.client.post('/auth/refresh', { refresh_token: refreshToken })
  }

  // Generic request method
  private async request<T = any>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.client(config)
      return response.data.data
    } catch (error: any) {
      this.handleError(error)
      throw error
    }
  }

  private handleError(error: any) {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || 'An error occurred'
      console.error('API Error:', message, error.response.data)
      
      // Don't show toast for 401 errors (handled by interceptor)
      if (error.response.status !== 401) {
        toast.error(message)
      }
    } else if (error.request) {
      // Request was made but no response received
      console.error('Network Error:', error.request)
      toast.error('Network error. Please check your connection.')
    } else {
      // Something else happened
      console.error('Error:', error.message)
      toast.error('An unexpected error occurred')
    }
  }

  // HTTP Methods
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'GET', url })
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'POST', url, data })
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PUT', url, data })
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PATCH', url, data })
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'DELETE', url })
  }

  // File upload with progress
  async uploadFile<T = any>(
    url: string, 
    file: File, 
    onProgress?: (progress: number) => void
  ): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)

    return this.request<T>({
      method: 'POST',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })
  }
}

// Create and export singleton instance
export const apiClient = new ApiClient()

// Make it available globally for job polling
if (typeof window !== 'undefined') {
  (window as any).apiClient = apiClient
}

export default apiClient
```

### 2. API Types and Interfaces

Create `src/types/api.ts`:

```typescript
// Base interfaces
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  offset: number
  limit: number
}

export interface JobConfig {
  task_id: string
  configuration: Record<string, any>
}

export interface JobResponse {
  job_id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  created_at: string
}

// Job Status interfaces
export interface JobStatus {
  job_id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  steps: JobStep[]
  error_message?: string
  started_at?: string
  completed_at?: string
}

export interface JobStep {
  step_id: string
  title: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  content: string // Markdown format
  timestamp: string
  duration?: number
}

// Connector interfaces
export interface ConnectorResponse {
  id: string
  name: string
  type: 'source' | 'destination'
  status: 'connected' | 'disconnected' | 'error'
  configuration: Record<string, any>
  created_at: string
  updated_at: string
}

export interface ConnectorTestResponse {
  success: boolean
  message: string
  details?: any
}

// File upload interfaces
export interface FileUploadResponse {
  file_id: string
  filename: string
  size: number
  content_type: string
  upload_url?: string
}

export interface FilePreviewResponse {
  columns: string[]
  data: Record<string, any>[]
  total_rows: number
  preview_rows: number
}

// Business Value Questions interfaces
export interface BVQ {
  bvq_id: string
  bvq_content: string
  bvq_relevancy: 'none' | 'low' | 'medium' | 'high'
  bvq_purpose: string
  created_at: string
}

export interface BVQGenerationRequest {
  business_objective: string
}

export interface BVQGenerationResponse {
  bvqs: BVQ[]
}

// Data Model interfaces
export interface DataModel {
  data_model_id: string
  name: string
  description: string
  industry: string
  official_website?: string
  example_usage?: string
  explanation?: string
}

export interface DataModelSearchResponse {
  models: DataModel[]
  total: number
}

// Connection interfaces
export interface Connection {
  id: string
  name: string
  source_id: string
  destination_id: string
  status: 'active' | 'paused' | 'error'
  last_sync: string
  next_sync: string
  records_synced: number
  created_at: string
}

export interface ConnectionTestRequest {
  source_id: string
  destination_id: string
}

export interface ConnectionCreateRequest {
  name: string
  source_id: string
  destination_id: string
  source_type: 'database' | 'file'
  file_id?: string
  sync_frequency?: string
}
```

## Authentication Integration

### 1. Authentication Service

Create `src/lib/authService.ts`:

```typescript
import apiClient from './apiClient'
import type { User, LoginCredentials, SignupData, ForgotPasswordData, ResetPasswordData } from '@/types/auth'

export interface AuthResponse {
  user: User
  access_token: string
  refresh_token: string
  expires_in: number
}

export class AuthService {
  // Login
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    return apiClient.post<AuthResponse>('/auth/login', credentials)
  }

  // Register
  async register(data: SignupData): Promise<AuthResponse> {
    return apiClient.post<AuthResponse>('/auth/register', data)
  }

  // Forgot password
  async forgotPassword(data: ForgotPasswordData): Promise<{ message: string }> {
    return apiClient.post('/auth/forgot-password', data)
  }

  // Reset password
  async resetPassword(data: ResetPasswordData): Promise<{ message: string }> {
    return apiClient.post('/auth/reset-password', data)
  }

  // Change password
  async changePassword(currentPassword: string, newPassword: string): Promise<{ message: string }> {
    return apiClient.post('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword
    })
  }

  // Get user profile
  async getProfile(): Promise<User> {
    return apiClient.get<User>('/auth/profile')
  }

  // Update user profile
  async updateProfile(data: Partial<User>): Promise<User> {
    return apiClient.patch<User>('/auth/profile', data)
  }

  // Delete account
  async deleteAccount(): Promise<{ message: string }> {
    return apiClient.delete('/auth/account')
  }

  // Refresh token
  async refreshToken(refreshToken: string): Promise<{ access_token: string; refresh_token: string }> {
    return apiClient.post('/auth/refresh', { refresh_token: refreshToken })
  }

  // Logout
  async logout(): Promise<{ message: string }> {
    return apiClient.post('/auth/logout')
  }
}

export const authService = new AuthService()
```

## Async Job Management

### 1. Job Manager Service

Create `src/lib/jobManager.ts`:

```typescript
import apiClient from './apiClient'
import type { JobConfig, JobResponse, JobStatus } from '@/types/api'

export class JobManager {
  // Trigger async job
  async triggerJob(taskId: string, configuration: Record<string, any>): Promise<string> {
    const jobConfig: JobConfig = {
      task_id: taskId,
      configuration
    }

    const response = await apiClient.post<JobResponse>('/jobs/trigger', jobConfig)
    return response.job_id
  }

  // Get job status
  async getJobStatus(jobId: string): Promise<JobStatus> {
    return apiClient.get<JobStatus>(`/jobs/${jobId}/status`)
  }

  // Cancel job
  async cancelJob(jobId: string): Promise<{ message: string }> {
    return apiClient.post(`/jobs/${jobId}/cancel`)
  }

  // Get job logs
  async getJobLogs(jobId: string): Promise<{ logs: string[] }> {
    return apiClient.get(`/jobs/${jobId}/logs`)
  }

  // List user jobs
  async listJobs(status?: string, limit?: number, offset?: number): Promise<{
    jobs: JobStatus[]
    total: number
  }> {
    const params = new URLSearchParams()
    if (status) params.append('status', status)
    if (limit) params.append('limit', limit.toString())
    if (offset) params.append('offset', offset.toString())

    return apiClient.get(`/jobs?${params.toString()}`)
  }

  // Retry failed job
  async retryJob(jobId: string): Promise<string> {
    const response = await apiClient.post<JobResponse>(`/jobs/${jobId}/retry`)
    return response.job_id
  }
}

export const jobManager = new JobManager()

// Job polling utility
export class JobPoller {
  private intervals: Map<string, NodeJS.Timeout> = new Map()

  startPolling(
    jobId: string, 
    onUpdate: (status: JobStatus) => void,
    interval: number = 5000
  ) {
    // Don't start if already polling
    if (this.intervals.has(jobId)) {
      return
    }

    const poll = async () => {
      try {
        const status = await jobManager.getJobStatus(jobId)
        onUpdate(status)

        // Stop polling if job is complete
        if (status.status === 'completed' || status.status === 'failed') {
          this.stopPolling(jobId)
        }
      } catch (error) {
        console.error('Error polling job:', jobId, error)
        // Continue polling even on error
      }
    }

    // Initial poll
    poll()

    // Set up interval
    const intervalId = setInterval(poll, interval)
    this.intervals.set(jobId, intervalId)
  }

  stopPolling(jobId: string) {
    const intervalId = this.intervals.get(jobId)
    if (intervalId) {
      clearInterval(intervalId)
      this.intervals.delete(jobId)
    }
  }

  stopAllPolling() {
    this.intervals.forEach((intervalId) => clearInterval(intervalId))
    this.intervals.clear()
  }

  isPolling(jobId: string): boolean {
    return this.intervals.has(jobId)
  }
}

export const jobPoller = new JobPoller()

// Clean up intervals on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    jobPoller.stopAllPolling()
  })
}
```

### 2. React Hook for Job Management

Create `src/hooks/useJob.ts`:

```typescript
import { useState, useEffect, useCallback } from 'react'
import { jobManager, jobPoller } from '@/lib/jobManager'
import type { JobStatus } from '@/types/api'
import toast from 'react-hot-toast'

export interface UseJobOptions {
  autoStart?: boolean
  onComplete?: (status: JobStatus) => void
  onError?: (status: JobStatus) => void
  pollInterval?: number
}

export function useJob(jobId: string | null, options: UseJobOptions = {}) {
  const [status, setStatus] = useState<JobStatus | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const {
    autoStart = true,
    onComplete,
    onError,
    pollInterval = 5000
  } = options

  const startPolling = useCallback(() => {
    if (!jobId) return

    jobPoller.startPolling(
      jobId,
      (newStatus) => {
        setStatus(newStatus)
        
        if (newStatus.status === 'completed') {
          toast.success('Job completed successfully')
          onComplete?.(newStatus)
        } else if (newStatus.status === 'failed') {
          toast.error('Job failed')
          onError?.(newStatus)
          setError(newStatus.error_message || 'Job failed')
        }
      },
      pollInterval
    )
  }, [jobId, onComplete, onError, pollInterval])

  const stopPolling = useCallback(() => {
    if (jobId) {
      jobPoller.stopPolling(jobId)
    }
  }, [jobId])

  const cancelJob = useCallback(async () => {
    if (!jobId) return

    try {
      setIsLoading(true)
      await jobManager.cancelJob(jobId)
      stopPolling()
      toast.success('Job cancelled')
    } catch (error) {
      toast.error('Failed to cancel job')
    } finally {
      setIsLoading(false)
    }
  }, [jobId, stopPolling])

  const retryJob = useCallback(async () => {
    if (!jobId) return

    try {
      setIsLoading(true)
      setError(null)
      const newJobId = await jobManager.retryJob(jobId)
      // Start polling the new job
      if (newJobId !== jobId) {
        stopPolling()
        jobPoller.startPolling(newJobId, setStatus, pollInterval)
      }
      toast.success('Job restarted')
    } catch (error) {
      toast.error('Failed to retry job')
    } finally {
      setIsLoading(false)
    }
  }, [jobId, stopPolling, pollInterval])

  // Start polling when job ID is available
  useEffect(() => {
    if (jobId && autoStart) {
      startPolling()
    }

    return () => {
      if (jobId) {
        jobPoller.stopPolling(jobId)
      }
    }
  }, [jobId, autoStart, startPolling])

  return {
    status,
    isLoading,
    error,
    startPolling,
    stopPolling,
    cancelJob,
    retryJob,
    isPolling: jobId ? jobPoller.isPolling(jobId) : false
  }
}
```

## Connectors API

### 1. Connectors Service

Create `src/lib/connectorsService.ts`:

```typescript
import apiClient from './apiClient'
import type { ConnectorResponse, ConnectorTestResponse, PaginatedResponse } from '@/types/api'

export interface ConnectorCreateRequest {
  name: string
  type: 'source' | 'destination'
  connector_type: string // 'postgresql', 'mysql', 'github', etc.
  configuration: Record<string, any>
}

export interface ConnectorUpdateRequest {
  name?: string
  configuration?: Record<string, any>
}

export class ConnectorsService {
  // List connectors
  async listConnectors(
    type?: 'source' | 'destination',
    limit: number = 20,
    offset: number = 0
  ): Promise<PaginatedResponse<ConnectorResponse>> {
    const params = new URLSearchParams()
    if (type) params.append('type', type)
    params.append('limit', limit.toString())
    params.append('offset', offset.toString())

    return apiClient.get(`/connectors?${params.toString()}`)
  }

  // Get connector by ID
  async getConnector(connectorId: string): Promise<ConnectorResponse> {
    return apiClient.get(`/connectors/${connectorId}`)
  }

  // Create connector
  async createConnector(data: ConnectorCreateRequest): Promise<ConnectorResponse> {
    return apiClient.post('/connectors', data)
  }

  // Update connector
  async updateConnector(
    connectorId: string, 
    data: ConnectorUpdateRequest
  ): Promise<ConnectorResponse> {
    return apiClient.patch(`/connectors/${connectorId}`, data)
  }

  // Delete connector
  async deleteConnector(connectorId: string): Promise<{ message: string }> {
    return apiClient.delete(`/connectors/${connectorId}`)
  }

  // Test connector connection
  async testConnector(connectorId: string): Promise<ConnectorTestResponse> {
    return apiClient.post(`/connectors/${connectorId}/test`)
  }

  // Get available connector types
  async getConnectorTypes(): Promise<{
    sources: Array<{
      type: string
      name: string
      description: string
      icon: string
      category: string
      config_schema: Record<string, any>
    }>
    destinations: Array<{
      type: string
      name: string
      description: string
      icon: string
      category: string
      config_schema: Record<string, any>
    }>
  }> {
    return apiClient.get('/connectors/types')
  }

  // Get connector configuration schema
  async getConnectorSchema(connectorType: string): Promise<{
    schema: Record<string, any>
    ui_schema?: Record<string, any>
  }> {
    return apiClient.get(`/connectors/types/${connectorType}/schema`)
  }
}

export const connectorsService = new ConnectorsService()
```

### 2. Connector Configuration Hook

Create `src/hooks/useConnector.ts`:

```typescript
import { useState, useEffect } from 'react'
import { connectorsService } from '@/lib/connectorsService'
import type { ConnectorResponse } from '@/types/api'
import toast from 'react-hot-toast'

export function useConnector(connectorId: string | null) {
  const [connector, setConnector] = useState<ConnectorResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchConnector = async () => {
    if (!connectorId) return

    try {
      setIsLoading(true)
      setError(null)
      const data = await connectorsService.getConnector(connectorId)
      setConnector(data)
    } catch (error: any) {
      setError(error.message || 'Failed to fetch connector')
    } finally {
      setIsLoading(false)
    }
  }

  const testConnection = async () => {
    if (!connectorId) return false

    try {
      setIsLoading(true)
      const result = await connectorsService.testConnector(connectorId)
      
      if (result.success) {
        toast.success('Connection test successful')
        return true
      } else {
        toast.error(result.message || 'Connection test failed')
        return false
      }
    } catch (error: any) {
      toast.error(error.message || 'Connection test failed')
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const updateConnector = async (data: any) => {
    if (!connectorId) return

    try {
      setIsLoading(true)
      const updated = await connectorsService.updateConnector(connectorId, data)
      setConnector(updated)
      toast.success('Connector updated successfully')
      return updated
    } catch (error: any) {
      toast.error(error.message || 'Failed to update connector')
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchConnector()
  }, [connectorId])

  return {
    connector,
    isLoading,
    error,
    testConnection,
    updateConnector,
    refetch: fetchConnector
  }
}
```

## Ingestion Pipelines API

### 1. Ingestion Service

Create `src/lib/ingestionService.ts`:

```typescript
import apiClient from './apiClient'
import type { Connection, ConnectionCreateRequest, ConnectionTestRequest, PaginatedResponse } from '@/types/api'

export interface IngestionCreateRequest extends ConnectionCreateRequest {
  schedule?: {
    frequency: 'manual' | 'hourly' | 'daily' | 'weekly'
    cron_expression?: string
  }
  streams?: Array<{
    stream_name: string
    sync_mode: 'full_refresh' | 'incremental'
    destination_name?: string
  }>
}

export interface IngestionUpdateRequest {
  name?: string
  schedule?: {
    frequency: 'manual' | 'hourly' | 'daily' | 'weekly'
    cron_expression?: string
  }
  streams?: Array<{
    stream_name: string
    sync_mode: 'full_refresh' | 'incremental'
    destination_name?: string
  }>
}

export interface SyncResponse {
  job_id: string
  message: string
}

export class IngestionService {
  // List ingestion pipelines
  async listIngestions(
    limit: number = 20,
    offset: number = 0
  ): Promise<PaginatedResponse<Connection>> {
    const params = new URLSearchParams()
    params.append('limit', limit.toString())
    params.append('offset', offset.toString())

    return apiClient.get(`/ingestions?${params.toString()}`)
  }

  // Get ingestion by ID
  async getIngestion(ingestionId: string): Promise<Connection> {
    return apiClient.get(`/ingestions/${ingestionId}`)
  }

  // Create ingestion pipeline
  async createIngestion(data: IngestionCreateRequest): Promise<Connection> {
    return apiClient.post('/ingestions', data)
  }

  // Update ingestion pipeline
  async updateIngestion(
    ingestionId: string,
    data: IngestionUpdateRequest
  ): Promise<Connection> {
    return apiClient.patch(`/ingestions/${ingestionId}`, data)
  }

  // Delete ingestion pipeline
  async deleteIngestion(ingestionId: string): Promise<{ message: string }> {
    return apiClient.delete(`/ingestions/${ingestionId}`)
  }

  // Test connection between source and destination
  async testConnection(data: ConnectionTestRequest): Promise<{
    success: boolean
    message: string
    details?: any
  }> {
    return apiClient.post('/ingestions/test', data)
  }

  // Trigger manual sync
  async triggerSync(ingestionId: string): Promise<SyncResponse> {
    return apiClient.post(`/ingestions/${ingestionId}/sync`)
  }

  // Pause/Resume ingestion
  async pauseIngestion(ingestionId: string): Promise<{ message: string }> {
    return apiClient.post(`/ingestions/${ingestionId}/pause`)
  }

  async resumeIngestion(ingestionId: string): Promise<{ message: string }> {
    return apiClient.post(`/ingestions/${ingestionId}/resume`)
  }

  // Get sync history
  async getSyncHistory(
    ingestionId: string,
    limit: number = 20,
    offset: number = 0
  ): Promise<PaginatedResponse<{
    job_id: string
    status: string
    started_at: string
    completed_at?: string
    records_synced: number
    error_message?: string
  }>> {
    const params = new URLSearchParams()
    params.append('limit', limit.toString())
    params.append('offset', offset.toString())

    return apiClient.get(`/ingestions/${ingestionId}/history?${params.toString()}`)
  }

  // Discover schema from source
  async discoverSchema(sourceId: string): Promise<{
    streams: Array<{
      name: string
      json_schema: Record<string, any>
      supported_sync_modes: string[]
      source_defined_cursor?: boolean
      default_cursor_field?: string[]
      source_defined_primary_key?: string[][]
    }>
  }> {
    return apiClient.post(`/ingestions/discover`, { source_id: sourceId })
  }
}

export const ingestionService = new IngestionService()
```

## Pipeline Migration API

### 1. Migration Service

Create `src/lib/migrationService.ts`:

```typescript
import apiClient from './apiClient'
import { jobManager } from './jobManager'

export interface MigrationTask {
  id: string
  name: string
  source_id: string
  target_branch: string
  status: 'pending' | 'exploring' | 'ready_for_generation' | 'generating' | 'completed' | 'failed'
  created_at: string
  updated_at: string
}

export interface ExplorationRequest {
  source_id: string
  source_branch: string
  target_branch: string
}

export interface ExplorationResult {
  repository_info: {
    name: string
    language: string
    framework?: string
    total_files: number
  }
  transactions: Array<{
    transaction_id: string
    name: string
    description: string
    complexity: 'low' | 'medium' | 'high'
    file_path: string
    dependencies: string[]
    estimated_effort_hours: number
  }>
  summary: {
    total_transactions: number
    complexity_breakdown: {
      low: number
      medium: number
      high: number
    }
    estimated_total_effort: number
  }
}

export interface CodeGenerationRequest {
  task_id: string
  selected_transactions: string[]
  target_format: 'databricks_pyspark' | 'python_pandas'
  target_branch: string
}

export interface CodeGenerationResult {
  generated_files: Array<{
    file_path: string
    content: string
    transaction_id: string
  }>
  pull_request: {
    url: string
    number: number
    status: 'open' | 'merged' | 'closed'
  }
  summary: {
    files_generated: number
    lines_of_code: number
  }
}

export class MigrationService {
  // Create migration task
  async createMigrationTask(data: {
    name: string
    source_id: string
    target_branch: string
  }): Promise<MigrationTask> {
    return apiClient.post('/migration/tasks', data)
  }

  // List migration tasks
  async listMigrationTasks(): Promise<{ tasks: MigrationTask[] }> {
    return apiClient.get('/migration/tasks')
  }

  // Get migration task
  async getMigrationTask(taskId: string): Promise<MigrationTask> {
    return apiClient.get(`/migration/tasks/${taskId}`)
  }

  // Delete migration task
  async deleteMigrationTask(taskId: string): Promise<{ message: string }> {
    return apiClient.delete(`/migration/tasks/${taskId}`)
  }

  // Trigger repository exploration
  async triggerExploration(taskId: string, data: ExplorationRequest): Promise<string> {
    return jobManager.triggerJob('pipeline_migration_exploration', {
      task_id: taskId,
      ...data
    })
  }

  // Get exploration results
  async getExplorationResults(taskId: string): Promise<ExplorationResult> {
    return apiClient.get(`/migration/tasks/${taskId}/exploration`)
  }

  // Trigger code generation
  async triggerCodeGeneration(data: CodeGenerationRequest): Promise<string> {
    return jobManager.triggerJob('pipeline_migration_codegen', data)
  }

  // Get code generation results
  async getCodeGenerationResults(taskId: string): Promise<CodeGenerationResult> {
    return apiClient.get(`/migration/tasks/${taskId}/generation`)
  }

  // Update task status
  async updateTaskStatus(taskId: string, status: string): Promise<MigrationTask> {
    return apiClient.patch(`/migration/tasks/${taskId}`, { status })
  }

  // Get available repositories from source
  async getRepositories(sourceId: string): Promise<{
    repositories: Array<{
      name: string
      full_name: string
      description?: string
      language: string
      default_branch: string
    }>
  }> {
    return apiClient.get(`/migration/sources/${sourceId}/repositories`)
  }

  // Get branches from repository
  async getBranches(sourceId: string, repository: string): Promise<{
    branches: Array<{
      name: string
      commit_sha: string
      protected: boolean
    }>
  }> {
    return apiClient.get(`/migration/sources/${sourceId}/repositories/${repository}/branches`)
  }
}

export const migrationService = new MigrationService()
```

### 2. Migration Hook

Create `src/hooks/useMigration.ts`:

```typescript
import { useState, useEffect } from 'react'
import { migrationService } from '@/lib/migrationService'
import { useJob } from './useJob'
import type { MigrationTask, ExplorationResult, CodeGenerationResult } from '@/lib/migrationService'
import toast from 'react-hot-toast'

export function useMigration(taskId: string | null) {
  const [task, setTask] = useState<MigrationTask | null>(null)
  const [explorationResults, setExplorationResults] = useState<ExplorationResult | null>(null)
  const [codeGenerationResults, setCodeGenerationResults] = useState<CodeGenerationResult | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [explorationJobId, setExplorationJobId] = useState<string | null>(null)
  const [codeGenJobId, setCodeGenJobId] = useState<string | null>(null)

  // Job hooks for monitoring
  const explorationJob = useJob(explorationJobId, {
    onComplete: async () => {
      if (taskId) {
        try {
          const results = await migrationService.getExplorationResults(taskId)
          setExplorationResults(results)
          await migrationService.updateTaskStatus(taskId, 'ready_for_generation')
          fetchTask()
        } catch (error) {
          toast.error('Failed to fetch exploration results')
        }
      }
    },
    onError: () => {
      toast.error('Repository exploration failed')
    }
  })

  const codeGenJob = useJob(codeGenJobId, {
    onComplete: async () => {
      if (taskId) {
        try {
          const results = await migrationService.getCodeGenerationResults(taskId)
          setCodeGenerationResults(results)
          await migrationService.updateTaskStatus(taskId, 'completed')
          fetchTask()
        } catch (error) {
          toast.error('Failed to fetch code generation results')
        }
      }
    },
    onError: () => {
      toast.error('Code generation failed')
    }
  })

  const fetchTask = async () => {
    if (!taskId) return

    try {
      setIsLoading(true)
      const taskData = await migrationService.getMigrationTask(taskId)
      setTask(taskData)

      // Fetch results if available
      if (taskData.status === 'ready_for_generation' || taskData.status === 'completed') {
        try {
          const exploration = await migrationService.getExplorationResults(taskId)
          setExplorationResults(exploration)
        } catch (error) {
          // Results not available yet
        }
      }

      if (taskData.status === 'completed') {
        try {
          const codeGen = await migrationService.getCodeGenerationResults(taskId)
          setCodeGenerationResults(codeGen)
        } catch (error) {
          // Results not available yet
        }
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch migration task')
    } finally {
      setIsLoading(false)
    }
  }

  const triggerExploration = async (data: {
    source_id: string
    source_branch: string
    target_branch: string
  }) => {
    if (!taskId) return

    try {
      setIsLoading(true)
      const jobId = await migrationService.triggerExploration(taskId, data)
      setExplorationJobId(jobId)
      await migrationService.updateTaskStatus(taskId, 'exploring')
      toast.success('Repository exploration started')
    } catch (error: any) {
      toast.error(error.message || 'Failed to start exploration')
    } finally {
      setIsLoading(false)
    }
  }

  const triggerCodeGeneration = async (data: {
    selected_transactions: string[]
    target_format: 'databricks_pyspark' | 'python_pandas'
    target_branch: string
  }) => {
    if (!taskId) return

    try {
      setIsLoading(true)
      const jobId = await migrationService.triggerCodeGeneration({
        task_id: taskId,
        ...data
      })
      setCodeGenJobId(jobId)
      await migrationService.updateTaskStatus(taskId, 'generating')
      toast.success('Code generation started')
    } catch (error: any) {
      toast.error(error.message || 'Failed to start code generation')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchTask()
  }, [taskId])

  return {
    task,
    explorationResults,
    codeGenerationResults,
    isLoading,
    explorationJob: explorationJob.status,
    codeGenJob: codeGenJob.status,
    triggerExploration,
    triggerCodeGeneration,
    refetch: fetchTask
  }
}
```

## Business Objectives API

### 1. Business Objectives Service

Create `src/lib/objectivesService.ts`:

```typescript
import apiClient from './apiClient'
import { jobManager } from './jobManager'
import type { BVQ, BVQGenerationRequest, BVQGenerationResponse } from '@/types/api'

export interface BusinessObjectiveTask {
  id: string
  name: string
  business_objective: string
  destination_id: string
  repository_id: string
  target_branch: string
  status: 'pending' | 'bvq_generation' | 'data_exploration' | 'code_generation' | 'completed' | 'failed'
  current_step: number
  total_steps: number
  created_at: string
  updated_at: string
}

export interface DataExplorationRequest {
  task_id: string
  pre_exploration_bvqs: BVQ[]
}

export interface DataExplorationResult {
  post_exploration_bvqs: BVQ[]
  data_insights: {
    dataset_summary: {
      total_tables: number
      total_columns: number
      total_rows: number
      data_quality_score: number
    }
    relevant_tables: Array<{
      table_name: string
      relevance_score: number
      column_count: number
      sample_data: Record<string, any>[]
    }>
  }
}

export interface BusinessCodeGenerationRequest {
  task_id: string
  post_exploration_bvqs: BVQ[]
  target_format: 'databricks_pyspark' | 'python_pandas'
}

export interface BusinessCodeGenerationResult {
  generated_scripts: Array<{
    script_name: string
    content: string
    bvq_ids: string[]
    description: string
  }>
  pull_request: {
    url: string
    number: number
    status: 'open' | 'merged' | 'closed'
  }
  analytics_dashboard: {
    url?: string
    components: Array<{
      type: 'chart' | 'table' | 'metric'
      title: string
      description: string
    }>
  }
}

export class ObjectivesService {
  // Create business objective task
  async createObjectiveTask(data: {
    name: string
    destination_id: string
    repository_id: string
    target_branch: string
  }): Promise<BusinessObjectiveTask> {
    return apiClient.post('/objectives/tasks', data)
  }

  // List objective tasks
  async listObjectiveTasks(): Promise<{ tasks: BusinessObjectiveTask[] }> {
    return apiClient.get('/objectives/tasks')
  }

  // Get objective task
  async getObjectiveTask(taskId: string): Promise<BusinessObjectiveTask> {
    return apiClient.get(`/objectives/tasks/${taskId}`)
  }

  // Delete objective task
  async deleteObjectiveTask(taskId: string): Promise<{ message: string }> {
    return apiClient.delete(`/objectives/tasks/${taskId}`)
  }

  // Generate BVQs from business objective
  async generateBVQs(data: BVQGenerationRequest): Promise<BVQGenerationResponse> {
    return apiClient.post('/objectives/generate-bvqs', data)
  }

  // Save business objective and BVQs
  async saveBusinessObjective(taskId: string, data: {
    business_objective: string
    pre_exploration_bvqs: BVQ[]
  }): Promise<BusinessObjectiveTask> {
    return apiClient.patch(`/objectives/tasks/${taskId}`, data)
  }

  // Trigger data exploration
  async triggerDataExploration(data: DataExplorationRequest): Promise<string> {
    return jobManager.triggerJob('b_obj_pre_exploration_bvqs', data)
  }

  // Get data exploration results
  async getDataExplorationResults(taskId: string): Promise<DataExplorationResult> {
    return apiClient.get(`/objectives/tasks/${taskId}/exploration`)
  }

  // Save post-exploration BVQs
  async savePostExplorationBVQs(taskId: string, bvqs: BVQ[]): Promise<BusinessObjectiveTask> {
    return apiClient.patch(`/objectives/tasks/${taskId}`, {
      post_exploration_bvqs: bvqs
    })
  }

  // Trigger code generation
  async triggerCodeGeneration(data: BusinessCodeGenerationRequest): Promise<string> {
    return jobManager.triggerJob('b_obj_code_generation', data)
  }

  // Get code generation results
  async getCodeGenerationResults(taskId: string): Promise<BusinessCodeGenerationResult> {
    return apiClient.get(`/objectives/tasks/${taskId}/generation`)
  }

  // Update task status and step
  async updateTaskProgress(taskId: string, data: {
    status?: string
    current_step?: number
  }): Promise<BusinessObjectiveTask> {
    return apiClient.patch(`/objectives/tasks/${taskId}`, data)
  }
}

export const objectivesService = new ObjectivesService()
```

## Data Model Mapping API

### 1. Data Model Mapping Service

Create `src/lib/mappingService.ts`:

```typescript
import apiClient from './apiClient'
import { jobManager } from './jobManager'
import type { DataModel, DataModelSearchResponse } from '@/types/api'

export interface DataMappingTask {
  id: string
  name: string
  destination_id: string
  repository_id: string
  target_branch: string
  selected_model_id?: string
  status: 'pending' | 'model_selection' | 'relevancy_analysis' | 'code_generation' | 'completed' | 'failed'
  current_step: number
  total_steps: number
  created_at: string
  updated_at: string
}

export interface RelevancyAnalysisRequest {
  task_id: string
  data_model_id: string
}

export interface RelevancyAnalysisResult {
  overall_relevancy_score: number
  compatibility_assessment: {
    can_adapt: boolean
    adaptation_effort: 'low' | 'medium' | 'high'
    missing_fields: string[]
    conflicting_fields: string[]
  }
  field_mappings: Array<{
    source_field: string
    target_field: string
    mapping_confidence: number
    requires_transformation: boolean
    transformation_note?: string
  }>
  recommendations: Array<{
    type: 'required' | 'suggested' | 'optional'
    action: string
    description: string
    impact: 'low' | 'medium' | 'high'
  }>
  next_steps: string[]
}

export interface DataMappingCodeGenerationRequest {
  task_id: string
  confirmed_mappings: Array<{
    source_field: string
    target_field: string
    transformation?: string
  }>
  target_format: 'databricks_pyspark' | 'python_pandas'
}

export interface DataMappingCodeGenerationResult {
  mapping_scripts: Array<{
    script_name: string
    content: string
    description: string
    tables_involved: string[]
  }>
  validation_scripts: Array<{
    script_name: string
    content: string
    description: string
  }>
  pull_request: {
    url: string
    number: number
    status: 'open' | 'merged' | 'closed'
  }
  compliance_report: {
    compliance_percentage: number
    standard_coverage: number
    missing_requirements: string[]
  }
}

export class MappingService {
  // Create data mapping task
  async createMappingTask(data: {
    name: string
    destination_id: string
    repository_id: string
    target_branch: string
  }): Promise<DataMappingTask> {
    return apiClient.post('/mapping/tasks', data)
  }

  // List mapping tasks
  async listMappingTasks(): Promise<{ tasks: DataMappingTask[] }> {
    return apiClient.get('/mapping/tasks')
  }

  // Get mapping task
  async getMappingTask(taskId: string): Promise<DataMappingTask> {
    return apiClient.get(`/mapping/tasks/${taskId}`)
  }

  // Delete mapping task
  async deleteMappingTask(taskId: string): Promise<{ message: string }> {
    return apiClient.delete(`/mapping/tasks/${taskId}`)
  }

  // Search data models
  async searchDataModels(query: string, filters?: {
    industry?: string
    category?: string
  }): Promise<DataModelSearchResponse> {
    const params = new URLSearchParams()
    params.append('q', query)
    if (filters?.industry) params.append('industry', filters.industry)
    if (filters?.category) params.append('category', filters.category)

    return apiClient.get(`/mapping/data-models/search?${params.toString()}`)
  }

  // Get data model details
  async getDataModel(modelId: string): Promise<DataModel & {
    schema: Record<string, any>
    example_usage: string
    documentation_url?: string
  }> {
    return apiClient.get(`/mapping/data-models/${modelId}`)
  }

  // Get available industries and categories
  async getDataModelFilters(): Promise<{
    industries: string[]
    categories: string[]
  }> {
    return apiClient.get('/mapping/data-models/filters')
  }

  // Save selected data model
  async saveSelectedModel(taskId: string, modelId: string): Promise<DataMappingTask> {
    return apiClient.patch(`/mapping/tasks/${taskId}`, {
      selected_model_id: modelId
    })
  }

  // Trigger relevancy analysis
  async triggerRelevancyAnalysis(data: RelevancyAnalysisRequest): Promise<string> {
    return jobManager.triggerJob('data_model_relevancy_analysis', data)
  }

  // Get relevancy analysis results
  async getRelevancyAnalysisResults(taskId: string): Promise<RelevancyAnalysisResult> {
    return apiClient.get(`/mapping/tasks/${taskId}/relevancy`)
  }

  // Trigger mapping code generation
  async triggerMappingCodeGeneration(data: DataMappingCodeGenerationRequest): Promise<string> {
    return jobManager.triggerJob('data_mapping_code_generation', data)
  }

  // Get mapping code generation results
  async getMappingCodeGenerationResults(taskId: string): Promise<DataMappingCodeGenerationResult> {
    return apiClient.get(`/mapping/tasks/${taskId}/generation`)
  }

  // Update task progress
  async updateTaskProgress(taskId: string, data: {
    status?: string
    current_step?: number
  }): Promise<DataMappingTask> {
    return apiClient.patch(`/mapping/tasks/${taskId}`, data)
  }
}

export const mappingService = new MappingService()
```

## File Upload & Management

### 1. File Upload Service

Create `src/lib/fileService.ts`:

```typescript
import apiClient from './apiClient'
import type { FileUploadResponse, FilePreviewResponse } from '@/types/api'

export interface FileUploadProgress {
  loaded: number
  total: number
  percentage: number
}

export class FileService {
  // Upload file with progress tracking
  async uploadFile(
    file: File,
    onProgress?: (progress: FileUploadProgress) => void
  ): Promise<FileUploadResponse> {
    // Validate file size (50MB limit)
    const maxSize = 50 * 1024 * 1024 // 50MB
    if (file.size > maxSize) {
      throw new Error('File size must be less than 50MB')
    }

    // Validate file type
    const allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]
    
    if (!allowedTypes.includes(file.type)) {
      throw new Error('Only CSV and Excel files are supported')
    }

    return apiClient.uploadFile('/files/upload', file, (percentage) => {
      if (onProgress) {
        onProgress({
          loaded: (file.size * percentage) / 100,
          total: file.size,
          percentage
        })
      }
    })
  }

  // Get file preview
  async getFilePreview(fileId: string, rows: number = 10): Promise<FilePreviewResponse> {
    return apiClient.get(`/files/${fileId}/preview?n=${rows}`)
  }

  // Get file info
  async getFileInfo(fileId: string): Promise<{
    file_id: string
    filename: string
    size: number
    content_type: string
    upload_date: string
    processed: boolean
    row_count?: number
    column_count?: number
  }> {
    return apiClient.get(`/files/${fileId}`)
  }

  // Delete file
  async deleteFile(fileId: string): Promise<{ message: string }> {
    return apiClient.delete(`/files/${fileId}`)
  }

  // List user files
  async listFiles(limit: number = 20, offset: number = 0): Promise<{
    files: Array<{
      file_id: string
      filename: string
      size: number
      content_type: string
      upload_date: string
      processed: boolean
    }>
    total: number
  }> {
    const params = new URLSearchParams()
    params.append('limit', limit.toString())
    params.append('offset', offset.toString())

    return apiClient.get(`/files?${params.toString()}`)
  }

  // Validate file before upload
  validateFile(file: File): { valid: boolean; error?: string } {
    const maxSize = 50 * 1024 * 1024 // 50MB
    
    if (file.size > maxSize) {
      return { valid: false, error: 'File size must be less than 50MB' }
    }

    const allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]
    
    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'Only CSV and Excel files are supported' }
    }

    return { valid: true }
  }
}

export const fileService = new FileService()
```

### 2. File Upload Hook

Create `src/hooks/useFileUpload.ts`:

```typescript
import { useState, useCallback } from 'react'
import { fileService } from '@/lib/fileService'
import type { FileUploadProgress, FileUploadResponse, FilePreviewResponse } from '@/lib/fileService'
import toast from 'react-hot-toast'

export function useFileUpload() {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<FileUploadProgress | null>(null)
  const [uploadedFile, setUploadedFile] = useState<FileUploadResponse | null>(null)
  const [previewData, setPreviewData] = useState<FilePreviewResponse | null>(null)
  const [error, setError] = useState<string | null>(null)

  const uploadFile = useCallback(async (file: File) => {
    // Validate file
    const validation = fileService.validateFile(file)
    if (!validation.valid) {
      setError(validation.error || 'Invalid file')
      toast.error(validation.error || 'Invalid file')
      return null
    }

    try {
      setIsUploading(true)
      setError(null)
      setUploadProgress({ loaded: 0, total: file.size, percentage: 0 })

      const result = await fileService.uploadFile(file, (progress) => {
        setUploadProgress(progress)
      })

      setUploadedFile(result)
      toast.success('File uploaded successfully')
      
      // Automatically load preview
      try {
        const preview = await fileService.getFilePreview(result.file_id, 10)
        setPreviewData(preview)
      } catch (previewError) {
        console.warn('Failed to load file preview:', previewError)
      }

      return result
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to upload file'
      setError(errorMessage)
      toast.error(errorMessage)
      return null
    } finally {
      setIsUploading(false)
      setUploadProgress(null)
    }
  }, [])

  const loadPreview = useCallback(async (fileId: string, rows: number = 10) => {
    try {
      const preview = await fileService.getFilePreview(fileId, rows)
      setPreviewData(preview)
      return preview
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to load preview'
      setError(errorMessage)
      toast.error(errorMessage)
      return null
    }
  }, [])

  const clearFile = useCallback(() => {
    setUploadedFile(null)
    setPreviewData(null)
    setError(null)
    setUploadProgress(null)
  }, [])

  return {
    isUploading,
    uploadProgress,
    uploadedFile,
    previewData,
    error,
    uploadFile,
    loadPreview,
    clearFile
  }
}
```

## Form Checkpoint Management

### 1. Form Checkpoint Service

Create `src/lib/checkpointService.ts`:

```typescript
import apiClient from './apiClient'

export interface FormCheckpoint {
  task_id: string
  checkpoint_data: Record<string, any>
  created_at: string
  updated_at: string
}

export class CheckpointService {
  // Save form checkpoint
  async saveCheckpoint(taskId: string, data: Record<string, any>): Promise<FormCheckpoint> {
    return apiClient.post('/checkpoints', {
      task_id: taskId,
      checkpoint_data: data
    })
  }

  // Load form checkpoint
  async loadCheckpoint(taskId: string): Promise<FormCheckpoint | null> {
    try {
      return await apiClient.get(`/checkpoints/${taskId}`)
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null // No checkpoint exists
      }
      throw error
    }
  }

  // Delete form checkpoint
  async deleteCheckpoint(taskId: string): Promise<{ message: string }> {
    return apiClient.delete(`/checkpoints/${taskId}`)
  }

  // List all checkpoints for user
  async listCheckpoints(): Promise<{ checkpoints: FormCheckpoint[] }> {
    return apiClient.get('/checkpoints')
  }
}

export const checkpointService = new CheckpointService()
```

### 2. Form Checkpoint Hook

Create `src/hooks/useFormCheckpoint.ts`:

```typescript
import { useState, useEffect, useCallback } from 'react'
import { checkpointService } from '@/lib/checkpointService'
import { useDebounce } from './useDebounce'
import toast from 'react-hot-toast'

export function useFormCheckpoint<T = Record<string, any>>(
  taskId: string | null,
  initialData: T
) {
  const [formData, setFormData] = useState<T>(initialData)
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  // Debounce form data changes to avoid too frequent saves
  const debouncedFormData = useDebounce(formData, 2000)

  // Load checkpoint on mount
  useEffect(() => {
    if (!taskId) return

    const loadCheckpoint = async () => {
      try {
        setIsLoading(true)
        const checkpoint = await checkpointService.loadCheckpoint(taskId)
        
        if (checkpoint) {
          setFormData(checkpoint.checkpoint_data as T)
          setLastSaved(new Date(checkpoint.updated_at))
        }
      } catch (error: any) {
        console.error('Failed to load checkpoint:', error)
        // Don't show error toast for missing checkpoints
      } finally {
        setIsLoading(false)
      }
    }

    loadCheckpoint()
  }, [taskId])

  // Auto-save when form data changes
  useEffect(() => {
    if (!taskId || isLoading) return

    const saveCheckpoint = async () => {
      try {
        setIsSaving(true)
        await checkpointService.saveCheckpoint(taskId, formData as Record<string, any>)
        setLastSaved(new Date())
      } catch (error: any) {
        console.error('Failed to save checkpoint:', error)
        toast.error('Failed to save progress')
      } finally {
        setIsSaving(false)
      }
    }

    saveCheckpoint()
  }, [debouncedFormData, taskId, isLoading])

  // Manual save function
  const saveNow = useCallback(async () => {
    if (!taskId) return false

    try {
      setIsSaving(true)
      await checkpointService.saveCheckpoint(taskId, formData as Record<string, any>)
      setLastSaved(new Date())
      toast.success('Progress saved')
      return true
    } catch (error: any) {
      toast.error('Failed to save progress')
      return false
    } finally {
      setIsSaving(false)
    }
  }, [taskId, formData])

  // Clear checkpoint
  const clearCheckpoint = useCallback(async () => {
    if (!taskId) return

    try {
      await checkpointService.deleteCheckpoint(taskId)
      setFormData(initialData)
      setLastSaved(null)
      toast.success('Progress cleared')
    } catch (error: any) {
      toast.error('Failed to clear progress')
    }
  }, [taskId, initialData])

  // Update form data
  const updateFormData = useCallback((updates: Partial<T> | ((prev: T) => T)) => {
    setFormData(prev => {
      if (typeof updates === 'function') {
        return updates(prev)
      }
      return { ...prev, ...updates }
    })
  }, [])

  return {
    formData,
    isLoading,
    isSaving,
    lastSaved,
    updateFormData,
    saveNow,
    clearCheckpoint
  }
}

// Debounce hook
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

export { useDebounce }
```

## Error Handling

### 1. Global Error Handler

Create `src/lib/errorHandler.ts`:

```typescript
import toast from 'react-hot-toast'

export interface AppError {
  message: string
  code?: string
  status?: number
  details?: any
}

export class ErrorHandler {
  static handle(error: any, context?: string): AppError {
    let appError: AppError

    if (error.response) {
      // API error response
      appError = {
        message: error.response.data?.message || 'An error occurred',
        code: error.response.data?.code,
        status: error.response.status,
        details: error.response.data
      }
    } else if (error.request) {
      // Network error
      appError = {
        message: 'Network error. Please check your connection.',
        code: 'NETWORK_ERROR'
      }
    } else {
      // Other error
      appError = {
        message: error.message || 'An unexpected error occurred',
        code: 'UNKNOWN_ERROR'
      }
    }

    // Log error for debugging
    console.error(`Error${context ? ` in ${context}` : ''}:`, error)

    // Show user-friendly message
    this.showUserError(appError, context)

    return appError
  }

  static showUserError(error: AppError, context?: string) {
    const userMessage = this.getUserFriendlyMessage(error, context)
    
    if (error.status === 401) {
      // Don't show toast for auth errors (handled by interceptor)
      return
    }

    if (error.status && error.status >= 500) {
      toast.error(userMessage, { duration: 6000 })
    } else {
      toast.error(userMessage)
    }
  }

  static getUserFriendlyMessage(error: AppError, context?: string): string {
    // Map common errors to user-friendly messages
    switch (error.code) {
      case 'NETWORK_ERROR':
        return 'Please check your internet connection and try again.'
      
      case 'TIMEOUT':
        return 'The request timed out. Please try again.'
      
      case 'INVALID_FILE_TYPE':
        return 'Please upload a valid CSV or Excel file.'
      
      case 'FILE_TOO_LARGE':
        return 'File is too large. Maximum size is 50MB.'
      
      case 'CONNECTOR_TEST_FAILED':
        return 'Connection test failed. Please check your configuration.'
      
      case 'JOB_FAILED':
        return 'The operation failed. Please try again or contact support.'
      
      case 'INSUFFICIENT_PERMISSIONS':
        return 'You don\'t have permission to perform this action.'
      
      default:
        // Use the original message or a generic one
        if (error.message && error.message.length < 100) {
          return error.message
        }
        
        return context 
          ? `Failed to ${context}. Please try again.`
          : 'Something went wrong. Please try again.'
    }
  }

  // Retry wrapper for network operations
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: any

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        
        // Don't retry on client errors (4xx)
        if (error.response?.status >= 400 && error.response?.status < 500) {
          throw error
        }

        // Don't retry on last attempt
        if (attempt === maxRetries) {
          break
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay * attempt))
      }
    }

    throw lastError
  }
}

// Global error event handler
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error)
  })

  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    ErrorHandler.handle(event.reason, 'unhandled promise')
  })
}

export default ErrorHandler
```

## Testing & Debugging

### 1. API Mock Service for Development

Create `src/lib/mockApiService.ts`:

```typescript
// Mock service for development and testing
export class MockApiService {
  private static delay(ms: number = 1000) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  static async mockJobProgress(jobId: string, totalSteps: number = 4) {
    const steps = [
      'Initializing...',
      'Processing data...',
      'Generating results...',
      'Finalizing...'
    ]

    for (let i = 0; i < totalSteps; i++) {
      await this.delay(2000)
      
      const status = {
        job_id: jobId,
        status: i === totalSteps - 1 ? 'completed' : 'running',
        progress: Math.round(((i + 1) / totalSteps) * 100),
        steps: steps.slice(0, i + 1).map((step, index) => ({
          step_id: `step-${index}`,
          title: step,
          status: index <= i ? 'completed' : 'pending',
          content: `# Step ${index + 1}\n\nCompleted: ${step}`,
          timestamp: new Date().toISOString()
        }))
      }

      // In real implementation, this would be handled by polling
      console.log('Mock job progress:', status)
    }
  }

  static generateMockExplorationResults() {
    return {
      repository_info: {
        name: 'legacy-etl-system',
        language: 'Python',
        framework: 'Apache Airflow',
        total_files: 45
      },
      transactions: [
        {
          transaction_id: 'tx-001',
          name: 'Customer Data ETL',
          description: 'Extracts customer data from CRM and loads into warehouse',
          complexity: 'medium',
          file_path: 'dags/customer_etl.py',
          dependencies: ['pandas', 'sqlalchemy'],
          estimated_effort_hours: 8
        },
        {
          transaction_id: 'tx-002',
          name: 'Sales Report Generation',
          description: 'Generates daily sales reports from transaction data',
          complexity: 'low',
          file_path: 'dags/sales_reports.py',
          dependencies: ['pandas', 'matplotlib'],
          estimated_effort_hours: 4
        }
      ],
      summary: {
        total_transactions: 2,
        complexity_breakdown: { low: 1, medium: 1, high: 0 },
        estimated_total_effort: 12
      }
    }
  }
}

// Enable mock mode with environment variable
export const useMockApi = process.env.NODE_ENV === 'development' && 
                          process.env.NEXT_PUBLIC_USE_MOCK_API === 'true'
```

### 2. API Testing Utilities

Create `src/utils/apiTesting.ts`:

```typescript
import apiClient from '@/lib/apiClient'

export interface ApiTestResult {
  endpoint: string
  method: string
  success: boolean
  responseTime: number
  error?: string
}

export class ApiTester {
  static async testEndpoint(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any
  ): Promise<ApiTestResult> {
    const startTime = Date.now()
    
    try {
      switch (method) {
        case 'GET':
          await apiClient.get(endpoint)
          break
        case 'POST':
          await apiClient.post(endpoint, data)
          break
        case 'PUT':
          await apiClient.put(endpoint, data)
          break
        case 'DELETE':
          await apiClient.delete(endpoint)
          break
      }
      
      return {
        endpoint,
        method,
        success: true,
        responseTime: Date.now() - startTime
      }
    } catch (error: any) {
      return {
        endpoint,
        method,
        success: false,
        responseTime: Date.now() - startTime,
        error: error.message
      }
    }
  }

  static async testAllEndpoints(): Promise<ApiTestResult[]> {
    const tests = [
      { method: 'GET' as const, endpoint: '/auth/profile' },
      { method: 'GET' as const, endpoint: '/connectors' },
      { method: 'GET' as const, endpoint: '/ingestions' },
      { method: 'GET' as const, endpoint: '/migration/tasks' },
      { method: 'GET' as const, endpoint: '/objectives/tasks' },
      { method: 'GET' as const, endpoint: '/mapping/tasks' }
    ]

    const results = []
    for (const test of tests) {
      const result = await this.testEndpoint(test.method, test.endpoint)
      results.push(result)
    }

    return results
  }
}

// Debug utility for API calls
export function debugApiCall(config: any, response?: any, error?: any) {
  if (process.env.NODE_ENV === 'development') {
    console.group(`🔗 API Call: ${config.method?.toUpperCase()} ${config.url}`)
    console.log('Request config:', config)
    
    if (response) {
      console.log('Response:', response.data)
      console.log('Status:', response.status)
    }
    
    if (error) {
      console.error('Error:', error)
    }
    
    console.groupEnd()
  }
}
```

This comprehensive API integration guide provides junior developers with:

1. **Complete API client setup** with authentication, retry logic, and error handling
2. **Detailed service classes** for each major feature area
3. **React hooks** for easy component integration
4. **Type-safe interfaces** for all API responses
5. **File upload handling** with progress tracking
6. **Real-time job monitoring** with polling mechanisms
7. **Form checkpoint management** for complex workflows
8. **Comprehensive error handling** with user-friendly messages
9. **Testing utilities** for debugging and validation
10. **Mock services** for development without backend

Each service is designed to be:
- **Easy to use** for junior developers
- **Type-safe** with comprehensive TypeScript interfaces
- **Error-resilient** with proper error handling
- **Performance-optimized** with debouncing and caching where appropriate
- **Testable** with clear separation of concerns

The guide follows modern React patterns and provides complete implementations that can be directly used in the application.

## Real-time Monitoring Implementation

### 1. Advanced Job Status Component

Create `src/components/common/JobStatusMonitor.tsx`:

```typescript
'use client'
import { useState, useEffect } from 'react'
import { useJob } from '@/hooks/useJob'
import { CheckCircle, XCircle, Clock, Loader, Play, Square } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import type { JobStatus, JobStep } from '@/types/api'

interface JobStatusMonitorProps {
  jobId: string | null
  title?: string
  onComplete?: (status: JobStatus) => void
  onError?: (status: JobStatus) => void
  showLogs?: boolean
  autoExpand?: boolean
}

export default function JobStatusMonitor({
  jobId,
  title = 'Job Progress',
  onComplete,
  onError,
  showLogs = true,
  autoExpand = false
}: JobStatusMonitorProps) {
  const [expanded, setExpanded] = useState(autoExpand)
  const [showAllSteps, setShowAllSteps] = useState(false)
  
  const { 
    status, 
    isLoading, 
    error, 
    cancelJob, 
    retryJob, 
    isPolling 
  } = useJob(jobId, {
    onComplete,
    onError
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />
      case 'running':
        return <Loader className="w-5 h-5 text-blue-500 animate-spin" />
      case 'pending':
        return <Clock className="w-5 h-5 text-gray-500" />
      default:
        return <Clock className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'border-green-500 bg-green-50'
      case 'failed': return 'border-red-500 bg-red-50'
      case 'running': return 'border-blue-500 bg-blue-50'
      case 'pending': return 'border-gray-300 bg-gray-50'
      default: return 'border-gray-300 bg-gray-50'
    }
  }

  const formatDuration = (start: string, end?: string) => {
    const startTime = new Date(start).getTime()
    const endTime = end ? new Date(end).getTime() : Date.now()
    const duration = Math.round((endTime - startTime) / 1000)
    
    if (duration < 60) return `${duration}s`
    if (duration < 3600) return `${Math.floor(duration / 60)}m ${duration % 60}s`
    return `${Math.floor(duration / 3600)}h ${Math.floor((duration % 3600) / 60)}m`
  }

  if (!jobId || (!status && !isLoading)) {
    return null
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Header */}
      <div 
        className="p-4 border-b border-gray-200 cursor-pointer flex items-center justify-between"
        onClick={() => setExpanded(!expanded)}
      >
        <div className="flex items-center space-x-3">
          {status ? getStatusIcon(status.status) : <Loader className="w-5 h-5 animate-spin" />}
          <div>
            <h3 className="font-medium text-gray-900">{title}</h3>
            {status && (
              <p className="text-sm text-gray-600">
                {status.status === 'running' && `${status.progress}% complete`}
                {status.status === 'completed' && 'Successfully completed'}
                {status.status === 'failed' && 'Failed'}
                {status.status === 'pending' && 'Waiting to start'}
                {isPolling && ' • Live updates'}
              </p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {status?.status === 'running' && (
            <button
              onClick={(e) => {
                e.stopPropagation()
                cancelJob()
              }}
              className="text-red-600 hover:text-red-700 text-sm font-medium"
            >
              Cancel
            </button>
          )}
          
          {status?.status === 'failed' && (
            <button
              onClick={(e) => {
                e.stopPropagation()
                retryJob()
              }}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Retry
            </button>
          )}
          
          <button className="text-gray-400 hover:text-gray-600">
            {expanded ? '−' : '+'}
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      {status && status.status === 'running' && (
        <div className="px-4 py-2 bg-gray-50">
          <div className="flex justify-between text-sm mb-1">
            <span>Progress</span>
            <span>{status.progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${status.progress}%` }}
            />
          </div>
        </div>
      )}

      {/* Expanded Content */}
      {expanded && status && (
        <div className="p-4">
          {/* Job Info */}
          <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
            <div>
              <span className="text-gray-600">Job ID:</span>
              <span className="ml-2 font-mono text-xs">{status.job_id}</span>
            </div>
            {status.started_at && (
              <div>
                <span className="text-gray-600">Duration:</span>
                <span className="ml-2">{formatDuration(status.started_at, status.completed_at)}</span>
              </div>
            )}
          </div>

          {/* Error Message */}
          {status.error_message && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm font-medium text-red-800">Error:</p>
              <p className="text-sm text-red-700 mt-1">{status.error_message}</p>
            </div>
          )}

          {/* Steps */}
          {status.steps && status.steps.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-900">Steps</h4>
                {status.steps.length > 3 && (
                  <button
                    onClick={() => setShowAllSteps(!showAllSteps)}
                    className="text-sm text-blue-600 hover:text-blue-700"
                  >
                    {showAllSteps ? 'Show Less' : `Show All (${status.steps.length})`}
                  </button>
                )}
              </div>
              
              <div className="space-y-3">
                {(showAllSteps ? status.steps : status.steps.slice(-3)).map((step, index) => (
                  <JobStepCard key={step.step_id} step={step} showLogs={showLogs} />
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

// Individual Step Component
function JobStepCard({ step, showLogs }: { step: JobStep; showLogs: boolean }) {
  const [expanded, setExpanded] = useState(false)

  return (
    <div className={`border rounded-lg ${
      step.status === 'completed' ? 'border-green-200 bg-green-50' :
      step.status === 'running' ? 'border-blue-200 bg-blue-50' :
      step.status === 'failed' ? 'border-red-200 bg-red-50' :
      'border-gray-200 bg-gray-50'
    }`}>
      <div 
        className="p-3 cursor-pointer"
        onClick={() => setExpanded(!expanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {step.status === 'completed' && <CheckCircle className="w-4 h-4 text-green-500" />}
            {step.status === 'running' && <Loader className="w-4 h-4 text-blue-500 animate-spin" />}
            {step.status === 'failed' && <XCircle className="w-4 h-4 text-red-500" />}
            {step.status === 'pending' && <Clock className="w-4 h-4 text-gray-400" />}
            
            <span className="font-medium text-sm">{step.title}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500">
              {new Date(step.timestamp).toLocaleTimeString()}
            </span>
            {showLogs && step.content && (
              <button className="text-gray-400 hover:text-gray-600">
                {expanded ? '−' : '+'}
              </button>
            )}
          </div>
        </div>
      </div>
      
      {expanded && showLogs && step.content && (
        <div className="px-3 pb-3">
          <div className="bg-gray-900 rounded p-3 overflow-auto max-h-40">
            <div className="prose prose-sm max-w-none text-gray-100">
              <ReactMarkdown>{step.content}</ReactMarkdown>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
```

### 2. WebSocket Integration for Real-time Updates

Create `src/lib/websocketService.ts`:

```typescript
import { useAppStore } from '@/store/useAppStore'

export interface WebSocketMessage {
  type: 'job_update' | 'notification' | 'system_update'
  data: any
  timestamp: string
}

export class WebSocketService {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private heartbeatInterval: NodeJS.Timeout | null = null

  connect(token: string) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      return
    }

    const wsUrl = `${process.env.NEXT_PUBLIC_WS_URL}?token=${token}`
    this.ws = new WebSocket(wsUrl)

    this.ws.onopen = () => {
      console.log('WebSocket connected')
      this.reconnectAttempts = 0
      this.startHeartbeat()
    }

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data)
        this.handleMessage(message)
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error)
      }
    }

    this.ws.onclose = () => {
      console.log('WebSocket disconnected')
      this.stopHeartbeat()
      this.attemptReconnect(token)
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error)
    }
  }

  private handleMessage(message: WebSocketMessage) {
    switch (message.type) {
      case 'job_update':
        this.handleJobUpdate(message.data)
        break
      case 'notification':
        this.handleNotification(message.data)
        break
      case 'system_update':
        this.handleSystemUpdate(message.data)
        break
      default:
        console.log('Unknown message type:', message.type)
    }
  }

  private handleJobUpdate(data: any) {
    const store = useAppStore.getState()
    store.updateJobStatus(data.job_id, data)
  }

  private handleNotification(data: any) {
    // Handle real-time notifications
    if (typeof window !== 'undefined' && 'Notification' in window) {
      if (Notification.permission === 'granted') {
        new Notification(data.title, {
          body: data.message,
          icon: '/favicon.ico'
        })
      }
    }
  }

  private handleSystemUpdate(data: any) {
    // Handle system-wide updates
    console.log('System update:', data)
  }

  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'ping' }))
      }
    }, 30000) // 30 seconds
  }

  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  private attemptReconnect(token: string) {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached')
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
    
    setTimeout(() => {
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      this.connect(token)
    }, delay)
  }

  disconnect() {
    this.stopHeartbeat()
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  sendMessage(message: any) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    }
  }
}

export const websocketService = new WebSocketService()
```

## Performance Optimization

### 1. API Request Caching

Create `src/lib/cacheService.ts`:

```typescript
interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number // Time to live in milliseconds
}

export class CacheService {
  private cache = new Map<string, CacheEntry<any>>()
  private readonly defaultTTL = 5 * 60 * 1000 // 5 minutes

  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      return null
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    return entry.data as T
  }

  delete(key: string): void {
    this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  // Clean up expired entries
  cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
      }
    }
  }

  // Get cache statistics
  getStats(): {
    size: number
    entries: Array<{ key: string; age: number; ttl: number }>
  } {
    const now = Date.now()
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.entries()).map(([key, entry]) => ({
        key,
        age: now - entry.timestamp,
        ttl: entry.ttl
      }))
    }
  }
}

export const cacheService = new CacheService()

// Clean up expired entries every 10 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    cacheService.cleanup()
  }, 10 * 60 * 1000)
}
```

### 2. Request Batching and Optimization

Create `src/lib/requestOptimizer.ts`:

```typescript
import apiClient from './apiClient'

interface BatchRequest {
  endpoint: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  resolve: (value: any) => void
  reject: (error: any) => void
}

export class RequestOptimizer {
  private batchQueue: BatchRequest[] = []
  private batchTimeout: NodeJS.Timeout | null = null
  private readonly batchDelay = 50 // 50ms
  private readonly maxBatchSize = 10

  // Batch multiple requests together
  batchRequest<T>(
    endpoint: string, 
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      this.batchQueue.push({
        endpoint,
        method,
        data,
        resolve,
        reject
      })

      // Clear existing timeout
      if (this.batchTimeout) {
        clearTimeout(this.batchTimeout)
      }

      // Process batch after delay or when max size reached
      if (this.batchQueue.length >= this.maxBatchSize) {
        this.processBatch()
      } else {
        this.batchTimeout = setTimeout(() => {
          this.processBatch()
        }, this.batchDelay)
      }
    })
  }

  private async processBatch() {
    if (this.batchQueue.length === 0) return

    const currentBatch = this.batchQueue.splice(0, this.maxBatchSize)
    
    // Group requests by endpoint and method
    const groups = new Map<string, BatchRequest[]>()
    
    for (const request of currentBatch) {
      const key = `${request.method}:${request.endpoint}`
      if (!groups.has(key)) {
        groups.set(key, [])
      }
      groups.get(key)!.push(request)
    }

    // Process each group
    for (const [key, requests] of groups) {
      if (requests.length === 1) {
        // Single request - process normally
        const request = requests[0]
        try {
          const result = await this.makeRequest(request)
          request.resolve(result)
        } catch (error) {
          request.reject(error)
        }
      } else {
        // Multiple requests - check if they can be batched
        await this.handleBatchedRequests(requests)
      }
    }
  }

  private async handleBatchedRequests(requests: BatchRequest[]) {
    // For GET requests to similar endpoints, try to batch them
    if (requests[0].method === 'GET' && this.canBatchEndpoint(requests[0].endpoint)) {
      try {
        const batchResult = await this.makeBatchRequest(requests)
        requests.forEach((request, index) => {
          request.resolve(batchResult[index])
        })
      } catch (error) {
        requests.forEach(request => request.reject(error))
      }
    } else {
      // Process individually
      for (const request of requests) {
        try {
          const result = await this.makeRequest(request)
          request.resolve(result)
        } catch (error) {
          request.reject(error)
        }
      }
    }
  }

  private canBatchEndpoint(endpoint: string): boolean {
    // Define which endpoints support batching
    const batchableEndpoints = [
      '/connectors',
      '/ingestions',
      '/jobs/status'
    ]
    
    return batchableEndpoints.some(pattern => endpoint.includes(pattern))
  }

  private async makeRequest(request: BatchRequest): Promise<any> {
    switch (request.method) {
      case 'GET':
        return apiClient.get(request.endpoint)
      case 'POST':
        return apiClient.post(request.endpoint, request.data)
      case 'PUT':
        return apiClient.put(request.endpoint, request.data)
      case 'DELETE':
        return apiClient.delete(request.endpoint)
      default:
        throw new Error(`Unsupported method: ${request.method}`)
    }
  }

  private async makeBatchRequest(requests: BatchRequest[]): Promise<any[]> {
    // Create batch request payload
    const batchPayload = {
      requests: requests.map(req => ({
        endpoint: req.endpoint,
        method: req.method,
        data: req.data
      }))
    }

    // Send batch request
    const response = await apiClient.post('/batch', batchPayload)
    return response.responses
  }
}

export const requestOptimizer = new RequestOptimizer()
```

### 3. Smart Polling Strategy

Create `src/hooks/useSmartPolling.ts`:

```typescript
import { useState, useEffect, useRef, useCallback } from 'react'

interface SmartPollingOptions {
  interval: number
  maxInterval?: number
  backoffMultiplier?: number
  stopOnError?: boolean
  stopOnInactivity?: boolean
  inactivityThreshold?: number
}

export function useSmartPolling<T>(
  pollFunction: () => Promise<T>,
  shouldContinue: (data: T | null) => boolean,
  options: SmartPollingOptions
) {
  const [data, setData] = useState<T | null>(null)
  const [isPolling, setIsPolling] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const intervalRef = useRef<number>(options.interval)
  const lastActivityRef = useRef<number>(Date.now())
  const consecutiveErrorsRef = useRef<number>(0)

  const {
    maxInterval = 30000,
    backoffMultiplier = 1.5,
    stopOnError = false,
    stopOnInactivity = true,
    inactivityThreshold = 300000 // 5 minutes
  } = options

  // Track user activity
  useEffect(() => {
    if (!stopOnInactivity) return

    const handleActivity = () => {
      lastActivityRef.current = Date.now()
    }

    const events = ['mousedown', 'keydown', 'scroll', 'touchstart']
    events.forEach(event => {
      window.addEventListener(event, handleActivity, true)
    })

    return () => {
      events.forEach(event => {
        window.removeEventListener(event, handleActivity, true)
      })
    }
  }, [stopOnInactivity])

  const poll = useCallback(async () => {
    try {
      // Check for inactivity
      if (stopOnInactivity && Date.now() - lastActivityRef.current > inactivityThreshold) {
        setIsPolling(false)
        return
      }

      const result = await pollFunction()
      setData(result)
      setError(null)
      consecutiveErrorsRef.current = 0
      
      // Reset interval on successful poll
      intervalRef.current = options.interval

      // Check if we should continue polling
      if (!shouldContinue(result)) {
        setIsPolling(false)
        return
      }

      // Schedule next poll
      timeoutRef.current = setTimeout(poll, intervalRef.current)
    } catch (err) {
      const error = err as Error
      setError(error)
      consecutiveErrorsRef.current++

      if (stopOnError) {
        setIsPolling(false)
        return
      }

      // Exponential backoff on errors
      intervalRef.current = Math.min(
        intervalRef.current * backoffMultiplier,
        maxInterval
      )

      // Stop polling after too many consecutive errors
      if (consecutiveErrorsRef.current >= 5) {
        setIsPolling(false)
        return
      }

      // Schedule retry
      timeoutRef.current = setTimeout(poll, intervalRef.current)
    }
  }, [pollFunction, shouldContinue, options.interval, maxInterval, backoffMultiplier, stopOnError, stopOnInactivity, inactivityThreshold])

  const startPolling = useCallback(() => {
    if (isPolling) return

    setIsPolling(true)
    intervalRef.current = options.interval
    consecutiveErrorsRef.current = 0
    poll()
  }, [isPolling, options.interval, poll])

  const stopPolling = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    setIsPolling(false)
  }, [])

  const resetPolling = useCallback(() => {
    stopPolling()
    startPolling()
  }, [stopPolling, startPolling])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return {
    data,
    error,
    isPolling,
    startPolling,
    stopPolling,
    resetPolling
  }
}
```

## Integration Examples with React Components

### 1. Complete Ingestion Creation Flow

Create `src/components/ingestion/CreateIngestionFlow.tsx`:

```typescript
'use client'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import toast from 'react-hot-toast'

import { ingestionService } from '@/lib/ingestionService'
import { connectorsService } from '@/lib/connectorsService'
import { useFileUpload } from '@/hooks/useFileUpload'
import { useFormCheckpoint } from '@/hooks/useFormCheckpoint'

import StepIndicator from '@/components/common/StepIndicator'
import FileUploadZone from '@/components/common/FileUploadZone'
import ConnectorSelector from '@/components/connectors/ConnectorSelector'
import SchemaPreview from '@/components/ingestion/SchemaPreview'

const ingestionSchema = yup.object({
  name: yup.string().required('Name is required'),
  sourceType: yup.string().oneOf(['database', 'file']).required(),
  sourceId: yup.string().when('sourceType', {
    is: 'database',
    then: (schema) => schema.required('Source is required')
  }),
  destinationId: yup.string().required('Destination is required'),
  schedule: yup.object({
    frequency: yup.string().oneOf(['manual', 'hourly', 'daily', 'weekly']).required()
  })
})

type IngestionFormData = yup.InferType<typeof ingestionSchema>

export default function CreateIngestionFlow() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [isCreating, setIsCreating] = useState(false)
  const [taskId] = useState(() => `ingestion_${Date.now()}`)

  // Form management with checkpoints
  const {
    formData,
    updateFormData,
    saveNow,
    isLoading: isLoadingCheckpoint
  } = useFormCheckpoint<IngestionFormData>(taskId, {
    name: '',
    sourceType: 'database',
    sourceId: '',
    destinationId: '',
    schedule: { frequency: 'daily' }
  })

  // React Hook Form
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm<IngestionFormData>({
    resolver: yupResolver(ingestionSchema),
    defaultValues: formData
  })

  // File upload for file sources
  const {
    uploadFile,
    uploadedFile,
    previewData,
    isUploading,
    uploadProgress
  } = useFileUpload()

  const watchedValues = watch()

  // Update form data when form values change
  React.useEffect(() => {
    updateFormData(watchedValues)
  }, [watchedValues, updateFormData])

  // Test connection between source and destination
  const testConnection = async () => {
    try {
      const result = await ingestionService.testConnection({
        source_id: formData.sourceId,
        destination_id: formData.destinationId
      })

      if (result.success) {
        toast.success('Connection test successful!')
        return true
      } else {
        toast.error(result.message)
        return false
      }
    } catch (error) {
      toast.error('Connection test failed')
      return false
    }
  }

  // Create the ingestion pipeline
  const createIngestion = async (data: IngestionFormData) => {
    setIsCreating(true)
    
    try {
      const ingestionData = {
        name: data.name,
        source_id: data.sourceId,
        destination_id: data.destinationId,
        source_type: data.sourceType,
        file_id: uploadedFile?.file_id,
        schedule: {
          frequency: data.schedule.frequency
        }
      }

      const result = await ingestionService.createIngestion(ingestionData)
      
      toast.success('Ingestion pipeline created successfully!')
      router.push(`/ingestions/${result.id}`)
    } catch (error) {
      toast.error('Failed to create ingestion pipeline')
    } finally {
      setIsCreating(false)
    }
  }

  const nextStep = () => {
    saveNow() // Save checkpoint
    setCurrentStep(prev => Math.min(prev + 1, 4))
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  if (isLoadingCheckpoint) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h1 className="text-2xl font-bold text-gray-900">Create Ingestion Pipeline</h1>
          <p className="text-gray-600 mt-1">Set up data ingestion from source to destination</p>
        </div>

        <div className="p-6">
          <StepIndicator currentStep={currentStep} totalSteps={4} />

          <form onSubmit={handleSubmit(createIngestion)} className="mt-8">
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold">Basic Information</h2>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Pipeline Name
                  </label>
                  <input
                    {...register('name')}
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter pipeline name"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Source Type
                  </label>
                  <div className="grid grid-cols-2 gap-4">
                    <label className="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:border-blue-500">
                      <input
                        {...register('sourceType')}
                        type="radio"
                        value="database"
                        className="sr-only"
                      />
                      <div className="flex-1">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <Database className="w-5 h-5 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium">Database</p>
                            <p className="text-sm text-gray-600">Connect to database sources</p>
                          </div>
                        </div>
                      </div>
                    </label>

                    <label className="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:border-blue-500">
                      <input
                        {...register('sourceType')}
                        type="radio"
                        value="file"
                        className="sr-only"
                      />
                      <div className="flex-1">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                            <FileText className="w-5 h-5 text-green-600" />
                          </div>
                          <div>
                            <p className="font-medium">File Upload</p>
                            <p className="text-sm text-gray-600">Upload CSV/Excel files</p>
                          </div>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={nextStep}
                    disabled={!formData.name || !formData.sourceType}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}

            {/* Step 2: Source Configuration */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold">Source Configuration</h2>
                
                {formData.sourceType === 'database' ? (
                  <ConnectorSelector
                    type="source"
                    selectedId={formData.sourceId}
                    onSelect={(id) => {
                      setValue('sourceId', id)
                      updateFormData({ ...formData, sourceId: id })
                    }}
                  />
                ) : (
                  <FileUploadZone
                    onFileUpload={uploadFile}
                    uploadProgress={uploadProgress}
                    uploadedFile={uploadedFile}
                    previewData={previewData}
                    isUploading={isUploading}
                  />
                )}

                <div className="flex justify-between">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                  >
                    Previous
                  </button>
                  <button
                    type="button"
                    onClick={nextStep}
                    disabled={
                      formData.sourceType === 'database' 
                        ? !formData.sourceId 
                        : !uploadedFile
                    }
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}

            {/* Step 3: Destination Configuration */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold">Destination Configuration</h2>
                
                <ConnectorSelector
                  type="destination"
                  selectedId={formData.destinationId}
                  onSelect={(id) => {
                    setValue('destinationId', id)
                    updateFormData({ ...formData, destinationId: id })
                  }}
                />

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sync Schedule
                  </label>
                  <select
                    {...register('schedule.frequency')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="manual">Manual</option>
                    <option value="hourly">Every Hour</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                  </select>
                </div>

                <div className="flex justify-between">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                  >
                    Previous
                  </button>
                  <button
                    type="button"
                    onClick={nextStep}
                    disabled={!formData.destinationId}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}

            {/* Step 4: Review and Create */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold">Review and Create</h2>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium mb-2">Pipeline Summary</h3>
                  <dl className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <dt className="text-gray-600">Name:</dt>
                      <dd className="font-medium">{formData.name}</dd>
                    </div>
                    <div>
                      <dt className="text-gray-600">Source Type:</dt>
                      <dd className="font-medium capitalize">{formData.sourceType}</dd>
                    </div>
                    <div>
                      <dt className="text-gray-600">Schedule:</dt>
                      <dd className="font-medium capitalize">{formData.schedule.frequency}</dd>
                    </div>
                    {uploadedFile && (
                      <div>
                        <dt className="text-gray-600">File:</dt>
                        <dd className="font-medium">{uploadedFile.filename}</dd>
                      </div>
                    )}
                  </dl>
                </div>

                <div className="border border-blue-200 bg-blue-50 rounded-lg p-4">
                  <h3 className="font-medium text-blue-900 mb-2">Connection Test</h3>
                  <p className="text-sm text-blue-700 mb-3">
                    Test the connection between your source and destination before creating the pipeline.
                  </p>
                  <button
                    type="button"
                    onClick={testConnection}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                  >
                    Test Connection
                  </button>
                </div>

                <div className="flex justify-between">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                  >
                    Previous
                  </button>
                  <button
                    type="submit"
                    disabled={isCreating}
                    className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isCreating ? 'Creating...' : 'Create Pipeline'}
                  </button>
                </div>
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  )
}
```

## Environment Configuration & Security

### 1. Environment Configuration

Create `src/lib/config.ts`:

```typescript
interface AppConfig {
  api: {
    baseUrl: string
    timeout: number
    retryAttempts: number
  }
  websocket: {
    url: string
    reconnectDelay: number
    maxReconnectAttempts: number
  }
  upload: {
    maxFileSize: number
    allowedTypes: string[]
    chunkSize: number
  }
  polling: {
    defaultInterval: number
    maxInterval: number
    backoffMultiplier: number
  }
  cache: {
    defaultTTL: number
    maxSize: number
  }
  features: {
    enableWebsocket: boolean
    enableNotifications: boolean
    enableAnalytics: boolean
  }
}

const getConfig = (): AppConfig => {
  // Validate required environment variables
  const requiredEnvVars = [
    'NEXT_PUBLIC_BACKEND_URL'
  ]

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`)
    }
  }

  return {
    api: {
      baseUrl: process.env.NEXT_PUBLIC_BACKEND_URL!,
      timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '30000'),
      retryAttempts: parseInt(process.env.NEXT_PUBLIC_API_RETRY_ATTEMPTS || '3')
    },
    websocket: {
      url: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws',
      reconnectDelay: parseInt(process.env.NEXT_PUBLIC_WS_RECONNECT_DELAY || '1000'),
      maxReconnectAttempts: parseInt(process.env.NEXT_PUBLIC_WS_MAX_RECONNECT_ATTEMPTS || '5')
    },
    upload: {
      maxFileSize: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '52428800'), // 50MB
      allowedTypes: (process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES || 'text/csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet').split(','),
      chunkSize: parseInt(process.env.NEXT_PUBLIC_UPLOAD_CHUNK_SIZE || '1048576') // 1MB
    },
    polling: {
      defaultInterval: parseInt(process.env.NEXT_PUBLIC_POLLING_INTERVAL || '5000'),
      maxInterval: parseInt(process.env.NEXT_PUBLIC_MAX_POLLING_INTERVAL || '30000'),
      backoffMultiplier: parseFloat(process.env.NEXT_PUBLIC_POLLING_BACKOFF_MULTIPLIER || '1.5')
    },
    cache: {
      defaultTTL: parseInt(process.env.NEXT_PUBLIC_CACHE_TTL || '300000'), // 5 minutes
      maxSize: parseInt(process.env.NEXT_PUBLIC_CACHE_MAX_SIZE || '100')
    },
    features: {
      enableWebsocket: process.env.NEXT_PUBLIC_ENABLE_WEBSOCKET === 'true',
      enableNotifications: process.env.NEXT_PUBLIC_ENABLE_NOTIFICATIONS === 'true',
      enableAnalytics: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true'
    }
  }
}

export const config = getConfig()
export default config
```

### 2. Security Best Practices Implementation

Create `src/lib/security.ts`:

```typescript
import CryptoJS from 'crypto-js'

export class SecurityUtils {
  // Sanitize user input to prevent XSS
  static sanitizeInput(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove potential script tags
      .replace(/javascript:/gi, '') // Remove javascript protocols
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim()
  }

  // Validate and sanitize file names
  static sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars with underscore
      .replace(/\.{2,}/g, '.') // Replace multiple dots with single dot
      .replace(/^\./, '') // Remove leading dot
      .slice(0, 255) // Limit length
  }

  // Generate secure random IDs
  static generateSecureId(length: number = 32): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length))
    }
    return result
  }

  // Encrypt sensitive data for client-side storage
  static encryptData(data: string, key: string): string {
    return CryptoJS.AES.encrypt(data, key).toString()
  }

  // Decrypt sensitive data
  static decryptData(encryptedData: string, key: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedData, key)
    return bytes.toString(CryptoJS.enc.Utf8)
  }

  // Validate URLs to prevent SSRF
  static isValidUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url)
      
      // Only allow HTTP and HTTPS protocols
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        return false
      }
      
      // Prevent access to private IP ranges
      const hostname = parsedUrl.hostname
      if (this.isPrivateIP(hostname)) {
        return false
      }
      
      return true
    } catch {
      return false
    }
  }

  private static isPrivateIP(hostname: string): boolean {
    // Check for localhost variations
    if (['localhost', '127.0.0.1', '::1'].includes(hostname)) {
      return true
    }
    
    // Check for private IP ranges
    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[01])\./,
      /^192\.168\./,
      /^169\.254\./, // Link-local
      /^fc00:/, // IPv6 private
      /^fe80:/ // IPv6 link-local
    ]
    
    return privateRanges.some(range => range.test(hostname))
  }

  // Rate limiting for API calls
  static createRateLimiter(maxRequests: number, windowMs: number) {
    const requests = new Map<string, number[]>()
    
    return (identifier: string): boolean => {
      const now = Date.now()
      const windowStart = now - windowMs
      
      // Get existing requests for this identifier
      const userRequests = requests.get(identifier) || []
      
      // Filter out requests outside the current window
      const validRequests = userRequests.filter(time => time > windowStart)
      
      // Check if limit exceeded
      if (validRequests.length >= maxRequests) {
        return false
      }
      
      // Add current request
      validRequests.push(now)
      requests.set(identifier, validRequests)
      
      return true
    }
  }

  // Content Security Policy headers
  static getCSPHeaders(): Record<string, string> {
    return {
      'Content-Security-Policy': [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: https:",
        "font-src 'self'",
        "connect-src 'self' wss: ws:",
        "frame-ancestors 'none'",
        "base-uri 'self'",
        "form-action 'self'"
      ].join('; ')
    }
  }
}

// Create rate limiter for API calls (100 requests per minute)
export const apiRateLimiter = SecurityUtils.createRateLimiter(100, 60000)
```

## Deployment Considerations

### 1. Production Build Optimization

Create `next.config.js`:

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable React strict mode
  reactStrictMode: true,
  
  // Enable SWC minification
  swcMinify: true,
  
  // Optimize images
  images: {
    domains: ['example.com'], // Add your image domains
    formats: ['image/webp', 'image/avif']
  },
  
  // Bundle analyzer (enable when needed)
  ...(process.env.ANALYZE === 'true' && {
    webpack: (config) => {
      config.plugins.push(
        new (require('@next/bundle-analyzer'))({
          enabled: true
        })
      )
      return config
    }
  }),
  
  // Environment variables validation
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  
  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          }
        ]
      }
    ]
  },
  
  // Redirect configuration
  async redirects() {
    return [
      {
        source: '/',
        destination: '/dashboard',
        permanent: false,
        has: [
          {
            type: 'cookie',
            key: 'authenticated',
            value: 'true'
          }
        ]
      }
    ]
  },
  
  // Experimental features
  experimental: {
    // Enable Server Components
    appDir: true,
    
    // Optimize CSS
    optimizeCss: true,
    
    // Enable edge runtime for API routes
    runtime: 'nodejs'
  }
}

module.exports = nextConfig
```

### 2. Monitoring and Error Tracking

Create `src/lib/monitoring.ts`:

```typescript
interface ErrorReport {
  message: string
  stack?: string
  url: string
  userAgent: string
  timestamp: number
  userId?: string
  additionalData?: Record<string, any>
}

export class MonitoringService {
  private static instance: MonitoringService
  private errorQueue: ErrorReport[] = []
  private isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true

  static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService()
    }
    return MonitoringService.instance
  }

  constructor() {
    if (typeof window !== 'undefined') {
      // Listen for online/offline events
      window.addEventListener('online', () => {
        this.isOnline = true
        this.flushErrorQueue()
      })
      
      window.addEventListener('offline', () => {
        this.isOnline = false
      })

      // Global error handler
      window.addEventListener('error', (event) => {
        this.reportError({
          message: event.message,
          stack: event.error?.stack,
          url: event.filename || window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        })
      })

      // Unhandled promise rejection handler
      window.addEventListener('unhandledrejection', (event) => {
        this.reportError({
          message: event.reason?.message || 'Unhandled Promise Rejection',
          stack: event.reason?.stack,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        })
      })
    }
  }

  reportError(error: Partial<ErrorReport>) {
    const errorReport: ErrorReport = {
      message: error.message || 'Unknown error',
      stack: error.stack,
      url: error.url || (typeof window !== 'undefined' ? window.location.href : ''),
      userAgent: error.userAgent || (typeof navigator !== 'undefined' ? navigator.userAgent : ''),
      timestamp: error.timestamp || Date.now(),
      userId: error.userId,
      additionalData: error.additionalData
    }

    // Add to queue
    this.errorQueue.push(errorReport)

    // Try to send immediately if online
    if (this.isOnline) {
      this.flushErrorQueue()
    }
  }

  private async flushErrorQueue() {
    if (this.errorQueue.length === 0) return

    try {
      // Send errors to monitoring service
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          errors: this.errorQueue.splice(0, 10) // Send max 10 errors at once
        })
      })
    } catch (error) {
      console.error('Failed to send error reports:', error)
      // Keep errors in queue for retry
    }
  }

  // Performance monitoring
  reportPerformance(metric: {
    name: string
    value: number
    unit: string
    tags?: Record<string, string>
  }) {
    if (typeof window !== 'undefined' && this.isOnline) {
      fetch('/api/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(metric)
      }).catch(console.error)
    }
  }

  // User action tracking
  trackAction(action: {
    name: string
    category: string
    properties?: Record<string, any>
  }) {
    if (typeof window !== 'undefined' && this.isOnline) {
      fetch('/api/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...action,
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent
        })
      }).catch(console.error)
    }
  }
}

export const monitoring = MonitoringService.getInstance()

// Performance monitoring hook
export function usePerformanceMonitoring() {
  React.useEffect(() => {
    // Monitor page load time
    if (typeof window !== 'undefined' && window.performance) {
      const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart
      
      monitoring.reportPerformance({
        name: 'page_load_time',
        value: loadTime,
        unit: 'ms',
        tags: {
          page: window.location.pathname
        }
      })
    }
  }, [])

  return {
    trackAction: monitoring.trackAction.bind(monitoring),
    reportError: monitoring.reportError.bind(monitoring)
  }
}
```

This comprehensive API integration guide now includes all the essential components for building a production-ready data integration platform, providing junior developers with complete, working examples they can directly implement in their Next.js application.