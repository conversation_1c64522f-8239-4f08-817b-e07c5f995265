# Complete Frontend Development Guide - Data Integration Platform

## Table of Contents
1. [Project Setup](#project-setup)
2. [Authentication System](#authentication-system)
3. [State Management](#state-management)
4. [Component Architecture](#component-architecture)
5. [Dashboard Implementation](#dashboard-implementation)
6. [Connectors Module](#connectors-module)
7. [Ingestion Pipelines](#ingestion-pipelines)
8. [Pipeline Migration](#pipeline-migration)
9. [Business Objectives](#business-objectives)
10. [Data Model Mapping](#data-model-mapping)
11. [Settings Implementation](#settings-implementation)
12. [Error Handling](#error-handling)
13. [Performance Optimization](#performance-optimization)

## Project Setup

### 1. Initialize Next.js Project

```bash
# Create new Next.js project with TypeScript
npx create-next-app@latest dataflow-platform --typescript --tailwind --eslint --app

# Navigate to project directory
cd dataflow-platform

# Install required dependencies
npm install next-auth zustand axios react-hook-form @hookform/resolvers yup
npm install lucide-react react-hot-toast react-vertical-timeline-component
npm install @types/react-vertical-timeline-component
npm install react-markdown recharts date-fns clsx

# Install development dependencies
npm install -D @types/node
```

### 2. Project Structure

Create the following directory structure:

```
src/
├── app/
│   ├── (auth)/
│   │   ├── login/
│   │   ├── signup/
│   │   └── forgot-password/
│   ├── api/
│   │   └── auth/
│   │       └── [...nextauth]/
│   ├── dashboard/
│   ├── connectors/
│   ├── ingestions/
│   ├── migration/
│   ├── objectives/
│   ├── mapping/
│   ├── settings/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── auth/
│   ├── common/
│   ├── connectors/
│   ├── dashboard/
│   ├── ingestion/
│   ├── migration/
│   ├── objectives/
│   ├── mapping/
│   └── settings/
├── hooks/
├── lib/
├── store/
├── types/
└── utils/
```

### 3. Environment Configuration

Create `.env.local`:

```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000
```

### 4. TypeScript Configuration

Update `tsconfig.json`:

```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/store/*": ["./src/store/*"],
      "@/types/*": ["./src/types/*"],
      "@/utils/*": ["./src/utils/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

## Authentication System

### 1. Types Definition

Create `src/types/auth.ts`:

```typescript
export interface User {
  id: string
  email: string
  name: string
  jobTitle?: string
  department?: string
  avatar?: string
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresAt: number
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface SignupData {
  email: string
  password: string
  name: string
  jobTitle?: string
  department?: string
}

export interface ForgotPasswordData {
  email: string
}

export interface ResetPasswordData {
  token: string
  password: string
  confirmPassword: string
}
```

### 2. NextAuth Configuration

Create `src/app/api/auth/[...nextauth]/route.ts`:

```typescript
import NextAuth from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'
import type { NextAuthOptions } from 'next-auth'

const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/auth`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password,
            }),
          })

          if (!response.ok) {
            return null
          }

          const data = await response.json()

          return {
            id: data.user.id,
            email: data.user.email,
            name: data.user.name,
            jobTitle: data.user.jobTitle,
            department: data.user.department,
            avatar: data.user.avatar,
            accessToken: data.access_token,
            refreshToken: data.refresh_token,
          }
        } catch (error) {
          console.error('Authentication error:', error)
          return null
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.accessToken = user.accessToken
        token.refreshToken = user.refreshToken
        token.user = {
          id: user.id,
          email: user.email,
          name: user.name,
          jobTitle: user.jobTitle,
          department: user.department,
          avatar: user.avatar,
        }
      }
      return token
    },
    async session({ session, token }) {
      session.accessToken = token.accessToken as string
      session.refreshToken = token.refreshToken as string
      session.user = token.user as User
      return session
    }
  },
  pages: {
    signIn: '/login',
    signUp: '/signup',
  },
  session: {
    strategy: 'jwt',
  },
}

const handler = NextAuth(authOptions)
export { handler as GET, handler as POST }
```

### 3. Authentication Components

Create `src/components/auth/LoginForm.tsx`:

```typescript
'use client'
import { useState } from 'react'
import { signIn } from 'next-auth/react'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import toast from 'react-hot-toast'
import { Eye, EyeOff, Mail, Lock } from 'lucide-react'
import type { LoginCredentials } from '@/types/auth'

const loginSchema = yup.object({
  email: yup.string().email('Invalid email').required('Email is required'),
  password: yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
})

export default function LoginForm() {
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginCredentials>({
    resolver: yupResolver(loginSchema),
  })

  const onSubmit = async (data: LoginCredentials) => {
    setIsLoading(true)
    
    try {
      const result = await signIn('credentials', {
        email: data.email,
        password: data.password,
        redirect: false,
      })

      if (result?.error) {
        toast.error('Invalid email or password')
      } else {
        toast.success('Successfully logged in!')
        router.push('/dashboard')
      }
    } catch (error) {
      toast.error('An error occurred during login')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xl">D</span>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to DataFlow
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Or{' '}
            <Link href="/signup" className="font-medium text-blue-600 hover:text-blue-500">
              create a new account
            </Link>
          </p>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  {...register('email')}
                  type="email"
                  autoComplete="email"
                  className={`appearance-none relative block w-full pl-10 pr-3 py-2 border ${
                    errors.email ? 'border-red-300' : 'border-gray-300'
                  } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                  placeholder="Enter your email"
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  {...register('password')}
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  className={`appearance-none relative block w-full pl-10 pr-10 py-2 border ${
                    errors.password ? 'border-red-300' : 'border-gray-300'
                  } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="text-sm">
              <Link href="/forgot-password" className="font-medium text-blue-600 hover:text-blue-500">
                Forgot your password?
              </Link>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Signing in...' : 'Sign in'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
```

### 4. Protected Route Wrapper

Create `src/components/auth/ProtectedRoute.tsx`:

```typescript
'use client'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

interface ProtectedRouteProps {
  children: React.ReactNode
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'loading') return // Still loading

    if (!session) {
      router.push('/login')
      return
    }
  }, [session, status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return <>{children}</>
}
```

## State Management

### 1. Zustand Store Setup

Create `src/store/useAppStore.ts`:

```typescript
import { create } from 'zustand'
import type { User } from '@/types/auth'

export interface JobStatus {
  job_id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  steps: JobStep[]
  error_message?: string
}

export interface JobStep {
  step_id: string
  title: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  content: string
  timestamp: string
}

interface AppState {
  // UI State
  sidebarCollapsed: boolean
  activeTab: string
  showOnboarding: boolean
  connectorsExpanded: boolean
  
  // Job Management
  activeJobs: Map<string, JobStatus>
  jobPollingIntervals: Map<string, NodeJS.Timeout>
  
  // Form Management
  formCheckpoints: Map<string, any>
  
  // Actions
  toggleSidebar: () => void
  setActiveTab: (tab: string) => void
  toggleOnboarding: () => void
  setConnectorsExpanded: (expanded: boolean) => void
  
  // Job Actions
  addActiveJob: (jobId: string, jobStatus: JobStatus) => void
  updateJobStatus: (jobId: string, jobStatus: JobStatus) => void
  removeActiveJob: (jobId: string) => void
  startJobPolling: (jobId: string) => void
  stopJobPolling: (jobId: string) => void
  
  // Form Actions
  saveFormCheckpoint: (taskId: string, data: any) => Promise<void>
  loadFormCheckpoint: (taskId: string) => any
  clearFormCheckpoint: (taskId: string) => void
}

export const useAppStore = create<AppState>((set, get) => ({
  // Initial State
  sidebarCollapsed: false,
  activeTab: 'dashboard',
  showOnboarding: true,
  connectorsExpanded: false,
  activeJobs: new Map(),
  jobPollingIntervals: new Map(),
  formCheckpoints: new Map(),
  
  // UI Actions
  toggleSidebar: () => set(state => ({ 
    sidebarCollapsed: !state.sidebarCollapsed 
  })),
  
  setActiveTab: (tab) => set({ activeTab: tab }),
  
  toggleOnboarding: () => set(state => ({ 
    showOnboarding: !state.showOnboarding 
  })),
  
  setConnectorsExpanded: (expanded) => set({ 
    connectorsExpanded: expanded 
  }),
  
  // Job Management Actions
  addActiveJob: (jobId, jobStatus) => {
    const { activeJobs } = get()
    activeJobs.set(jobId, jobStatus)
    set({ activeJobs: new Map(activeJobs) })
  },
  
  updateJobStatus: (jobId, jobStatus) => {
    const { activeJobs } = get()
    if (activeJobs.has(jobId)) {
      activeJobs.set(jobId, jobStatus)
      set({ activeJobs: new Map(activeJobs) })
    }
  },
  
  removeActiveJob: (jobId) => {
    const { activeJobs, stopJobPolling } = get()
    activeJobs.delete(jobId)
    stopJobPolling(jobId)
    set({ activeJobs: new Map(activeJobs) })
  },
  
  startJobPolling: (jobId) => {
    const { jobPollingIntervals } = get()
    
    // Don't start if already polling
    if (jobPollingIntervals.has(jobId)) return
    
    const interval = setInterval(async () => {
      try {
        // This will be implemented in the API integration guide
        const jobStatus = await window.apiClient?.getJobStatus(jobId)
        if (jobStatus) {
          get().updateJobStatus(jobId, jobStatus)
          
          // Stop polling if job is complete
          if (jobStatus.status === 'completed' || jobStatus.status === 'failed') {
            get().stopJobPolling(jobId)
          }
        }
      } catch (error) {
        console.error('Error polling job status:', error)
      }
    }, 5000) // Poll every 5 seconds
    
    jobPollingIntervals.set(jobId, interval)
    set({ jobPollingIntervals: new Map(jobPollingIntervals) })
  },
  
  stopJobPolling: (jobId) => {
    const { jobPollingIntervals } = get()
    const interval = jobPollingIntervals.get(jobId)
    
    if (interval) {
      clearInterval(interval)
      jobPollingIntervals.delete(jobId)
      set({ jobPollingIntervals: new Map(jobPollingIntervals) })
    }
  },
  
  // Form Management Actions
  saveFormCheckpoint: async (taskId, data) => {
    try {
      // Save to backend - this will be implemented in API integration guide
      await window.apiClient?.saveFormCheckpoint(taskId, data)
      
      const { formCheckpoints } = get()
      formCheckpoints.set(taskId, data)
      set({ formCheckpoints: new Map(formCheckpoints) })
    } catch (error) {
      console.error('Failed to save form checkpoint:', error)
      throw error
    }
  },
  
  loadFormCheckpoint: (taskId) => {
    const { formCheckpoints } = get()
    return formCheckpoints.get(taskId) || null
  },
  
  clearFormCheckpoint: (taskId) => {
    const { formCheckpoints } = get()
    formCheckpoints.delete(taskId)
    set({ formCheckpoints: new Map(formCheckpoints) })
  },
}))
```

## Component Architecture

### 1. Layout Components

Create `src/components/common/Sidebar.tsx`:

```typescript
'use client'
import { useAppStore } from '@/store/useAppStore'
import { 
  Home, 
  Database, 
  Cable, 
  RefreshCw, 
  Target, 
  Map, 
  Settings,
  Menu,
  ChevronDown,
  ChevronRight,
  Download,
  Upload
} from 'lucide-react'

const navigationItems = [
  { id: 'dashboard', label: 'Dashboard', icon: Home },
  { 
    id: 'connectors', 
    label: 'Connectors', 
    icon: Database, 
    hasSubmenu: true,
    subItems: [
      { id: 'sources', label: 'Sources', icon: Download },
      { id: 'destinations', label: 'Destinations', icon: Upload }
    ]
  },
  { id: 'ingestions', label: 'Ingestions', icon: Cable },
  { id: 'migration', label: 'Pipeline Migration', icon: RefreshCw },
  { id: 'objectives', label: 'Business Objectives', icon: Target },
  { id: 'mapping', label: 'Data Model Mapping', icon: Map },
  { id: 'settings', label: 'Settings', icon: Settings }
]

export default function Sidebar() {
  const { 
    sidebarCollapsed, 
    activeTab, 
    connectorsExpanded,
    toggleSidebar, 
    setActiveTab, 
    setConnectorsExpanded 
  } = useAppStore()

  const handleNavClick = (itemId: string) => {
    if (itemId === 'connectors') {
      setConnectorsExpanded(!connectorsExpanded)
      if (!connectorsExpanded) {
        setActiveTab('connectors')
      }
    } else {
      setActiveTab(itemId)
      setConnectorsExpanded(false)
    }
  }

  return (
    <div className={`${sidebarCollapsed ? 'w-16' : 'w-64'} bg-white shadow-lg transition-all duration-300 border-r border-gray-200 flex flex-col`}>
      {/* Header */}
      <div className="p-4">
        <div className="flex items-center justify-between">
          {!sidebarCollapsed && (
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">DF</span>
              </div>
              <span className="font-bold text-xl text-gray-900">DataFlow</span>
            </div>
          )}
          <button 
            onClick={toggleSidebar}
            className="p-1 rounded-lg hover:bg-gray-100"
          >
            <Menu className="w-5 h-5 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="mt-4 px-2 flex-1">
        {navigationItems.map((item) => {
          const Icon = item.icon
          const isActive = activeTab === item.id || (item.id === 'connectors' && connectorsExpanded)
          
          return (
            <div key={item.id}>
              <button
                onClick={() => handleNavClick(item.id)}
                className={`w-full flex items-center px-3 py-2 mb-1 rounded-lg transition-colors ${
                  isActive 
                    ? 'bg-blue-50 text-blue-700' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Icon className="w-5 h-5" />
                {!sidebarCollapsed && (
                  <>
                    <span className="ml-3 text-sm font-medium flex-1 text-left">
                      {item.label}
                    </span>
                    {item.hasSubmenu && (
                      connectorsExpanded ? 
                        <ChevronDown className="w-4 h-4" /> : 
                        <ChevronRight className="w-4 h-4" />
                    )}
                  </>
                )}
              </button>
              
              {/* Submenu for Connectors */}
              {item.id === 'connectors' && connectorsExpanded && !sidebarCollapsed && (
                <div className="ml-6 mb-2">
                  {item.subItems?.map((subItem) => {
                    const SubIcon = subItem.icon
                    return (
                      <button
                        key={subItem.id}
                        onClick={() => setActiveTab('connectors')}
                        className="w-full flex items-center px-3 py-1.5 mb-1 rounded-lg text-sm transition-colors text-gray-600 hover:bg-gray-50"
                      >
                        <SubIcon className="w-4 h-4" />
                        <span className="ml-2">{subItem.label}</span>
                      </button>
                    )
                  })}
                </div>
              )}
            </div>
          )
        })}
      </nav>
    </div>
  )
}
```

### 2. Header Component

Create `src/components/common/Header.tsx`:

```typescript
'use client'
import { useSession, signOut } from 'next-auth/react'
import { useState } from 'react'
import { Search, Bell, User, LogOut } from 'lucide-react'
import { useAppStore } from '@/store/useAppStore'

export default function Header() {
  const { data: session } = useSession()
  const { activeTab } = useAppStore()
  const [showUserMenu, setShowUserMenu] = useState(false)

  const getPageTitle = (tab: string) => {
    switch (tab) {
      case 'dashboard': return 'Dashboard'
      case 'connectors': return 'Connectors'
      case 'ingestions': return 'Data Ingestions'
      case 'migration': return 'Pipeline Migration'
      case 'objectives': return 'Business Objectives'
      case 'mapping': return 'Data Model Mapping'
      case 'settings': return 'Settings'
      default: return 'Dashboard'
    }
  }

  const getPageDescription = (tab: string) => {
    switch (tab) {
      case 'dashboard': return 'Monitor your data integration pipelines'
      case 'connectors': return 'Manage your data sources and destinations'
      case 'ingestions': return 'Monitor and manage your data ingestion pipelines'
      case 'migration': return 'Monitor code analysis and automated migration jobs'
      case 'objectives': return 'AI-driven pipeline generation based on business goals'
      case 'mapping': return 'Automated mapping to standard data models'
      case 'settings': return 'Manage your account, workspace, and platform preferences'
      default: return 'Monitor your data integration pipelines'
    }
  }

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {getPageTitle(activeTab)}
          </h1>
          <p className="text-sm text-gray-600">
            {getPageDescription(activeTab)}
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input 
              type="text" 
              placeholder="Search..." 
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          {/* Notifications */}
          <button className="p-2 rounded-lg hover:bg-gray-100 relative">
            <Bell className="w-5 h-5 text-gray-600" />
            <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
          </button>
          
          {/* User Menu */}
          <div className="relative">
            <button 
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100"
            >
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-blue-600" />
              </div>
              <span className="text-sm font-medium text-gray-700">
                {session?.user?.name || 'User'}
              </span>
            </button>
            
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                <div className="px-4 py-2 text-sm text-gray-700 border-b">
                  <div className="font-medium">{session?.user?.name}</div>
                  <div className="text-gray-500">{session?.user?.email}</div>
                </div>
                <button
                  onClick={() => signOut()}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign out
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
```

## Dashboard Implementation

### 1. Onboarding Guide Component

Create `src/components/dashboard/OnboardingGuide.tsx`:

```typescript
'use client'
import { useState } from 'react'
import { X, ChevronRight, CheckCircle } from 'lucide-react'
import { useAppStore } from '@/store/useAppStore'

const onboardingSteps = [
  {
    id: 1,
    title: 'Set up your data sources',
    description: 'Connect to databases, APIs, and code repositories',
    action: 'Go to Connectors',
    targetTab: 'connectors'
  },
  {
    id: 2,
    title: 'Create ingestion pipelines',
    description: 'Move data from sources to destinations',
    action: 'Create Ingestion',
    targetTab: 'ingestions'
  },
  {
    id: 3,
    title: 'Migrate legacy systems',
    description: 'Use AI-powered tools to modernize your pipelines',
    action: 'Start Migration',
    targetTab: 'migration'
  },
  {
    id: 4,
    title: 'Define business objectives',
    description: 'Create analytics pipelines driven by business goals',
    action: 'Set Objectives',
    targetTab: 'objectives'
  },
  {
    id: 5,
    title: 'Map to standard models',
    description: 'Ensure compliance with industry standards',
    action: 'Map Data Models',
    targetTab: 'mapping'
  }
]

export default function OnboardingGuide() {
  const { toggleOnboarding, setActiveTab } = useAppStore()
  const [completedSteps, setCompletedSteps] = useState<number[]>([])

  const handleStepAction = (targetTab: string, stepId: number) => {
    setActiveTab(targetTab)
    if (!completedSteps.includes(stepId)) {
      setCompletedSteps([...completedSteps, stepId])
    }
  }

  const markStepComplete = (stepId: number) => {
    if (!completedSteps.includes(stepId)) {
      setCompletedSteps([...completedSteps, stepId])
    }
  }

  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg m-6 p-6">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold text-blue-900">
            Welcome to DataFlow Platform! 🚀
          </h3>
          <p className="text-blue-700 text-sm mt-1">
            Get started with these essential steps to set up your data integration platform
          </p>
        </div>
        <button 
          onClick={toggleOnboarding}
          className="text-blue-400 hover:text-blue-600 p-1"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {onboardingSteps.map((step) => {
          const isCompleted = completedSteps.includes(step.id)
          
          return (
            <div 
              key={step.id} 
              className={`p-4 rounded-lg border-2 transition-all ${
                isCompleted 
                  ? 'border-green-200 bg-green-50' 
                  : 'border-blue-200 bg-white hover:border-blue-300'
              }`}
            >
              <div className="flex items-start justify-between mb-2">
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                  isCompleted 
                    ? 'bg-green-500 text-white' 
                    : 'bg-blue-100 text-blue-600'
                }`}>
                  {isCompleted ? <CheckCircle className="w-4 h-4" /> : step.id}
                </div>
                {!isCompleted && (
                  <button 
                    onClick={() => markStepComplete(step.id)}
                    className="text-xs text-gray-500 hover:text-gray-700"
                  >
                    Mark done
                  </button>
                )}
              </div>
              
              <h4 className="font-medium text-gray-900 text-sm mb-1">
                {step.title}
              </h4>
              <p className="text-xs text-gray-600 mb-3">
                {step.description}
              </p>
              
              {!isCompleted && (
                <button 
                  onClick={() => handleStepAction(step.targetTab, step.id)}
                  className="flex items-center text-xs font-medium text-blue-600 hover:text-blue-700"
                >
                  {step.action}
                  <ChevronRight className="w-3 h-3 ml-1" />
                </button>
              )}
            </div>
          )
        })}
      </div>

      <div className="mt-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="text-sm text-blue-700">
            Progress: {completedSteps.length}/{onboardingSteps.length} completed
          </div>
          <div className="w-32 bg-blue-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(completedSteps.length / onboardingSteps.length) * 100}%` }}
            />
          </div>
        </div>
        
        {completedSteps.length === onboardingSteps.length && (
          <div className="text-sm font-medium text-green-600">
            🎉 Onboarding complete!
          </div>
        )}
      </div>
    </div>
  )
}
```

### 2. Dashboard Stats Cards

Create `src/components/dashboard/StatsCards.tsx`:

```typescript
'use client'
import { Cable, Activity, CheckCircle, XCircle } from 'lucide-react'

interface StatCard {
  title: string
  value: string
  change: string
  changeType: 'positive' | 'negative' | 'neutral'
  icon: React.ComponentType<any>
  color: string
}

const statsData: StatCard[] = [
  {
    title: 'Active Ingestions',
    value: '12',
    change: '+2 this week',
    changeType: 'positive',
    icon: Cable,
    color: 'blue'
  },
  {
    title: 'Records Synced Today',
    value: '847K',
    change: '+12% from yesterday',
    changeType: 'positive',
    icon: Activity,
    color: 'green'
  },
  {
    title: 'Success Rate',
    value: '98.2%',
    change: '+0.3% this week',
    changeType: 'positive',
    icon: CheckCircle,
    color: 'green'
  },
  {
    title: 'Failed Jobs',
    value: '3',
    change: '+1 needs attention',
    changeType: 'negative',
    icon: XCircle,
    color: 'red'
  }
]

export default function StatsCards() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statsData.map((stat, index) => {
        const Icon = stat.icon
        
        return (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">{stat.value}</p>
                <p className={`text-xs mt-1 ${
                  stat.changeType === 'positive' ? 'text-green-600' :
                  stat.changeType === 'negative' ? 'text-red-600' :
                  'text-gray-600'
                }`}>
                  {stat.changeType === 'positive' && '↗ '}
                  {stat.changeType === 'negative' && '↗ '}
                  {stat.change}
                </p>
              </div>
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                stat.color === 'blue' ? 'bg-blue-100' :
                stat.color === 'green' ? 'bg-green-100' :
                stat.color === 'red' ? 'bg-red-100' :
                'bg-gray-100'
              }`}>
                <Icon className={`w-6 h-6 ${
                  stat.color === 'blue' ? 'text-blue-600' :
                  stat.color === 'green' ? 'text-green-600' :
                  stat.color === 'red' ? 'text-red-600' :
                  'text-gray-600'
                }`} />
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}
```

### 3. Main Dashboard Page

Create `src/app/dashboard/page.tsx`:

```typescript
'use client'
import { useAppStore } from '@/store/useAppStore'
import OnboardingGuide from '@/components/dashboard/OnboardingGuide'
import StatsCards from '@/components/dashboard/StatsCards'
import RecentJobs from '@/components/dashboard/RecentJobs'
import QuickActions from '@/components/dashboard/QuickActions'

export default function DashboardPage() {
  const { showOnboarding } = useAppStore()
  
  return (
    <div className="p-6 overflow-y-auto h-full space-y-8">
      {/* Onboarding Guide */}
      {showOnboarding && <OnboardingGuide />}
      
      {/* Stats Cards */}
      <StatsCards />
      
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Recent Jobs - Takes 2 columns */}
        <div className="xl:col-span-2">
          <RecentJobs />
        </div>
        
        {/* Quick Actions - Takes 1 column */}
        <div>
          <QuickActions />
        </div>
      </div>
    </div>
  )
}
```

## Connectors Module

### 1. Connector Types

Create `src/types/connectors.ts`:

```typescript
export interface Connector {
  id: string
  name: string
  description: string
  type: 'source' | 'destination'
  category: 'database' | 'warehouse' | 'repository' | 'api'
  icon: string
  status: 'connected' | 'disconnected' | 'error'
  connectionCount?: number
  lastConnected?: string
  configuration?: Record<string, any>
}

export interface ConnectorConfig {
  id: string
  name: string
  host?: string
  port?: number
  database?: string
  username?: string
  password?: string
  apiKey?: string
  repository?: string
  branch?: string
  [key: string]: any
}

export const sourceConnectors: Omit<Connector, 'id' | 'status' | 'connectionCount' | 'lastConnected'>[] = [
  {
    name: 'PostgreSQL',
    description: 'Relational database for transactional data',
    type: 'source',
    category: 'database',
    icon: '🐘'
  },
  {
    name: 'MySQL',
    description: 'Open-source relational database management',
    type: 'source',
    category: 'database',
    icon: '🐬'
  },
  {
    name: 'MS SQL',
    description: 'Microsoft SQL Server for enterprise data',
    type: 'source',
    category: 'database',
    icon: '🏢'
  },
  {
    name: 'GitHub',
    description: 'Code repository and version control integration',
    type: 'source',
    category: 'repository',
    icon: '🐙'
  },
  {
    name: 'GitLab',
    description: 'DevOps platform with Git repository management',
    type: 'source',
    category: 'repository',
    icon: '🦊'
  },
  {
    name: 'Snowflake',
    description: 'Cloud data warehouse for analytics',
    type: 'source',
    category: 'warehouse',
    icon: '❄️'
  },
  {
    name: 'Oracle',
    description: 'Enterprise database management system',
    type: 'source',
    category: 'database',
    icon: '🔶'
  },
  {
    name: 'Cosmos DB',
    description: 'Microsoft globally distributed database',
    type: 'source',
    category: 'database',
    icon: '🌌'
  }
]

export const destinationConnectors: Omit<Connector, 'id' | 'status' | 'connectionCount' | 'lastConnected'>[] = [
  {
    name: 'Databricks',
    description: 'Unified analytics platform for big data and machine learning',
    type: 'destination',
    category: 'warehouse',
    icon: '🧱'
  },
  {
    name: 'AWS Redshift',
    description: 'Cloud data warehouse for analytics and reporting',
    type: 'destination',
    category: 'warehouse',
    icon: '🔴'
  },
  {
    name: 'BigQuery Google',
    description: 'Serverless data warehouse for analytics and AI',
    type: 'destination',
    category: 'warehouse',
    icon: '📊'
  }
]
```

### 2. Connector Card Component

Create `src/components/connectors/ConnectorCard.tsx`:

```typescript
'use client'
import { useState } from 'react'
import { Settings, Play, Pause, AlertCircle } from 'lucide-react'
import type { Connector } from '@/types/connectors'

interface ConnectorCardProps {
  connector: Connector
  onConfigure: (connector: Connector) => void
  onTest: (connector: Connector) => void
}

export default function ConnectorCard({ 
  connector, 
  onConfigure, 
  onTest 
}: ConnectorCardProps) {
  const [isLoading, setIsLoading] = useState(false)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800'
      case 'disconnected': return 'bg-gray-100 text-gray-800'
      case 'error': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <Play className="w-3 h-3" />
      case 'disconnected': return <Pause className="w-3 h-3" />
      case 'error': return <AlertCircle className="w-3 h-3" />
      default: return <Pause className="w-3 h-3" />
    }
  }

  const handleTest = async () => {
    setIsLoading(true)
    try {
      await onTest(connector)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-all group">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-4">
          <div className="text-3xl">{connector.icon}</div>
          <div>
            <h3 className="font-semibold text-gray-900">{connector.name}</h3>
            <p className="text-sm text-gray-600 mt-1 max-w-xs">
              {connector.description}
            </p>
          </div>
        </div>
        
        <div className="flex flex-col items-end space-y-2">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(connector.status)}`}>
            {getStatusIcon(connector.status)}
            <span className="ml-1 capitalize">{connector.status}</span>
          </span>
          
          {connector.connectionCount !== undefined && (
            <span className="text-xs text-gray-500">
              {connector.connectionCount} connection{connector.connectionCount !== 1 ? 's' : ''}
            </span>
          )}
        </div>
      </div>

      {connector.lastConnected && (
        <div className="text-xs text-gray-500 mb-4">
          Last connected: {connector.lastConnected}
        </div>
      )}

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
            connector.category === 'database' ? 'bg-blue-100 text-blue-800' :
            connector.category === 'warehouse' ? 'bg-purple-100 text-purple-800' :
            connector.category === 'repository' ? 'bg-green-100 text-green-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {connector.category}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          {connector.status === 'connected' && (
            <button
              onClick={handleTest}
              disabled={isLoading}
              className="text-xs text-blue-600 hover:text-blue-700 font-medium disabled:opacity-50"
            >
              {isLoading ? 'Testing...' : 'Test'}
            </button>
          )}
          
          <button
            onClick={() => onConfigure(connector)}
            className="flex items-center space-x-1 text-blue-600 hover:text-blue-700 font-medium"
          >
            <Settings className="w-4 h-4" />
            <span className="text-sm">Configure</span>
          </button>
        </div>
      </div>
    </div>
  )
}
```

### 3. Connectors Page

Create `src/app/connectors/page.tsx`:

```typescript
'use client'
import { useState, useEffect } from 'react'
import { Plus, Search, Filter } from 'lucide-react'
import ConnectorCard from '@/components/connectors/ConnectorCard'
import ConnectorModal from '@/components/connectors/ConnectorModal'
import { sourceConnectors, destinationConnectors } from '@/types/connectors'
import type { Connector } from '@/types/connectors'
import toast from 'react-hot-toast'

export default function ConnectorsPage() {
  const [activeTab, setActiveTab] = useState<'sources' | 'destinations'>('sources')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [connectors, setConnectors] = useState<Connector[]>([])
  const [showModal, setShowModal] = useState(false)
  const [selectedConnector, setSelectedConnector] = useState<Connector | null>(null)

  // Mock data - in real app, this would come from API
  useEffect(() => {
    const mockConnectors: Connector[] = [
      ...sourceConnectors.map((conn, index) => ({
        ...conn,
        id: `source-${index}`,
        status: index % 3 === 0 ? 'connected' : index % 3 === 1 ? 'disconnected' : 'error',
        connectionCount: Math.floor(Math.random() * 5),
        lastConnected: index % 2 === 0 ? '2 hours ago' : '1 day ago'
      })),
      ...destinationConnectors.map((conn, index) => ({
        ...conn,
        id: `destination-${index}`,
        status: index % 2 === 0 ? 'connected' : 'disconnected',
        connectionCount: Math.floor(Math.random() * 3),
        lastConnected: index % 2 === 0 ? '1 hour ago' : '3 days ago'
      }))
    ] as Connector[]
    
    setConnectors(mockConnectors)
  }, [])

  const currentConnectors = connectors.filter(conn => {
    const matchesTab = conn.type === activeTab.slice(0, -1) // Remove 's' from 'sources'/'destinations'
    const matchesSearch = conn.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         conn.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || conn.category === selectedCategory
    
    return matchesTab && matchesSearch && matchesCategory
  })

  const categories = Array.from(new Set(connectors.map(conn => conn.category)))

  const handleConfigure = (connector: Connector) => {
    setSelectedConnector(connector)
    setShowModal(true)
  }

  const handleTest = async (connector: Connector) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success(`${connector.name} connection test successful`)
    } catch (error) {
      toast.error(`${connector.name} connection test failed`)
    }
  }

  const handleSaveConnector = async (connectorData: any) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Connector saved successfully')
      setShowModal(false)
      setSelectedConnector(null)
    } catch (error) {
      toast.error('Failed to save connector')
    }
  }

  return (
    <div className="p-6 overflow-y-auto h-full">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {activeTab === 'sources' ? 'Data Sources' : 'Data Destinations'}
          </h1>
          <p className="text-sm text-gray-600">
            {activeTab === 'sources' 
              ? 'Manage your data sources and configure extraction settings' 
              : 'Manage your data destinations and configure loading settings'
            }
          </p>
        </div>
        <button 
          onClick={() => {
            setSelectedConnector(null)
            setShowModal(true)
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add {activeTab === 'sources' ? 'Source' : 'Destination'}</span>
        </button>
      </div>

      {/* Tabs and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex">
              <button
                onClick={() => setActiveTab('sources')}
                className={`px-6 py-3 text-sm font-medium border-b-2 ${
                  activeTab === 'sources'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Sources ({connectors.filter(c => c.type === 'source').length})
              </button>
              <button
                onClick={() => setActiveTab('destinations')}
                className={`px-6 py-3 text-sm font-medium border-b-2 ${
                  activeTab === 'destinations'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Destinations ({connectors.filter(c => c.type === 'destination').length})
              </button>
            </div>
            
            <div className="flex items-center space-x-4 px-6">
              {/* Search */}
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search connectors..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              {/* Category Filter */}
              <div className="relative">
                <Filter className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Connectors Grid */}
        <div className="p-6">
          {currentConnectors.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {currentConnectors.map((connector) => (
                <ConnectorCard
                  key={connector.id}
                  connector={connector}
                  onConfigure={handleConfigure}
                  onTest={handleTest}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">🔌</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No connectors found
              </h3>
              <p className="text-gray-600 mb-4">
                {searchQuery || selectedCategory !== 'all'
                  ? 'Try adjusting your search or filters'
                  : `No ${activeTab} have been configured yet`
                }
              </p>
              {!searchQuery && selectedCategory === 'all' && (
                <button 
                  onClick={() => setShowModal(true)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Add Your First {activeTab === 'sources' ? 'Source' : 'Destination'}
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Connector Configuration Modal */}
      {showModal && (
        <ConnectorModal
          connector={selectedConnector}
          type={activeTab === 'sources' ? 'source' : 'destination'}
          onSave={handleSaveConnector}
          onClose={() => {
            setShowModal(false)
            setSelectedConnector(null)
          }}
        />
      )}
    </div>
  )
}
```

This comprehensive frontend development guide provides junior developers with:

1. **Complete project setup** with all necessary dependencies
2. **Step-by-step component implementation** with detailed code examples
3. **Proper TypeScript types** and interfaces
4. **State management patterns** using Zustand
5. **Authentication flow** with NextAuth.js
6. **Reusable component patterns** and best practices
7. **Error handling** and loading states
8. **Responsive design** with Tailwind CSS

Each section builds upon the previous one, providing a clear learning path for implementing the entire data integration platform frontend.