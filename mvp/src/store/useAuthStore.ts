import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { authService, type AuthResponse } from '@/lib/authService'
import type { User, LoginCredentials, SignupData, ForgotPasswordData } from '@/types/auth'

export interface AuthState {
  // State
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  isInitialized: boolean

  // Actions
  login: (credentials: LoginCredentials) => Promise<void>
  register: (data: SignupData) => Promise<void>
  logout: () => void
  forgotPassword: (data: ForgotPasswordData) => Promise<void>
  clearError: () => void
  setUser: (user: User | null) => void
  checkAuth: () => Promise<void>
  initialize: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    immer((set, get) => ({
      // Initial State
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      isInitialized: false,
      isInitialized: false,

      // Actions
      login: async (credentials: LoginCredentials) => {
        set((state) => {
          state.isLoading = true
          state.error = null
        })

        try {
          const response: AuthResponse = await authService.login(credentials)
          
          // For now, we'll create a basic user object from the response
          // In a real app, you might need to fetch user details separately
          const user: User = {
            id: response.user_id,
            email: credentials.email,
            name: credentials.email.split('@')[0], // Temporary name from email
          }

          set((state) => {
            state.user = user
            state.isAuthenticated = true
            state.isLoading = false
            state.error = null
          })
        } catch (error: any) {
          set((state) => {
            state.isLoading = false
            state.error = error.response?.data?.message || error.message || 'Login failed'
            state.isAuthenticated = false
            state.user = null
          })
          throw error
        }
      },

      register: async (data: SignupData) => {
        set((state) => {
          state.isLoading = true
          state.error = null
        })

        try {
          await authService.register(data)
          
          set((state) => {
            state.isLoading = false
            state.error = null
          })
        } catch (error: any) {
          set((state) => {
            state.isLoading = false
            state.error = error.response?.data?.message || error.message || 'Registration failed'
          })
          throw error
        }
      },

      logout: () => {
        authService.logout()
        set((state) => {
          state.user = null
          state.isAuthenticated = false
          state.error = null
        })
      },

      forgotPassword: async (data: ForgotPasswordData) => {
        set((state) => {
          state.isLoading = true
          state.error = null
        })

        try {
          await authService.forgotPassword(data)
          
          set((state) => {
            state.isLoading = false
            state.error = null
          })
        } catch (error: any) {
          set((state) => {
            state.isLoading = false
            state.error = error.response?.data?.message || error.message || 'Failed to send reset email'
          })
          throw error
        }
      },

      clearError: () => {
        set((state) => {
          state.error = null
        })
      },

      setUser: (user: User | null) => {
        set((state) => {
          state.user = user
          state.isAuthenticated = !!user
        })
      },

      checkAuth: async () => {
        set((state) => {
          state.isLoading = true
        })

        try {
          const hasToken = authService.isAuthenticated()

          if (hasToken) {
            // Token exists, but we should verify it's still valid
            // For now, we'll trust the token if it exists
            // In a real app, you might want to make a request to verify the token
            set((state) => {
              state.isAuthenticated = true
              state.isLoading = false
              state.isInitialized = true
            })
          } else {
            // No token found
            set((state) => {
              state.isAuthenticated = false
              state.user = null
              state.isLoading = false
              state.isInitialized = true
            })
          }
        } catch (error) {
          // Token validation failed
          authService.logout()
          set((state) => {
            state.isAuthenticated = false
            state.user = null
            state.isLoading = false
            state.isInitialized = true
          })
        }
      },

      initialize: () => {
        // This function will be called when the store is hydrated from localStorage
        const hasToken = authService.isAuthenticated()
        set((state) => {
          state.isAuthenticated = hasToken
          state.isInitialized = true
          if (!hasToken) {
            state.user = null
          }
        })
      },
    })),
    {
      name: 'auth-store',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // After rehydration, initialize the auth state
        if (state) {
          state.initialize()
        }
      },
    }
  )
)

// Selector hooks for performance optimization
export const useAuth = () => useAuthStore((state) => ({
  user: state.user,
  isAuthenticated: state.isAuthenticated,
  isLoading: state.isLoading,
  error: state.error,
  isInitialized: state.isInitialized,
}))

export const useAuthActions = () => useAuthStore((state) => ({
  login: state.login,
  register: state.register,
  logout: state.logout,
  forgotPassword: state.forgotPassword,
  clearError: state.clearError,
  setUser: state.setUser,
  checkAuth: state.checkAuth,
  initialize: state.initialize,
}))
