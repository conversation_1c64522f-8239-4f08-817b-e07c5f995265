import apiClient from './apiClient'
import type { User, LoginCredentials, SignupData, ForgotPasswordData, ResetPasswordData } from '@/types/auth'

export interface AuthResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user_id: string
}

export interface MessageResponse {
  message: string
  success: boolean
}

export class AuthService {
  // Login
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials)

    // Store tokens with expiration info
    apiClient.setTokens(response.access_token, response.refresh_token, response.expires_in)

    return response
  }

  // Register
  async register(data: SignupData): Promise<MessageResponse> {
    return apiClient.post<MessageResponse>('/auth/signup', data)
  }

  // Forgot password
  async forgotPassword(data: ForgotPasswordData): Promise<MessageResponse> {
    return apiClient.post<MessageResponse>('/auth/forgot-password', data)
  }

  // Reset password
  async resetPassword(data: ResetPasswordData): Promise<MessageResponse> {
    return apiClient.post<MessageResponse>('/auth/reset-password', data)
  }

  // Refresh token (legacy method for manual refresh)
  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/refresh', { refresh_token: refreshToken })

    // Update stored access token with expiration info
    apiClient.setTokens(response.access_token, response.refresh_token, response.expires_in)

    return response
  }

  // Automatic token refresh using stored refresh token
  async refreshAccessToken(): Promise<boolean> {
    return apiClient.refreshAccessToken()
  }

  // Logout
  logout(): void {
    apiClient.clearTokens()
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!apiClient.getAccessToken()
  }

  // Check if token is valid (not expired)
  isTokenValid(): boolean {
    return apiClient.isTokenValid()
  }

  // Get current access token
  getAccessToken(): string | null {
    return apiClient.getAccessToken()
  }

  // Get current refresh token
  getRefreshToken(): string | null {
    return apiClient.getRefreshToken()
  }

  // Ensure token is valid before making requests
  async ensureValidToken(): Promise<void> {
    if (!this.isTokenValid()) {
      const refreshed = await this.refreshAccessToken()
      if (!refreshed) {
        throw new Error('Unable to refresh token')
      }
    }
  }
}

export const authService = new AuthService()
export default authService
