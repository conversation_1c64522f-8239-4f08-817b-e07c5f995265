import apiClient from './apiClient'
import type { User, LoginCredentials, SignupData, ForgotPasswordData, ResetPasswordData } from '@/types/auth'

export interface AuthResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user_id: string
}

export interface MessageResponse {
  message: string
  success: boolean
}

export class AuthService {
  // Login
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials)
    
    // Store tokens
    apiClient.setTokens(response.access_token, response.refresh_token)
    
    return response
  }

  // Register
  async register(data: SignupData): Promise<MessageResponse> {
    return apiClient.post<MessageResponse>('/auth/signup', data)
  }

  // Forgot password
  async forgotPassword(data: ForgotPasswordData): Promise<MessageResponse> {
    return apiClient.post<MessageResponse>('/auth/forgot-password', data)
  }

  // Reset password
  async resetPassword(data: ResetPasswordData): Promise<MessageResponse> {
    return apiClient.post<MessageResponse>('/auth/reset-password', data)
  }

  // Refresh token
  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/refresh', { refresh_token: refreshToken })
    
    // Update stored access token
    apiClient.setTokens(response.access_token, response.refresh_token)
    
    return response
  }

  // Logout
  logout(): void {
    apiClient.clearTokens()
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!apiClient.getAccessToken()
  }
}

export const authService = new AuthService()
export default authService
