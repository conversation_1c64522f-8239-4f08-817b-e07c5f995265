import apiClient from './apiClient'
import { authService } from './authService'

export interface AirbyteSourceDefinition {
  id: string
  sourceDefinitionId: string
  name: string
  dockerRepository: string
  dockerImageTag: string
  documentationUrl: string
  icon?: string
  sourceType: string
  spec: {
    documentationUrl: string
    connectionSpecification: any
  }
  tombstone: boolean
  public: boolean
  custom: boolean
  releaseStage: string
  releaseDate?: string
  resourceRequirements?: any
  protocolVersion: string
  normalizationConfig?: any
  supportsDbt: boolean
  suggestedStreams?: any
}

export interface AirbyteDestinationDefinition {
  id: string
  destinationDefinitionId: string
  name: string
  dockerRepository: string
  dockerImageTag: string
  documentationUrl: string
  icon?: string
  spec: {
    documentationUrl: string
    connectionSpecification: any
  }
  tombstone: boolean
  public: boolean
  custom: boolean
  releaseStage: string
  releaseDate?: string
  resourceRequirements?: any
  protocolVersion: string
  normalizationConfig?: any
  supportsDbt: boolean
  suggestedStreams?: any
}

export interface WorkspaceResponse {
  workspaceId: string
  name: string
  slug: string
  initialSetupComplete: boolean
  displaySetupWizard: boolean
  anonymousDataCollection: boolean
  news: boolean
  securityUpdates: boolean
  notifications: any[]
  notificationSettings: any
  defaultGeography: string
  webhookConfigs: any[]
}

export interface AirbyteSource {
  sourceId: string
  name: string
  sourceDefinitionId: string
  workspaceId: string
  connectionConfiguration: any
  sourceName?: string
  sourceType?: string
}

export interface AirbyteDestination {
  destinationId: string
  name: string
  destinationDefinitionId: string
  workspaceId: string
  connectionConfiguration: any
  destinationName?: string
  destinationType?: string
}

export class AirbyteService {
  private workspaceId: string | null = null

  // Ensure token is valid before making API calls
  private async ensureValidToken(): Promise<void> {
    try {
      await authService.ensureValidToken()
    } catch (error) {
      console.error('Token refresh failed in AirbyteService:', error)
      throw error
    }
  }

  // Get workspace ID for the current user
  async getWorkspaceId(userId: string): Promise<string> {
    if (this.workspaceId) {
      return this.workspaceId
    }

    try {
      // Ensure token is valid before making the request
      await this.ensureValidToken()

      // Debug: Check if we have an access token
      const token = apiClient.getAccessToken()
      console.log('Making /get_workspace_id request with token:', token ? 'Present' : 'Missing')

      const response = await apiClient.post<{ workspace_id: string }>('/get_workspace_id', {
        user_id: userId
      })

      this.workspaceId = response.workspace_id
      return this.workspaceId
    } catch (error) {
      console.error('Failed to get workspace ID:', error)
      console.error('Error details:', error)
      throw new Error('Failed to get workspace ID')
    }
  }

  // Get available source definitions from Airbyte
  async getSourceDefinitions(userId: string): Promise<AirbyteSourceDefinition[]> {
    try {
      // Ensure token is valid before making the request
      await this.ensureValidToken()

      const workspaceId = await this.getWorkspaceId(userId)
      console.log('Making Airbyte sources request for workspace:', workspaceId)

      const response = await apiClient.get<{ data: AirbyteSourceDefinition[] }>(
        `/airbyte/api/public/v1/workspaces/${workspaceId}/definitions/sources`
      )

      // console.log('Airbyte sources response:', response)
      const available_sources = [
        'Postgres',
        'MySQL',
        'Microsoft SQL Server (MSSQL)',
        'Oracle DB',
        'Snowflake',
        'Google Sheets',
        'BigQuery',
        'Redshift',
        'Github',
        'Gitlab',
        // 'MongoDB',
        'Google Cloud Storage (GCS)',
        'Azure Blob Storage'
      ].map(source => source.toLowerCase())

      // Filter out tombstoned definitions and sort by name
      return response.data
        //filter with normalize lower text
        .filter(def => available_sources.includes(def.name.toLowerCase()))
        .sort((a, b) => a.name.localeCompare(b.name))
    } catch (error) {
      console.error('Failed to fetch source definitions:', error)
      console.error('Error details:', error)
      throw new Error('Failed to fetch source definitions')
    }
  }

  // Get available destination definitions from Airbyte
  async getDestinationDefinitions(userId: string): Promise<AirbyteDestinationDefinition[]> {
    try {
      // Ensure token is valid before making the request
      await this.ensureValidToken()

      const workspaceId = await this.getWorkspaceId(userId)
      console.log('Making Airbyte destinations request for workspace:', workspaceId)

      const response = await apiClient.get<{ data: AirbyteDestinationDefinition[] }>(
        `/airbyte/api/public/v1/workspaces/${workspaceId}/definitions/destinations`
      )

      const available_destinations = [
        'Databricks Lakehouse',
        'Redshift',
        'BigQuery',
        'Snowflake',
        'S3',
        // 'MongoDB',
        'Google Cloud Storage (GCS)',
        'Azure Blob Storage'
      ].map(dest => dest.toLowerCase())

      return response.data
        .filter(def => available_destinations.includes(def.name.toLowerCase()))
        .sort((a, b) => a.name.localeCompare(b.name))
    } catch (error) {
      console.error('Failed to fetch destination definitions:', error)
      console.error('Error details:', error)
      throw new Error('Failed to fetch destination definitions')
    }
  }

  // Get workspace information
  async getWorkspaceInfo(userId: string): Promise<WorkspaceResponse> {
    try {
      const workspaceId = await this.getWorkspaceId(userId)
      const response = await apiClient.get<WorkspaceResponse>(
        `/airbyte/api/public/v1/workspaces/${workspaceId}`
      )
      
      return response
    } catch (error) {
      console.error('Failed to fetch workspace info:', error)
      throw new Error('Failed to fetch workspace info')
    }
  }

  // Get actual sources created in the workspace
  async getSources(userId: string): Promise<AirbyteSource[]> {
    try {
      // Ensure token is valid before making the request
      await this.ensureValidToken()

      const workspaceId = await this.getWorkspaceId(userId)
      console.log('Making Airbyte sources request for workspace:', workspaceId)

      const response = await apiClient.get<{ data: AirbyteSource[] }>(
        `/airbyte/api/public/v1/sources?workspaceIds=${workspaceId}`
      )

      // console.log('Airbyte sources response:', response)
      return response.data || []
    } catch (error) {
      console.error('Error fetching sources:', error)
      throw error
    }
  }

  // Get actual destinations created in the workspace
  async getDestinations(userId: string): Promise<AirbyteDestination[]> {
    try {
      // Ensure token is valid before making the request
      await this.ensureValidToken()

      const workspaceId = await this.getWorkspaceId(userId)
      console.log('Making Airbyte destinations request for workspace:', workspaceId)

      const response = await apiClient.get<{ data: AirbyteDestination[] }>(
        `/airbyte/api/public/v1/destinations?workspaceIds=${workspaceId}`
      )

      // console.log('Airbyte destinations response:', response)
      return response.data || []
    } catch (error) {
      console.error('Error fetching destinations:', error)
      throw error
    }
  }

  // Get a specific source by ID
  async getSource(userId: string, sourceId: string): Promise<AirbyteSource> {
    try {
      await this.ensureValidToken()

      const response = await apiClient.get<AirbyteSource>(
        `/airbyte/api/public/v1/sources/${sourceId}`
      )

      return response
    } catch (error) {
      console.error('Error fetching source:', error)
      throw error
    }
  }

  // Get a specific destination by ID
  async getDestination(userId: string, destinationId: string): Promise<AirbyteDestination> {
    try {
      await this.ensureValidToken()

      const response = await apiClient.get<AirbyteDestination>(
        `/airbyte/api/public/v1/destinations/${destinationId}`
      )

      return response
    } catch (error) {
      console.error('Error fetching destination:', error)
      throw error
    }
  }

  // Create a new source connector
  async createSource(userId: string, sourceData: {
    name: string
    definitionId: string
    configuration: any
  }): Promise<AirbyteSource> {
    try {
      // Ensure token is valid before making the request
      await this.ensureValidToken()

      const workspaceId = await this.getWorkspaceId(userId)

      const response = await apiClient.post<AirbyteSource>(
        '/airbyte/api/public/v1/sources',
        {
          name: sourceData.name,
          definitionId: sourceData.definitionId,
          workspaceId: workspaceId,
          configuration: sourceData.configuration
        }
      )

      return response
    } catch (error) {
      console.error('Error creating source:', error)
      throw error
    }
  }

  // Create a new destination connector
  async createDestination(userId: string, destinationData: {
    name: string
    definitionId: string
    configuration: any
  }): Promise<AirbyteDestination> {
    try {
      // Ensure token is valid before making the request
      await this.ensureValidToken()

      const workspaceId = await this.getWorkspaceId(userId)

      const response = await apiClient.post<AirbyteDestination>(
        '/airbyte/api/public/v1/destinations',
        {
          name: destinationData.name,
          definitionId: destinationData.definitionId,
          workspaceId: workspaceId,
          configuration: destinationData.configuration
        }
      )

      return response
    } catch (error) {
      console.error('Error creating destination:', error)
      throw error
    }
  }

  // Update an existing source connector
  async updateSource(userId: string, sourceId: string, sourceData: {
    name: string
    configuration: any
  }): Promise<AirbyteSource> {
    try {
      // Ensure token is valid before making the request
      await this.ensureValidToken()

      const response = await apiClient.put<AirbyteSource>(`/airbyte/api/public/v1/sources/${sourceId}`, {
        name: sourceData.name,
        connectionConfiguration: sourceData.configuration
      })

      return response
    } catch (error) {
      console.error('Error updating source:', error)
      throw error
    }
  }

  // Update an existing destination connector
  async updateDestination(userId: string, destinationId: string, destinationData: {
    name: string
    configuration: any
  }): Promise<AirbyteDestination> {
    try {
      // Ensure token is valid before making the request
      await this.ensureValidToken()

      const response = await apiClient.put<AirbyteDestination>(`/airbyte/api/public/v1/destinations/${destinationId}`, {
        name: destinationData.name,
        connectionConfiguration: destinationData.configuration
      })

      return response
    } catch (error) {
      console.error('Error updating destination:', error)
      throw error
    }
  }

  // Check source connection before creating
  async checkSourceConnection(userId: string, sourceDefinitionId: string, configuration: any): Promise<{ status: 'succeeded' | 'failed', message?: string }> {
    try {
      await this.ensureValidToken()

      const workspaceId = await this.getWorkspaceId(userId)

      const response = await apiClient.post<{ status: 'succeeded' | 'failed', message?: string }>(
        '/airbyte/api/v1/scheduler/sources/check_connection',
        {
          connectionConfiguration: configuration,
          workspaceId: workspaceId,
          sourceDefinitionId: sourceDefinitionId
        }
      )

      return response
    } catch (error) {
      console.error('Error checking source connection:', error)
      throw error
    }
  }

  // Check destination connection before creating
  async checkDestinationConnection(userId: string, destinationDefinitionId: string, configuration: any): Promise<{ status: 'succeeded' | 'failed', message?: string }> {
    try {
      await this.ensureValidToken()

      const workspaceId = await this.getWorkspaceId(userId)

      const response = await apiClient.post<{ status: 'succeeded' | 'failed', message?: string }>(
        '/airbyte/api/v1/scheduler/destinations/check_connection',
        {
          connectionConfiguration: configuration,
          workspaceId: workspaceId,
          destinationDefinitionId: destinationDefinitionId
        }
      )

      return response
    } catch (error) {
      console.error('Error checking destination connection:', error)
      throw error
    }
  }

  // Delete a source connector
  async deleteSource(userId: string, sourceId: string): Promise<void> {
    try {
      await this.ensureValidToken()

      const workspaceId = await this.getWorkspaceId(userId)
      await apiClient.delete(`/airbyte/api/public/v1/sources/${sourceId}?workspaceId=${workspaceId}`)
    } catch (error) {
      console.error('Error deleting source:', error)
      throw error
    }
  }

  // Delete a destination connector
  async deleteDestination(userId: string, destinationId: string): Promise<void> {
    try {
      await this.ensureValidToken()

      const workspaceId = await this.getWorkspaceId(userId)
      await apiClient.delete(`/airbyte/api/public/v1/destinations/${destinationId}?workspaceId=${workspaceId}`)
    } catch (error) {
      console.error('Error deleting destination:', error)
      throw error
    }
  }

  // Clear cached workspace ID (useful for logout)
  clearCache(): void {
    this.workspaceId = null
  }
}

export const airbyteService = new AirbyteService()
export default airbyteService
