import apiClient from './apiClient'

export interface AirbyteSourceDefinition {
  id: string
  sourceDefinitionId: string
  name: string
  dockerRepository: string
  dockerImageTag: string
  documentationUrl: string
  icon?: string
  sourceType: string
  spec: {
    documentationUrl: string
    connectionSpecification: any
  }
  tombstone: boolean
  public: boolean
  custom: boolean
  releaseStage: string
  releaseDate?: string
  resourceRequirements?: any
  protocolVersion: string
  normalizationConfig?: any
  supportsDbt: boolean
  suggestedStreams?: any
}

export interface AirbyteDestinationDefinition {
  id: string
  destinationDefinitionId: string
  name: string
  dockerRepository: string
  dockerImageTag: string
  documentationUrl: string
  icon?: string
  spec: {
    documentationUrl: string
    connectionSpecification: any
  }
  tombstone: boolean
  public: boolean
  custom: boolean
  releaseStage: string
  releaseDate?: string
  resourceRequirements?: any
  protocolVersion: string
  normalizationConfig?: any
  supportsDbt: boolean
  suggestedStreams?: any
}

export interface WorkspaceResponse {
  workspaceId: string
  name: string
  slug: string
  initialSetupComplete: boolean
  displaySetupWizard: boolean
  anonymousDataCollection: boolean
  news: boolean
  securityUpdates: boolean
  notifications: any[]
  notificationSettings: any
  defaultGeography: string
  webhookConfigs: any[]
}

export class AirbyteService {
  private workspaceId: string | null = null

  // Get workspace ID for the current user
  async getWorkspaceId(userId: string): Promise<string> {
    if (this.workspaceId) {
      return this.workspaceId
    }

    try {
      // Debug: Check if we have an access token
      const token = apiClient.getAccessToken()
      console.log('Making /get_workspace_id request with token:', token ? 'Present' : 'Missing')

      const response = await apiClient.post<{ workspace_id: string }>('/get_workspace_id', {
        user_id: userId
      })

      this.workspaceId = response.workspace_id
      return this.workspaceId
    } catch (error) {
      console.error('Failed to get workspace ID:', error)
      console.error('Error details:', error)
      throw new Error('Failed to get workspace ID')
    }
  }

  // Get available source definitions from Airbyte
  async getSourceDefinitions(userId: string): Promise<AirbyteSourceDefinition[]> {
    try {
      const workspaceId = await this.getWorkspaceId(userId)
      console.log('Making Airbyte sources request for workspace:', workspaceId)

      const response = await apiClient.get<{ data: AirbyteSourceDefinition[] }>(
        `/airbyte/api/public/v1/workspaces/${workspaceId}/definitions/sources`
      )

      // console.log('Airbyte sources response:', response)
      const available_sources = [
        'Postgres',
        'MySQL',
        'Microsoft SQL Server (MSSQL)',
        'Oracle DB',
        'Snowflake',
        'Google Sheets',
        'BigQuery',
        'Redshift',
        'MongoDB',
        'Google Cloud Storage (GCS)',
        'Azure Blob Storage'
      ].map(source => source.toLowerCase())

      // Filter out tombstoned definitions and sort by name
      return response.data
        //filter with normalize lower text
        .filter(def => available_sources.includes(def.name.toLowerCase()))
        .sort((a, b) => a.name.localeCompare(b.name))
    } catch (error) {
      console.error('Failed to fetch source definitions:', error)
      console.error('Error details:', error)
      throw new Error('Failed to fetch source definitions')
    }
  }

  // Get available destination definitions from Airbyte
  async getDestinationDefinitions(userId: string): Promise<AirbyteDestinationDefinition[]> {
    try {
      const workspaceId = await this.getWorkspaceId(userId)
      console.log('Making Airbyte destinations request for workspace:', workspaceId)

      const response = await apiClient.get<{ data: AirbyteDestinationDefinition[] }>(
        `/airbyte/api/public/v1/workspaces/${workspaceId}/definitions/destinations`
      )

      console
      

      return response.data
        .filter(def => !def.tombstone && def.public)
        .sort((a, b) => a.name.localeCompare(b.name))
    } catch (error) {
      console.error('Failed to fetch destination definitions:', error)
      console.error('Error details:', error)
      throw new Error('Failed to fetch destination definitions')
    }
  }

  // Get workspace information
  async getWorkspaceInfo(userId: string): Promise<WorkspaceResponse> {
    try {
      const workspaceId = await this.getWorkspaceId(userId)
      const response = await apiClient.get<WorkspaceResponse>(
        `/airbyte/api/public/v1/workspaces/${workspaceId}`
      )
      
      return response
    } catch (error) {
      console.error('Failed to fetch workspace info:', error)
      throw new Error('Failed to fetch workspace info')
    }
  }

  // Clear cached workspace ID (useful for logout)
  clearCache(): void {
    this.workspaceId = null
  }
}

export const airbyteService = new AirbyteService()
export default airbyteService
