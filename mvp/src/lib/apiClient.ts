import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

export interface ApiResponse<T = any> {
  data: T
  message?: string
  success?: boolean
}

export interface ApiError {
  message: string
  status: number
  details?: any
}

class ApiClient {
  private client: AxiosInstance
  private isRefreshing: boolean = false
  private failedQueue: Array<{
    resolve: (value?: any) => void
    reject: (reason?: any) => void
  }> = []
  private tokenExpirationTime: number | null = null
  private refreshBuffer: number = 5 * 60 * 1000 // 5 minutes before expiration

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_BACKEND_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Initialize token expiration time from localStorage
    this.initializeTokenExpiration()

    this.setupInterceptors()
  }

  private initializeTokenExpiration(): void {
    if (typeof window !== 'undefined') {
      const storedExpiration = localStorage.getItem('token_expiration')
      if (storedExpiration) {
        this.tokenExpirationTime = parseInt(storedExpiration)
      }
    }
  }

  private setupInterceptors() {
    // Request interceptor - Add auth token and proactive refresh
    this.client.interceptors.request.use(
      async (config) => {
        // Check if token needs refresh before making the request
        await this.ensureValidToken()

        const token = this.getStoredToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
          console.log(`Making ${config.method?.toUpperCase()} request to ${config.url} with auth token`)
        } else {
          console.warn(`Making ${config.method?.toUpperCase()} request to ${config.url} WITHOUT auth token`)
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Response interceptor - Handle token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        console.error('API Request failed:', {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        })

        const originalRequest = error.config

        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject })
            }).then(token => {
              originalRequest.headers.Authorization = `Bearer ${token}`
              return this.client(originalRequest)
            }).catch(err => {
              return Promise.reject(err)
            })
          }

          originalRequest._retry = true
          this.isRefreshing = true

          try {
            const refreshToken = this.getStoredRefreshToken()
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken)
              const { access_token } = response.data
              
              this.setStoredToken(access_token)
              this.processQueue(null, access_token)
              
              originalRequest.headers.Authorization = `Bearer ${access_token}`
              return this.client(originalRequest)
            }
          } catch (refreshError) {
            this.processQueue(refreshError, null)
            this.clearStoredTokens()
            // Redirect to login page
            if (typeof window !== 'undefined') {
              window.location.href = '/login'
            }
            return Promise.reject(refreshError)
          } finally {
            this.isRefreshing = false
          }
        }

        return Promise.reject(error)
      }
    )
  }

  private processQueue(error: any, token: string | null = null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error)
      } else {
        resolve(token)
      }
    })
    
    this.failedQueue = []
  }

  private getStoredToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('access_token')
    }
    return null
  }

  private getStoredRefreshToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('refresh_token')
    }
    return null
  }

  private setStoredToken(token: string) {
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', token)
    }
  }

  private clearStoredTokens() {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('token_timestamp')
      localStorage.removeItem('token_expiration')
    }
  }

  private async refreshToken(refreshToken: string) {
    return this.client.post('/auth/refresh', { refresh_token: refreshToken })
  }

  // Check if token needs refresh and refresh if necessary
  private async ensureValidToken(): Promise<void> {
    const refreshToken = this.getStoredRefreshToken()
    if (!refreshToken) {
      return // No refresh token available
    }

    // Check if token is expired or will expire soon
    if (this.isTokenExpiringSoon()) {
      try {
        await this.proactiveRefreshToken()
      } catch (error) {
        console.error('Proactive token refresh failed:', error)
        // Don't throw here, let the request proceed and handle 401 in response interceptor
      }
    }
  }

  // Check if token is expiring soon
  private isTokenExpiringSoon(): boolean {
    if (!this.tokenExpirationTime) {
      // If we don't have expiration time, check if token is older than 50 minutes
      const tokenTimestamp = this.getTokenTimestamp()
      if (tokenTimestamp) {
        const tokenAge = Date.now() - tokenTimestamp
        const fiftyMinutes = 50 * 60 * 1000
        return tokenAge > fiftyMinutes
      }
      return false
    }

    const now = Date.now()
    return (this.tokenExpirationTime - now) <= this.refreshBuffer
  }

  // Get token timestamp from localStorage
  private getTokenTimestamp(): number | null {
    if (typeof window !== 'undefined') {
      const timestamp = localStorage.getItem('token_timestamp')
      return timestamp ? parseInt(timestamp) : null
    }
    return null
  }

  // Set token timestamp
  private setTokenTimestamp(): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('token_timestamp', Date.now().toString())
    }
  }

  // Proactive token refresh
  private async proactiveRefreshToken(): Promise<void> {
    if (this.isRefreshing) {
      return new Promise((resolve, reject) => {
        this.failedQueue.push({ resolve, reject })
      })
    }

    this.isRefreshing = true

    try {
      const refreshToken = this.getStoredRefreshToken()
      if (!refreshToken) {
        throw new Error('No refresh token available')
      }

      const response = await this.refreshToken(refreshToken)
      const { access_token, refresh_token, expires_in } = response.data

      // Update stored tokens
      this.setTokens(access_token, refresh_token || refreshToken)

      // Set expiration time if provided
      if (expires_in) {
        this.tokenExpirationTime = Date.now() + (expires_in * 1000)
      }

      this.processQueue(null, access_token)
      console.log('Token refreshed proactively')
    } catch (error) {
      this.processQueue(error, null)
      throw error
    } finally {
      this.isRefreshing = false
    }
  }

  // Generic request methods
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.get(url, config)
    return response.data
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.post(url, data, config)
    return response.data
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.put(url, data, config)
    return response.data
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.patch(url, data, config)
    return response.data
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.delete(url, config)
    return response.data
  }

  // Token management methods
  setTokens(accessToken: string, refreshToken: string, expiresIn?: number) {
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', accessToken)
      localStorage.setItem('refresh_token', refreshToken)
      this.setTokenTimestamp()

      // Set expiration time if provided
      if (expiresIn) {
        this.tokenExpirationTime = Date.now() + (expiresIn * 1000)
        localStorage.setItem('token_expiration', this.tokenExpirationTime.toString())
      }
    }
  }

  clearTokens() {
    this.clearStoredTokens()
    this.tokenExpirationTime = null
  }

  getAccessToken(): string | null {
    return this.getStoredToken()
  }

  getRefreshToken(): string | null {
    return this.getStoredRefreshToken()
  }

  // Public method to manually refresh token
  async refreshAccessToken(): Promise<boolean> {
    try {
      await this.ensureValidToken()
      return true
    } catch (error) {
      console.error('Manual token refresh failed:', error)
      return false
    }
  }

  // Check if token is valid (not expired)
  isTokenValid(): boolean {
    const token = this.getStoredToken()
    if (!token) return false

    // Check expiration
    if (this.tokenExpirationTime) {
      return Date.now() < this.tokenExpirationTime
    }

    // Fallback: check token age
    const tokenTimestamp = this.getTokenTimestamp()
    if (tokenTimestamp) {
      const tokenAge = Date.now() - tokenTimestamp
      const oneHour = 60 * 60 * 1000
      return tokenAge < oneHour
    }

    return true // Assume valid if we can't determine age
  }
}

export const apiClient = new ApiClient()
export default apiClient
