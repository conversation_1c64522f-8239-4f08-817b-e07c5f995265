'use client'
import { useEffect, useCallback } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { authService } from '@/lib/authService'
import { useAuthStore } from '@/store/useAuthStore'
import toast from 'react-hot-toast'

interface UseTokenRefreshOptions {
  refreshOnPageChange?: boolean
  refreshInterval?: number // in milliseconds
  onTokenExpired?: () => void
}

export function useTokenRefresh(options: UseTokenRefreshOptions = {}) {
  const {
    refreshOnPageChange = true,
    refreshInterval = 5 * 60 * 1000, // 5 minutes
    onTokenExpired
  } = options

  const router = useRouter()
  const pathname = usePathname()
  const { user, logout } = useAuthStore()

  // Function to refresh token
  const refreshToken = useCallback(async () => {
    if (!user) return false

    try {
      const success = await authService.refreshAccessToken()
      if (!success) {
        console.warn('Token refresh failed')
        if (onTokenExpired) {
          onTokenExpired()
        } else {
          // Default behavior: logout and redirect to login
          toast.error('Session expired. Please log in again.')
          logout()
          router.push('/auth/login')
        }
        return false
      }
      console.log('Token refreshed successfully')
      return true
    } catch (error) {
      console.error('Token refresh error:', error)
      if (onTokenExpired) {
        onTokenExpired()
      } else {
        toast.error('Session expired. Please log in again.')
        logout()
        router.push('/auth/login')
      }
      return false
    }
  }, [user, onTokenExpired, logout, router])

  // Function to ensure token is valid
  const ensureValidToken = useCallback(async () => {
    if (!user) return false

    try {
      await authService.ensureValidToken()
      return true
    } catch (error) {
      console.error('Token validation error:', error)
      return await refreshToken()
    }
  }, [user, refreshToken])

  // Refresh token on page change
  useEffect(() => {
    if (refreshOnPageChange && user) {
      ensureValidToken()
    }
  }, [pathname, refreshOnPageChange, user, ensureValidToken])

  // Set up periodic token refresh
  useEffect(() => {
    if (!user || !refreshInterval) return

    const interval = setInterval(() => {
      if (authService.isAuthenticated()) {
        refreshToken()
      }
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [user, refreshInterval, refreshToken])

  // Check token validity on mount
  useEffect(() => {
    if (user && !authService.isTokenValid()) {
      refreshToken()
    }
  }, [user, refreshToken])

  return {
    refreshToken,
    ensureValidToken,
    isTokenValid: () => authService.isTokenValid(),
    isAuthenticated: () => authService.isAuthenticated()
  }
}

// Hook for API calls that automatically refreshes token before request
export function useApiWithTokenRefresh() {
  const { ensureValidToken } = useTokenRefresh({ refreshOnPageChange: false })

  const apiCall = useCallback(async <T>(
    apiFunction: () => Promise<T>
  ): Promise<T> => {
    // Ensure token is valid before making the API call
    await ensureValidToken()
    
    // Make the API call
    return apiFunction()
  }, [ensureValidToken])

  return { apiCall, ensureValidToken }
}

// Hook for components that need to refresh token on specific actions
export function useTokenRefreshOnAction() {
  const { ensureValidToken } = useTokenRefresh({ refreshOnPageChange: false })

  const withTokenRefresh = useCallback(async <T>(
    action: () => Promise<T> | T
  ): Promise<T> => {
    // Ensure token is valid before performing the action
    await ensureValidToken()
    
    // Perform the action
    return action()
  }, [ensureValidToken])

  return { withTokenRefresh, ensureValidToken }
}
