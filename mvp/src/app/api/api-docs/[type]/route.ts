import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function GET(
  request: NextRequest,
  { params }: { params: { type: string } }
) {
  const { type } = params

  // Validate type parameter
  if (type !== 'public' && type !== 'private') {
    return NextResponse.json(
      { error: 'Invalid documentation type' },
      { status: 400 }
    )
  }

  try {
    // Read the JSON file
    const filePath = path.join(process.cwd(), 'api_docs', `${type}_auth.json`)
    const fileContents = fs.readFileSync(filePath, 'utf8')
    const data = JSON.parse(fileContents)

    return NextResponse.json(data)
  } catch (error) {
    console.error(`Error reading API documentation file: ${error}`)
    return NextResponse.json(
      { error: 'Failed to load API documentation' },
      { status: 500 }
    )
  }
}
