import ProtectedRoute from '@/components/auth/ProtectedRoute'
import Layout from '@/components/common/Layout'
import IngestionsPage from '@/components/ingestion/IngestionsPage'

export const metadata = {
  title: 'Data Ingestions - AIM DataFlow Platform',
  description: 'Monitor and configure your data ingestion pipelines',
}

export default function Ingestions() {
  return (
    <ProtectedRoute>
      <Layout>
        <div className="animate-fade-in">
          <IngestionsPage />
        </div>
      </Layout>
    </ProtectedRoute>
  )
}
