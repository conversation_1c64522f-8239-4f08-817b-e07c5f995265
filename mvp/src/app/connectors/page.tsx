import ProtectedRoute from '@/components/auth/ProtectedRoute'
import Layout from '@/components/common/Layout'
import ConnectorsPage from '@/components/connectors/ConnectorsPage'

export const metadata = {
  title: 'Connectors - AIM DataFlow Platform',
  description: 'Manage your data sources and destinations',
}

export default function Connectors() {
  return (
    <ProtectedRoute>
      <Layout>
        <div className="animate-fade-in">
          <ConnectorsPage />
        </div>
      </Layout>
    </ProtectedRoute>
  )
}
