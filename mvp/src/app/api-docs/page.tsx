'use client'
import { useState } from 'react'
import { Globe, Lock } from 'lucide-react'
import { cn } from '@/lib/utils'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import ApiDocumentation from '@/components/docs/ApiDocumentation'

export default function ApiDocsPage() {
  const [activeTab, setActiveTab] = useState<'public' | 'private'>('public')

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-white to-neutral-100">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-xl shadow-sm border-b border-neutral-200/50 px-6 py-8">
          <div className="max-w-6xl mx-auto">
            <h1 className="text-4xl font-bold text-gradient-primary mb-2">
              API Documentation
            </h1>
            <p className="text-neutral-600 text-lg">
              Comprehensive documentation for AIM DataFlow Platform APIs
            </p>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white/60 backdrop-blur-sm border-b border-neutral-200/50">
          <div className="max-w-6xl mx-auto px-6">
            <div className="flex space-x-8">
              <button
                onClick={() => setActiveTab('public')}
                className={cn(
                  "flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors",
                  activeTab === 'public'
                    ? "border-primary-500 text-primary-600"
                    : "border-transparent text-neutral-500 hover:text-neutral-700 hover:border-neutral-300"
                )}
              >
                <Globe className="h-4 w-4" />
                <span>Public API</span>
              </button>
              <button
                onClick={() => setActiveTab('private')}
                className={cn(
                  "flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors",
                  activeTab === 'private'
                    ? "border-primary-500 text-primary-600"
                    : "border-transparent text-neutral-500 hover:text-neutral-700 hover:border-neutral-300"
                )}
              >
                <Lock className="h-4 w-4" />
                <span>Private API</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="py-8">
          <ApiDocumentation type={activeTab} />
        </div>
      </div>
    </ProtectedRoute>
  )
}
