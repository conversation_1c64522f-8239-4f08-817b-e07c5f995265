'use client'
import { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON><PERSON>, 
  User, 
  Bell, 
  Shield, 
  Palette, 
  Globe, 
  Key, 
  Database, 
  Brain,
  Save,
  RefreshCw,
  Eye,
  EyeOff,
  Check,
  X,
  AlertTriangle,
  Info,
  Monitor,
  Moon,
  Sun,
  Smartphone,
  Mail,
  Clock,
  Activity
} from 'lucide-react'
import { UserProfile, WorkspaceSettings } from '@/types/api'
import { mockDataService } from '@/lib/mockData'
import { cn } from '@/lib/utils'

type SettingsTab = 'profile' | 'notifications' | 'security' | 'workspace' | 'integrations' | 'audit'

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState<SettingsTab>('profile')
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [workspaceSettings, setWorkspaceSettings] = useState<WorkspaceSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [unsavedChanges, setUnsavedChanges] = useState(false)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      const [profileData, workspaceData] = await Promise.all([
        mockDataService.getUserProfile(),
        mockDataService.getWorkspaceSettings()
      ])
      setUserProfile(profileData)
      setWorkspaceSettings(workspaceData)
    } catch (error) {
      console.error('Error loading settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    if (!userProfile || !workspaceSettings) return
    
    try {
      setSaving(true)
      await Promise.all([
        mockDataService.updateUserProfile(userProfile),
        mockDataService.updateWorkspaceSettings(workspaceSettings)
      ])
      setUnsavedChanges(false)
    } catch (error) {
      console.error('Error saving settings:', error)
    } finally {
      setSaving(false)
    }
  }

  const updateUserProfile = (updates: Partial<UserProfile>) => {
    if (!userProfile) return
    setUserProfile({ ...userProfile, ...updates })
    setUnsavedChanges(true)
  }

  const updateUserPreferences = (updates: any) => {
    if (!userProfile) return
    setUserProfile({
      ...userProfile,
      preferences: { ...userProfile.preferences, ...updates }
    })
    setUnsavedChanges(true)
  }

  const updateWorkspaceSettings = (updates: Partial<WorkspaceSettings>) => {
    if (!workspaceSettings) return
    setWorkspaceSettings({ ...workspaceSettings, ...updates })
    setUnsavedChanges(true)
  }

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'workspace', label: 'Workspace', icon: Settings },
    { id: 'integrations', label: 'Integrations', icon: Brain },
    { id: 'audit', label: 'Audit & Logs', icon: Database }
  ]

  if (loading || !userProfile || !workspaceSettings) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-neutral-200 rounded mb-6"></div>
          <div className="flex space-x-4">
            <div className="w-64 h-96 bg-neutral-200 rounded"></div>
            <div className="flex-1 h-96 bg-neutral-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6 relative">
      {/* Modern background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-info-50/30 via-transparent to-primary-50/20 pointer-events-none" />
      
      {/* Header */}
      <div className="flex items-center justify-between relative z-10">
        <div>
          <h1 className="text-3xl font-bold text-gradient-primary">Settings</h1>
          <p className="text-neutral-600 mt-2">Manage your account, workspace, and platform preferences</p>
        </div>
        <div className="flex items-center space-x-3">
          {unsavedChanges && (
            <div className="flex items-center space-x-2 text-warning-600 text-sm">
              <AlertTriangle className="w-4 h-4" />
              <span>Unsaved changes</span>
            </div>
          )}
          <button 
            onClick={handleSave}
            disabled={saving || !unsavedChanges}
            className={cn(
              "btn-gradient flex items-center space-x-2 hover-lift",
              (!unsavedChanges) && "opacity-50 cursor-not-allowed"
            )}
          >
            {saving ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            <span>Save Changes</span>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 relative z-10">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="card-gradient p-4 sticky top-6">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as SettingsTab)}
                    className={cn(
                      "w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200",
                      activeTab === tab.id
                        ? "bg-primary-500 text-white shadow-lg"
                        : "text-neutral-600 hover:text-neutral-900 hover:bg-neutral-100"
                    )}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="card-gradient p-6">
            {/* Profile Tab */}
            {activeTab === 'profile' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-2 mb-6">
                  <User className="w-5 h-5 text-primary-600" />
                  <h2 className="text-xl font-semibold text-neutral-900">Profile Settings</h2>
                </div>

                {/* Profile Photo */}
                <div className="flex items-center space-x-6">
                  <div className="relative">
                    <img 
                      src={userProfile.avatar_url || '/placeholder-avatar.png'} 
                      alt="Profile" 
                      className="w-20 h-20 rounded-full object-cover"
                    />
                    <button className="absolute -bottom-1 -right-1 w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center text-white hover:bg-primary-600 transition-colors">
                      <RefreshCw className="w-3 h-3" />
                    </button>
                  </div>
                  <div>
                    <h3 className="font-medium text-neutral-900">{userProfile.full_name}</h3>
                    <p className="text-sm text-neutral-600">{userProfile.email}</p>
                    <p className="text-xs text-neutral-500 mt-1">
                      Role: <span className="capitalize">{userProfile.role.replace('_', ' ')}</span>
                    </p>
                  </div>
                </div>

                {/* Form Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={userProfile.full_name}
                      onChange={(e) => updateUserProfile({ full_name: e.target.value })}
                      className="input w-full"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Username
                    </label>
                    <input
                      type="text"
                      value={userProfile.username}
                      onChange={(e) => updateUserProfile({ username: e.target.value })}
                      className="input w-full"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      value={userProfile.email}
                      onChange={(e) => updateUserProfile({ email: e.target.value })}
                      className="input w-full"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Timezone
                    </label>
                    <select
                      value={userProfile.preferences.timezone}
                      onChange={(e) => updateUserPreferences({ timezone: e.target.value })}
                      className="input w-full"
                    >
                      <option value="America/New_York">Eastern Time</option>
                      <option value="America/Chicago">Central Time</option>
                      <option value="America/Denver">Mountain Time</option>
                      <option value="America/Los_Angeles">Pacific Time</option>
                      <option value="UTC">UTC</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Language
                    </label>
                    <select
                      value={userProfile.preferences.language}
                      onChange={(e) => updateUserPreferences({ language: e.target.value })}
                      className="input w-full"
                    >
                      <option value="en-US">English (US)</option>
                      <option value="en-GB">English (UK)</option>
                      <option value="es-ES">Spanish</option>
                      <option value="fr-FR">French</option>
                      <option value="de-DE">German</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Date Format
                    </label>
                    <select
                      value={userProfile.preferences.date_format}
                      onChange={(e) => updateUserPreferences({ date_format: e.target.value })}
                      className="input w-full"
                    >
                      <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                      <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                      <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                    </select>
                  </div>
                </div>

                {/* Theme Settings */}
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-3">
                    Theme Preference
                  </label>
                  <div className="grid grid-cols-3 gap-3">
                    {['light', 'dark', 'auto'].map((theme) => (
                      <button
                        key={theme}
                        onClick={() => updateUserPreferences({ theme })}
                        className={cn(
                          "p-4 rounded-lg border-2 transition-all duration-200 flex flex-col items-center space-y-2",
                          userProfile.preferences.theme === theme
                            ? "border-primary-500 bg-primary-50"
                            : "border-neutral-200 hover:border-neutral-300"
                        )}
                      >
                        {theme === 'light' && <Sun className="w-6 h-6 text-warning-500" />}
                        {theme === 'dark' && <Moon className="w-6 h-6 text-info-500" />}
                        {theme === 'auto' && <Monitor className="w-6 h-6 text-neutral-500" />}
                        <span className="text-sm font-medium capitalize">{theme}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Notifications Tab */}
            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-2 mb-6">
                  <Bell className="w-5 h-5 text-primary-600" />
                  <h2 className="text-xl font-semibold text-neutral-900">Notification Settings</h2>
                </div>

                {/* Notification Channels */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-neutral-900">Notification Channels</h3>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-neutral-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Mail className="w-5 h-5 text-primary-600" />
                        <div>
                          <p className="font-medium text-neutral-900">Email Notifications</p>
                          <p className="text-sm text-neutral-600">Receive notifications via email</p>
                        </div>
                      </div>
                      <label className="switch">
                        <input
                          type="checkbox"
                          checked={userProfile.preferences.notifications.email_enabled}
                          onChange={(e) => updateUserPreferences({
                            notifications: { 
                              ...userProfile.preferences.notifications,
                              email_enabled: e.target.checked 
                            }
                          })}
                        />
                        <span className="slider"></span>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-neutral-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Smartphone className="w-5 h-5 text-primary-600" />
                        <div>
                          <p className="font-medium text-neutral-900">Push Notifications</p>
                          <p className="text-sm text-neutral-600">Receive push notifications in browser</p>
                        </div>
                      </div>
                      <label className="switch">
                        <input
                          type="checkbox"
                          checked={userProfile.preferences.notifications.push_enabled}
                          onChange={(e) => updateUserPreferences({
                            notifications: { 
                              ...userProfile.preferences.notifications,
                              push_enabled: e.target.checked 
                            }
                          })}
                        />
                        <span className="slider"></span>
                      </label>
                    </div>
                  </div>
                </div>

                {/* Notification Types */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-neutral-900">Notification Types</h3>
                  
                  <div className="space-y-3">
                    {[
                      { key: 'job_completion', label: 'Job Completion', desc: 'When jobs finish successfully' },
                      { key: 'job_failure', label: 'Job Failure', desc: 'When jobs fail or encounter errors' },
                      { key: 'system_updates', label: 'System Updates', desc: 'Platform updates and maintenance' },
                      { key: 'security_alerts', label: 'Security Alerts', desc: 'Security-related notifications' },
                      { key: 'weekly_reports', label: 'Weekly Reports', desc: 'Weekly summary reports' }
                    ].map((notification) => (
                      <div key={notification.key} className="flex items-center justify-between p-3 rounded-lg hover:bg-neutral-50 transition-colors">
                        <div>
                          <p className="font-medium text-neutral-900">{notification.label}</p>
                          <p className="text-sm text-neutral-600">{notification.desc}</p>
                        </div>
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={userProfile.preferences.notifications[notification.key as keyof typeof userProfile.preferences.notifications]}
                            onChange={(e) => updateUserPreferences({
                              notifications: { 
                                ...userProfile.preferences.notifications,
                                [notification.key]: e.target.checked 
                              }
                            })}
                          />
                          <span className="slider"></span>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Security Tab */}
            {activeTab === 'security' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-2 mb-6">
                  <Shield className="w-5 h-5 text-primary-600" />
                  <h2 className="text-xl font-semibold text-neutral-900">Security Settings</h2>
                </div>

                {/* Password Policy */}
                <div className="bg-info-50 border border-info-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <Info className="w-5 h-5 text-info-600 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-info-900">Password Policy</h3>
                      <ul className="text-sm text-info-800 mt-2 space-y-1">
                        <li>• Minimum {workspaceSettings.security_policy.password_policy.min_length} characters</li>
                        <li>• Must include uppercase and lowercase letters</li>
                        <li>• Must include numbers and symbols</li>
                        <li>• Expires every {workspaceSettings.security_policy.password_policy.expiry_days} days</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Security Features */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-neutral-900">Security Features</h3>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-neutral-50 rounded-lg">
                      <div>
                        <p className="font-medium text-neutral-900">Multi-Factor Authentication</p>
                        <p className="text-sm text-neutral-600">Add an extra layer of security to your account</p>
                      </div>
                      <span className={cn(
                        "badge",
                        workspaceSettings.security_policy.mfa_required ? "badge-success" : "badge-warning"
                      )}>
                        {workspaceSettings.security_policy.mfa_required ? "Required" : "Optional"}
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-neutral-50 rounded-lg">
                      <div>
                        <p className="font-medium text-neutral-900">Session Timeout</p>
                        <p className="text-sm text-neutral-600">
                          Automatically log out after {workspaceSettings.security_policy.session_timeout} minutes of inactivity
                        </p>
                      </div>
                      <Clock className="w-5 h-5 text-neutral-400" />
                    </div>

                    <div className="flex items-center justify-between p-4 bg-neutral-50 rounded-lg">
                      <div>
                        <p className="font-medium text-neutral-900">Data Encryption</p>
                        <p className="text-sm text-neutral-600">Data is encrypted at rest and in transit</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Check className="w-4 h-4 text-success-600" />
                        <span className="text-sm text-success-600">Enabled</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* IP Whitelist */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-neutral-900">IP Whitelist</h3>
                  <div className="bg-neutral-50 rounded-lg p-4">
                    <p className="text-sm text-neutral-600 mb-3">Allowed IP ranges:</p>
                    <div className="space-y-2">
                      {workspaceSettings.security_policy.ip_whitelist.map((ip, index) => (
                        <div key={index} className="flex items-center justify-between bg-white rounded border px-3 py-2">
                          <span className="font-mono text-sm">{ip}</span>
                          <button className="text-error-600 hover:text-error-700">
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Add other tabs content here... */}
            {activeTab === 'workspace' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-2 mb-6">
                  <Settings className="w-5 h-5 text-primary-600" />
                  <h2 className="text-xl font-semibold text-neutral-900">Workspace Settings</h2>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Workspace Name
                    </label>
                    <input
                      type="text"
                      value={workspaceSettings.name}
                      onChange={(e) => updateWorkspaceSettings({ name: e.target.value })}
                      className="input w-full"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Data Retention (Days)
                    </label>
                    <input
                      type="number"
                      value={workspaceSettings.default_retention_days}
                      onChange={(e) => updateWorkspaceSettings({ default_retention_days: parseInt(e.target.value) })}
                      className="input w-full"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={workspaceSettings.description}
                    onChange={(e) => updateWorkspaceSettings({ description: e.target.value })}
                    className="input w-full h-24 resize-none"
                  />
                </div>
              </div>
            )}

            {/* Integrations Tab */}
            {activeTab === 'integrations' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-2 mb-6">
                  <Brain className="w-5 h-5 text-primary-600" />
                  <h2 className="text-xl font-semibold text-neutral-900">Integration Settings</h2>
                </div>

                {/* AI Models Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-neutral-900">AI Models</h3>
                  <div className="space-y-4">
                    {workspaceSettings.integration_settings.ai_models.map((model, index) => (
                      <div key={index} className="bg-neutral-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <h4 className="font-medium text-neutral-900">{model.provider}</h4>
                            <p className="text-sm text-neutral-600">{model.model_name}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="badge badge-success">Active</span>
                            <button className="text-neutral-400 hover:text-neutral-600">
                              <Settings className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-neutral-600">Endpoint:</span>
                            <span className="ml-2 font-mono text-xs">{model.endpoint}</span>
                          </div>
                          <div>
                            <span className="text-neutral-600">API Key:</span>
                            <span className="ml-2 font-mono text-xs">{model.api_key}</span>
                          </div>
                        </div>
                        
                        <div className="mt-3 pt-3 border-t border-neutral-200">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-neutral-600">Parameters:</span>
                            <span className="text-neutral-500">
                              Temperature: {model.parameters.temperature}, Max Tokens: {model.parameters.max_tokens}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                    
                    <button className="w-full border-2 border-dashed border-neutral-300 rounded-lg p-4 text-neutral-600 hover:border-neutral-400 hover:text-neutral-700 transition-colors">
                      <div className="flex items-center justify-center space-x-2">
                        <Brain className="w-5 h-5" />
                        <span>Add AI Model</span>
                      </div>
                    </button>
                  </div>
                </div>

                {/* External APIs Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-neutral-900">External APIs</h3>
                  <div className="space-y-4">
                    {workspaceSettings.integration_settings.external_apis.map((api, index) => (
                      <div key={index} className="bg-neutral-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <h4 className="font-medium text-neutral-900">{api.name}</h4>
                            <p className="text-sm text-neutral-600">{api.base_url}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={cn(
                              "badge",
                              api.auth_type === 'oauth' ? "badge-info" : 
                              api.auth_type === 'api_key' ? "badge-success" : "badge-warning"
                            )}>
                              {api.auth_type.toUpperCase()}
                            </span>
                            <button className="text-neutral-400 hover:text-neutral-600">
                              <Settings className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-neutral-600">Authentication:</span>
                          <span className="text-neutral-500">
                            {api.auth_type === 'oauth' ? 'OAuth 2.0' : 
                             api.auth_type === 'api_key' ? 'API Key' : 'Basic Auth'}
                          </span>
                        </div>
                      </div>
                    ))}
                    
                    <button className="w-full border-2 border-dashed border-neutral-300 rounded-lg p-4 text-neutral-600 hover:border-neutral-400 hover:text-neutral-700 transition-colors">
                      <div className="flex items-center justify-center space-x-2">
                        <Globe className="w-5 h-5" />
                        <span>Add External API</span>
                      </div>
                    </button>
                  </div>
                </div>

                {/* Data Sources Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-neutral-900">Data Sources</h3>
                  <div className="space-y-4">
                    {workspaceSettings.integration_settings.data_sources.map((source, index) => (
                      <div key={index} className="bg-neutral-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <h4 className="font-medium text-neutral-900">{source.name}</h4>
                            <p className="text-sm text-neutral-600 capitalize">{source.type}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={cn(
                              "badge",
                              source.ssl_enabled ? "badge-success" : "badge-warning"
                            )}>
                              {source.ssl_enabled ? 'SSL Enabled' : 'SSL Disabled'}
                            </span>
                            <button className="text-neutral-400 hover:text-neutral-600">
                              <Settings className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-neutral-600">Connection:</span>
                            <span className="ml-2 font-mono text-xs">{source.connection_string.substring(0, 30)}...</span>
                          </div>
                          <div>
                            <span className="text-neutral-600">Timeout:</span>
                            <span className="ml-2">{source.timeout}s</span>
                          </div>
                        </div>
                      </div>
                    ))}
                    
                    <button className="w-full border-2 border-dashed border-neutral-300 rounded-lg p-4 text-neutral-600 hover:border-neutral-400 hover:text-neutral-700 transition-colors">
                      <div className="flex items-center justify-center space-x-2">
                        <Database className="w-5 h-5" />
                        <span>Add Data Source</span>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Audit & Logs Tab */}
            {activeTab === 'audit' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-2 mb-6">
                  <Database className="w-5 h-5 text-primary-600" />
                  <h2 className="text-xl font-semibold text-neutral-900">Audit & Logs</h2>
                </div>

                {/* Audit Configuration */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-neutral-900">Audit Configuration</h3>
                  
                  <div className="bg-neutral-50 rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          Audit Enabled
                        </label>
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={workspaceSettings.audit_settings.enabled}
                            onChange={(e) => updateWorkspaceSettings({
                              audit_settings: {
                                ...workspaceSettings.audit_settings,
                                enabled: e.target.checked
                              }
                            })}
                          />
                          <span className="slider"></span>
                        </label>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          Retention (Days)
                        </label>
                        <input
                          type="number"
                          value={workspaceSettings.audit_settings.retention_days}
                          onChange={(e) => updateWorkspaceSettings({
                            audit_settings: {
                              ...workspaceSettings.audit_settings,
                              retention_days: parseInt(e.target.value)
                            }
                          })}
                          className="input w-full"
                          min="1"
                          max="3650"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          Log Level
                        </label>
                        <select
                          value={workspaceSettings.audit_settings.log_level}
                          onChange={(e) => updateWorkspaceSettings({
                            audit_settings: {
                              ...workspaceSettings.audit_settings,
                              log_level: e.target.value as 'basic' | 'detailed' | 'verbose'
                            }
                          })}
                          className="input w-full"
                        >
                          <option value="basic">Basic</option>
                          <option value="detailed">Detailed</option>
                          <option value="verbose">Verbose</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Audit Events */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-neutral-900">Audit Events</h3>
                  
                  <div className="space-y-3">
                    {workspaceSettings.audit_settings.events.map((event, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-neutral-50 rounded-lg">
                        <div>
                          <h4 className="font-medium text-neutral-900 capitalize">
                            {event.type.replace('_', ' ')}
                          </h4>
                          <p className="text-sm text-neutral-600">{event.description}</p>
                        </div>
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={event.enabled}
                            onChange={(e) => {
                              const updatedEvents = [...workspaceSettings.audit_settings.events]
                              updatedEvents[index] = { ...event, enabled: e.target.checked }
                              updateWorkspaceSettings({
                                audit_settings: {
                                  ...workspaceSettings.audit_settings,
                                  events: updatedEvents
                                }
                              })
                            }}
                          />
                          <span className="slider"></span>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Recent Audit Logs */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-neutral-900">Recent Audit Logs</h3>
                    <button className="btn-secondary text-sm">
                      <Database className="w-4 h-4 mr-2" />
                      Export Logs
                    </button>
                  </div>
                  
                  <div className="bg-white border rounded-lg overflow-hidden">
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead className="bg-neutral-50 border-b">
                          <tr>
                            <th className="text-left p-3 font-medium text-neutral-700">Timestamp</th>
                            <th className="text-left p-3 font-medium text-neutral-700">Event Type</th>
                            <th className="text-left p-3 font-medium text-neutral-700">User</th>
                            <th className="text-left p-3 font-medium text-neutral-700">Description</th>
                            <th className="text-left p-3 font-medium text-neutral-700">IP Address</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-neutral-200">
                          {/* Mock audit log entries */}
                          {[
                            {
                              timestamp: '2024-03-18T14:30:25Z',
                              type: 'user_login',
                              user: '<EMAIL>',
                              description: 'User logged in successfully',
                              ip: '*************'
                            },
                            {
                              timestamp: '2024-03-18T14:25:12Z',
                              type: 'data_access',
                              user: '<EMAIL>',
                              description: 'Accessed customer database',
                              ip: '*************'
                            },
                            {
                              timestamp: '2024-03-18T14:20:45Z',
                              type: 'system_config',
                              user: '<EMAIL>',
                              description: 'Updated workspace settings',
                              ip: '************'
                            },
                            {
                              timestamp: '2024-03-18T14:15:33Z',
                              type: 'job_execution',
                              user: 'system',
                              description: 'Started data migration job',
                              ip: 'localhost'
                            },
                            {
                              timestamp: '2024-03-18T14:10:21Z',
                              type: 'data_access',
                              user: '<EMAIL>',
                              description: 'Downloaded data model mapping',
                              ip: '*************'
                            }
                          ].map((log, index) => (
                            <tr key={index} className="hover:bg-neutral-50">
                              <td className="p-3 font-mono text-xs">
                                {new Date(log.timestamp).toLocaleString()}
                              </td>
                              <td className="p-3">
                                <span className={cn(
                                  "badge",
                                  log.type === 'user_login' ? "badge-info" :
                                  log.type === 'data_access' ? "badge-warning" :
                                  log.type === 'system_config' ? "badge-primary" :
                                  "badge-success"
                                )}>
                                  {log.type.replace('_', ' ')}
                                </span>
                              </td>
                              <td className="p-3 text-neutral-900">{log.user}</td>
                              <td className="p-3 text-neutral-600">{log.description}</td>
                              <td className="p-3 font-mono text-xs text-neutral-500">{log.ip}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-neutral-600">
                    <span>Showing 5 of 1,247 entries</span>
                    <div className="flex items-center space-x-2">
                      <button className="px-3 py-1 border rounded hover:bg-neutral-50">Previous</button>
                      <span className="px-3 py-1">1 of 250</span>
                      <button className="px-3 py-1 border rounded hover:bg-neutral-50">Next</button>
                    </div>
                  </div>
                </div>

                {/* Audit Statistics */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-neutral-900">Audit Statistics</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="bg-neutral-50 rounded-lg p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-info-100 rounded-lg flex items-center justify-center">
                          <User className="w-5 h-5 text-info-600" />
                        </div>
                        <div>
                          <p className="text-sm text-neutral-600">Login Events</p>
                          <p className="text-xl font-semibold text-neutral-900">247</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-neutral-50 rounded-lg p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-warning-100 rounded-lg flex items-center justify-center">
                          <Database className="w-5 h-5 text-warning-600" />
                        </div>
                        <div>
                          <p className="text-sm text-neutral-600">Data Access</p>
                          <p className="text-xl font-semibold text-neutral-900">1,205</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-neutral-50 rounded-lg p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                          <Settings className="w-5 h-5 text-primary-600" />
                        </div>
                        <div>
                          <p className="text-sm text-neutral-600">Config Changes</p>
                          <p className="text-xl font-semibold text-neutral-900">32</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-neutral-50 rounded-lg p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-success-100 rounded-lg flex items-center justify-center">
                          <Activity className="w-5 h-5 text-success-600" />
                        </div>
                        <div>
                          <p className="text-sm text-neutral-600">Job Executions</p>
                          <p className="text-xl font-semibold text-neutral-900">89</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
