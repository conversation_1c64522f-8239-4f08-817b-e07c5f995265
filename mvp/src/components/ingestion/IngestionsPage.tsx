'use client'
import { useState, useEffect } from 'react'
import { Plus, Search, Calendar, Clock, Activity, Settings, ChevronLeft, ChevronRight } from 'lucide-react'
import { formatDate, cn } from '@/lib/utils'
import { useAuthStore } from '@/store/useAuthStore'
import { airbyteService, type AirbyteConnection } from '@/lib/airbyteService'
import { useAsyncWithTokenRefresh } from '@/components/hoc/withTokenRefresh'
import toast from 'react-hot-toast'

export default function IngestionsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [connections, setConnections] = useState<AirbyteConnection[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalConnections, setTotalConnections] = useState(0)
  const [hasNextPage, setHasNextPage] = useState(false)
  const [hasPreviousPage, setHasPreviousPage] = useState(false)

  const { user } = useAuthStore()
  const { withTokenRefresh } = useAsyncWithTokenRefresh()

  const limit = 10
  const offset = (currentPage - 1) * limit

  // Load connections from API
  const loadConnections = async () => {
    if (!user?.id) return

    setLoading(true)
    try {
      await withTokenRefresh(async () => {
        const response = await airbyteService.getConnections(user.id, limit, offset)
        setConnections(response.data || [])
        setTotalConnections(response.data?.length || 0)
        setHasNextPage(!!response.next)
        setHasPreviousPage(!!response.previous)
      })
    } catch (error) {
      console.error('Error loading connections:', error)
      toast.error('Failed to load connections')
      setConnections([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadConnections()
  }, [user?.id, currentPage])

  // Filter connections based on search query
  const filteredConnections = connections.filter((connection) => {
    return connection.name.toLowerCase().includes(searchQuery.toLowerCase())
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Activity className="w-4 h-4 text-success-500" />
      case 'inactive':
        return <Clock className="w-4 h-4 text-neutral-500" />
      case 'deprecated':
        return <Clock className="w-4 h-4 text-warning-500" />
      default:
        return <Clock className="w-4 h-4 text-neutral-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-success-100 text-success-700 border-success-200'
      case 'inactive':
        return 'bg-neutral-100 text-neutral-700 border-neutral-200'
      case 'deprecated':
        return 'bg-warning-100 text-warning-700 border-warning-200'
      default:
        return 'bg-neutral-100 text-neutral-700 border-neutral-200'
    }
  }

  const getScheduleText = (connection: AirbyteConnection) => {
    if (!connection.schedule) return 'Manual'

    if (connection.schedule.scheduleType === 'manual') return 'Manual'
    if (connection.schedule.scheduleType === 'cron') return `Cron: ${connection.schedule.cronExpression}`
    if (connection.schedule.scheduleType === 'basic' && connection.schedule.basicSchedule) {
      const { timeUnit, units } = connection.schedule.basicSchedule
      return `Every ${units} ${timeUnit}`
    }

    return 'Scheduled'
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          {/* <h1 className="text-2xl font-bold text-neutral-900">Data Ingestions</h1>
          <p className="text-neutral-600 mt-1">Monitor and manage your data pipeline executions</p> */}
        </div>
        <button className="btn-primary flex items-center space-x-2">
          <Plus className="w-4 h-4" />
          <span>Create Pipeline</span>
        </button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search connections..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="input pl-10 w-full"
          />
        </div>

        {/* Pagination Info */}
        <div className="flex items-center space-x-4">
          <span className="text-sm text-neutral-600">
            Page {currentPage} • {totalConnections} total connections
          </span>

          {/* Pagination Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={!hasPreviousPage || loading}
              className="p-2 rounded-lg border border-neutral-200 hover:bg-neutral-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
            <button
              onClick={() => setCurrentPage(prev => prev + 1)}
              disabled={!hasNextPage || loading}
              className="p-2 rounded-lg border border-neutral-200 hover:bg-neutral-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="card p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-neutral-100 rounded-lg flex items-center justify-center">
              <Activity className="w-5 h-5 text-neutral-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-neutral-900">{loading ? '...' : totalConnections}</p>
              <p className="text-sm text-neutral-600">Total Connections</p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-success-100 rounded-lg flex items-center justify-center">
              <Activity className="w-5 h-5 text-success-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-neutral-900">
                {loading ? '...' : connections.filter(c => c.status === 'active').length}
              </p>
              <p className="text-sm text-neutral-600">Active</p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-neutral-100 rounded-lg flex items-center justify-center">
              <Clock className="w-5 h-5 text-neutral-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-neutral-900">
                {loading ? '...' : connections.filter(c => c.status === 'inactive').length}
              </p>
              <p className="text-sm text-neutral-600">Inactive</p>
            </div>
          </div>
        </div>
      </div>

      {/* Connections List */}
      {loading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="card p-6 animate-pulse">
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-4 h-4 bg-neutral-200 rounded"></div>
                <div className="flex-1">
                  <div className="h-4 bg-neutral-200 rounded w-1/3 mb-2"></div>
                  <div className="h-3 bg-neutral-200 rounded w-1/2"></div>
                </div>
                <div className="h-6 bg-neutral-200 rounded w-16"></div>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="h-3 bg-neutral-200 rounded"></div>
                <div className="h-3 bg-neutral-200 rounded"></div>
                <div className="h-3 bg-neutral-200 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredConnections.map((connection, index) => (
            <div
              key={connection.connectionId}
              className="card p-6 hover:shadow-lg transition-all duration-200"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  {getStatusIcon(connection.status)}
                  <div>
                    <h3 className="font-semibold text-neutral-900">{connection.name}</h3>
                    <p className="text-sm text-neutral-600">
                      Connection ID: {connection.connectionId}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <span className={cn(
                    'text-xs px-2 py-1 rounded-full border font-medium capitalize',
                    getStatusColor(connection.status)
                  )}>
                    {connection.status}
                  </span>
                  <button className="text-neutral-400 hover:text-neutral-600">
                    <Settings className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Connection Details */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="text-neutral-600">Source ID</p>
                  <p className="font-medium text-neutral-900 font-mono text-xs">{connection.sourceId}</p>
                </div>
                <div>
                  <p className="text-neutral-600">Destination ID</p>
                  <p className="font-medium text-neutral-900 font-mono text-xs">{connection.destinationId}</p>
                </div>
                <div>
                  <p className="text-neutral-600">Schedule</p>
                  <p className="font-medium text-neutral-900">{getScheduleText(connection)}</p>
                </div>
              </div>

              {/* Timestamps */}
              <div className="mt-4 pt-4 border-t border-neutral-100">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-neutral-400" />
                    <span className="text-neutral-600">Created:</span>
                    <span className="text-neutral-900">{formatDate(connection.createdAt, 'relative')}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-neutral-400" />
                    <span className="text-neutral-600">Updated:</span>
                    <span className="text-neutral-900">{formatDate(connection.updatedAt, 'relative')}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {!loading && filteredConnections.length === 0 && (
        <div className="text-center py-12">
          <Activity className="w-12 h-12 text-neutral-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-neutral-900 mb-2">
            {connections.length === 0 ? 'No connections found' : 'No connections match your search'}
          </h3>
          <p className="text-neutral-600 mb-6">
            {connections.length === 0
              ? 'Create your first connection to start syncing data'
              : 'Try adjusting your search criteria'
            }
          </p>
          <button className="btn-primary">Create Connection</button>
        </div>
      )}
    </div>
  )
}
