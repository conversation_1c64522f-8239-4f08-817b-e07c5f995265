'use client'
import { useState, useEffect } from 'react'
import { X, Search, Loader2, ExternalLink, Database, Cloud, Globe, Zap } from 'lucide-react'
import { useAuthStore } from '@/store/useAuthStore'
import { airbyteService, type AirbyteSourceDefinition, type AirbyteDestinationDefinition } from '@/lib/airbyteService'
import { useAsyncWithTokenRefresh } from '@/components/hoc/withTokenRefresh'
import { cn } from '@/lib/utils'
import toast from 'react-hot-toast'
import ConnectorConfigForm from './ConnectorConfigForm'

interface AddConnectorModalProps {
  isOpen: boolean
  onClose: () => void
  type: 'source' | 'destination'
  onSuccess?: () => void
}

export default function AddConnectorModal({ isOpen, onClose, type, onSuccess }: AddConnectorModalProps) {
  const { user } = useAuthStore()
  const { withTokenRefresh } = useAsyncWithTokenRefresh()
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(false)
  const [sourceDefinitions, setSourceDefinitions] = useState<AirbyteSourceDefinition[]>([])
  const [destinationDefinitions, setDestinationDefinitions] = useState<AirbyteDestinationDefinition[]>([])
  const [selectedDefinition, setSelectedDefinition] = useState<AirbyteSourceDefinition | AirbyteDestinationDefinition | null>(null)
  const [showConfigForm, setShowConfigForm] = useState(false)

  // Load definitions when modal opens and reset state when closed
  useEffect(() => {
    if (isOpen && user?.id) {
      loadDefinitions()
    } else if (!isOpen) {
      // Reset state when modal is closed
      setShowConfigForm(false)
      setSelectedDefinition(null)
      setSearchQuery('')
    }
  }, [isOpen, user?.id, type])

  const loadDefinitions = async () => {
    if (!user?.id) {
      toast.error('User not authenticated')
      return
    }

    setLoading(true)
    try {
      await withTokenRefresh(async () => {
        if (type === 'source') {
          const definitions = await airbyteService.getSourceDefinitions(user.id)
          setSourceDefinitions(definitions)
        } else {
          const definitions = await airbyteService.getDestinationDefinitions(user.id)
          setDestinationDefinitions(definitions)
        }
      })
    } catch (error) {
      toast.error(`Failed to load ${type} definitions`)
      console.error('Error loading definitions:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filter definitions based on search query
  const filteredDefinitions = (type === 'source' ? sourceDefinitions : destinationDefinitions)
    .filter(def => 
      def.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      def.dockerRepository.toLowerCase().includes(searchQuery.toLowerCase())
    )

  // Get icon for connector
  const getConnectorIcon = (definition: AirbyteSourceDefinition | AirbyteDestinationDefinition) => {
    const name = definition.name.toLowerCase()
    
    if (name.includes('postgres') || name.includes('mysql') || name.includes('sql')) {
      return <Database className="w-6 h-6" />
    } else if (name.includes('s3') || name.includes('gcs') || name.includes('azure')) {
      return <Cloud className="w-6 h-6" />
    } else if (name.includes('api') || name.includes('rest') || name.includes('http')) {
      return <Globe className="w-6 h-6" />
    } else {
      return <Zap className="w-6 h-6" />
    }
  }

  // Get color scheme for connector type
  const getConnectorColor = (definition: AirbyteSourceDefinition | AirbyteDestinationDefinition) => {
    const name = definition.name.toLowerCase()

    if (name.includes('postgres') || name.includes('mysql')) {
      return 'from-blue-500 to-blue-600'
    } else if (name.includes('s3') || name.includes('aws')) {
      return 'from-orange-500 to-orange-600'
    } else if (name.includes('google') || name.includes('gcs')) {
      return 'from-green-500 to-green-600'
    } else if (name.includes('azure')) {
      return 'from-blue-600 to-blue-700'
    } else if (name.includes('snowflake')) {
      return 'from-cyan-500 to-cyan-600'
    } else {
      return 'from-purple-500 to-purple-600'
    }
  }

  // Get unique identifier for definition
  const getDefinitionId = (definition: AirbyteSourceDefinition | AirbyteDestinationDefinition) => {
    const id = definition.id

    // Ensure we have a valid ID
    if (!id) {
      console.warn('Definition missing ID:', definition)
      return `fallback-${definition.name}-${Math.random()}`
    }

    return id
  }

  const handleConnectorSelect = (definition: AirbyteSourceDefinition | AirbyteDestinationDefinition) => {
    setSelectedDefinition(definition)
    setShowConfigForm(true)
  }

  const handleBackToList = () => {
    setShowConfigForm(false)
    setSelectedDefinition(null)
  }

  const handleConfigSuccess = () => {
    setShowConfigForm(false)
    setSelectedDefinition(null)
    onSuccess?.()
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-200">
          <div>
            <h2 className="text-2xl font-bold text-neutral-900">
              Add {type === 'source' ? 'Data Source' : 'Destination'}
            </h2>
            <p className="text-neutral-600 mt-1">
              Choose from {filteredDefinitions.length} available {type === 'source' ? 'sources' : 'destinations'}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-neutral-100 rounded-xl transition-colors"
          >
            <X className="w-5 h-5 text-neutral-500" />
          </button>
        </div>

        {/* Search */}
        <div className="p-6 border-b border-neutral-200">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
            <input
              type="text"
              placeholder={`Search ${type === 'source' ? 'sources' : 'destinations'}...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="animate-spin h-8 w-8 text-primary-600 mx-auto mb-4" />
                <p className="text-neutral-600">Loading {type === 'source' ? 'sources' : 'destinations'}...</p>
              </div>
            </div>
          ) : filteredDefinitions.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-neutral-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Database className="w-8 h-8 text-neutral-400" />
              </div>
              <h3 className="text-lg font-semibold text-neutral-900 mb-2">
                No {type === 'source' ? 'sources' : 'destinations'} found
              </h3>
              <p className="text-neutral-600">
                {searchQuery ? 'Try adjusting your search terms' : `No ${type === 'source' ? 'sources' : 'destinations'} available`}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredDefinitions.map((definition) => (
                <button
                  key={getDefinitionId(definition)}
                  onClick={() => handleConnectorSelect(definition)}
                  className="group p-4 border border-neutral-200 rounded-xl hover:border-primary-300 hover:shadow-md transition-all duration-200 text-left"
                >
                  <div className="flex items-start space-x-3">
                    <div className={cn(
                      "w-12 h-12 bg-gradient-to-br rounded-xl flex items-center justify-center text-white flex-shrink-0",
                      getConnectorColor(definition)
                    )}>
                      {getConnectorIcon(definition)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-neutral-900 group-hover:text-primary-700 transition-colors truncate">
                        {definition.name}
                      </h3>
                      <p className="text-sm text-neutral-600 mt-1 line-clamp-2">
                        {type === 'source' 
                          ? definition.dockerRepository.split('/').pop()?.split('source-').pop()
                          : definition.dockerRepository.split('/').pop()?.split('destination-').pop()
                        }
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        {definition.documentationUrl && (
                          <ExternalLink className="w-3 h-3 text-neutral-400" />
                        )}
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Configuration Form */}
      {showConfigForm && selectedDefinition && (
        <ConnectorConfigForm
          isOpen={showConfigForm}
          onClose={onClose}
          onBack={handleBackToList}
          definition={selectedDefinition}
          type={type}
          onSuccess={handleConfigSuccess}
        />
      )}
    </div>
  )
}
