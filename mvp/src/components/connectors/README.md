# Connector Creation System

This directory contains the connector creation system that allows users to create Airbyte sources and destinations through dynamic forms.

## Architecture

### Main Components

1. **AddConnectorModal** - The main modal that shows available connectors
2. **ConnectorConfigForm** - The configuration form wrapper that routes to specific forms
3. **forms/** - Directory containing specific configuration forms for each connector type

### Flow

1. User clicks "Add Source" or "Add Destination" 
2. `AddConnectorModal` opens showing available connector definitions from Airbyte
3. User selects a connector
4. `ConnectorConfigForm` opens with the appropriate form for that connector
5. User fills out the configuration
6. Form submits to `/airbyte/api/public/v1/sources` or `/airbyte/api/public/v1/destinations`
7. New connector is created and the connectors list refreshes

## Supported Connectors

### Sources
- ✅ **PostgreSQL** - Full form with SSL, tunneling, and advanced options
- ✅ **BigQuery** - Full form with service account authentication
- ✅ **Snowflake** - Full form with multiple auth methods (username/password, OAuth, key pair)
- 🚧 **MySQL** - Basic form (expandable)
- 🚧 **MSSQL** - Basic form (expandable)
- 🚧 **Oracle** - Basic form (expandable)
- 🚧 **Redshift** - Basic form (expandable)
- 🚧 **Google Sheets** - Basic form (expandable)
- 🚧 **Google Cloud Storage** - Basic form (expandable)
- 🚧 **Azure Blob Storage** - Basic form (expandable)

### Destinations
- ✅ **BigQuery** - Full form with dataset configuration
- ✅ **Snowflake** - Full form with multiple auth methods
- ✅ **Databricks** - Full form with lakehouse configuration
- ✅ **S3** - Full form with format options
- 🚧 **Redshift** - Basic form (expandable)
- 🚧 **Google Cloud Storage** - Basic form (expandable)
- 🚧 **Azure Blob Storage** - Basic form (expandable)

## Adding New Connector Forms

To add a new connector form:

1. Create a new form component in `forms/` directory:
```tsx
// forms/MyConnectorForm.tsx
'use client'
import { useState, useEffect } from 'react'

interface MyConnectorFormProps {
  configuration: any
  onChange: (config: any) => void
  type?: 'source' | 'destination' // if connector supports both
}

export default function MyConnectorForm({ configuration, onChange, type }: MyConnectorFormProps) {
  const [config, setConfig] = useState({
    // Default configuration based on Airbyte schema
    sourceType: type === 'source' ? 'my-connector' : undefined,
    destinationType: type === 'destination' ? 'my-connector' : undefined,
    ...configuration
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  // Form implementation...
}
```

2. Import and add to `ConnectorConfigForm.tsx`:
```tsx
import MyConnectorForm from './forms/MyConnectorForm'

// In renderConfigurationForm():
if (connectorName.includes('my-connector')) {
  return <MyConnectorForm configuration={configuration} onChange={setConfiguration} type={type} />
}
```

## Configuration Schema Reference

Each connector form should follow the configuration schema defined in the main request. Key points:

- Always include `sourceType` for sources or `destinationType` for destinations
- Use nested objects for complex configurations (e.g., `ssl_mode`, `credentials`)
- Provide sensible defaults
- Mark required fields clearly in the UI
- Include helpful placeholder text and descriptions

## API Integration

The forms integrate with these Airbyte API endpoints:

- **POST** `/airbyte/api/public/v1/sources` - Create source connector
- **POST** `/airbyte/api/public/v1/destinations` - Create destination connector

Request body format:
```json
{
  "name": "User-defined connector name",
  "definitionId": "UUID from connector definition",
  "workspaceId": "User's workspace UUID", 
  "configuration": {
    // Dynamic configuration based on connector type
  }
}
```

## Error Handling

- Form validation happens client-side before submission
- API errors are displayed as toast notifications
- Users can retry failed submissions
- Configuration is preserved during errors

## Future Enhancements

- [ ] Form validation schemas
- [ ] Connection testing before creation
- [ ] Configuration templates/presets
- [ ] Bulk connector creation
- [ ] Configuration import/export
- [ ] Advanced field validation
- [ ] Conditional field display
- [ ] Form field dependencies
