'use client'
import { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON>ef<PERSON>, Loader2 } from 'lucide-react'
import { useAuthStore } from '@/store/useAuthStore'
import { airbyteService, type AirbyteSourceDefinition, type AirbyteDestinationDefinition } from '@/lib/airbyteService'
import { useAsyncWithTokenRefresh } from '@/components/hoc/withTokenRefresh'
import { cn } from '@/lib/utils'
import toast from 'react-hot-toast'

// Import form components
import PostgreSQLForm from './forms/PostgreSQLForm'
import MySQLForm from './forms/MySQLForm'
import MSSQLForm from './forms/MSSQLForm'
import OracleForm from './forms/OracleForm'
import SnowflakeForm from './forms/SnowflakeForm'
import BigQueryForm from './forms/BigQueryForm'
import RedshiftForm from './forms/RedshiftForm'
import GoogleSheetsForm from './forms/GoogleSheetsForm'
import GCSForm from './forms/GCSForm'
import AzureBlobForm from './forms/AzureBlobForm'
import GithubForm from './forms/GithubForm'
import GitlabForm from './forms/GitlabForm'

// Destination forms
import DatabricksForm from './forms/DatabricksForm'
import S3Form from './forms/S3Form'

interface ConnectorConfigFormProps {
  isOpen: boolean
  onClose: () => void
  onBack: () => void
  definition: AirbyteSourceDefinition | AirbyteDestinationDefinition
  type: 'source' | 'destination'
  onSuccess?: () => void
  initialConfiguration?: any
  onConfigurationChange?: (config: any) => void
  isEditMode?: boolean
}

export default function ConnectorConfigForm({
  isOpen,
  onClose,
  onBack,
  definition,
  type,
  onSuccess,
  initialConfiguration,
  onConfigurationChange,
  isEditMode = false
}: ConnectorConfigFormProps) {
  const { user } = useAuthStore()
  const { withTokenRefresh } = useAsyncWithTokenRefresh()
  const [loading, setLoading] = useState(false)
  const [connectorName, setConnectorName] = useState('')
  const [configuration, setConfiguration] = useState<any>(initialConfiguration || {})

  // Initialize configuration from props
  useEffect(() => {
    if (initialConfiguration) {
      setConfiguration(initialConfiguration)
    }
  }, [initialConfiguration])

  // Sync configuration changes to parent component
  useEffect(() => {
    if (onConfigurationChange) {
      onConfigurationChange(configuration)
    }
  }, [configuration, onConfigurationChange])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Skip creation logic when in edit mode (handled by parent component)
    if (isEditMode) {
      return
    }

    if (!user?.id) {
      toast.error('User not authenticated')
      return
    }

    if (!connectorName.trim()) {
      toast.error('Please enter a connector name')
      return
    }

    setLoading(true)
    try {
      await withTokenRefresh(async () => {
        if (type === 'source') {
          await airbyteService.createSource(user.id, {
            name: connectorName,
            definitionId: definition.id,
            configuration
          })
          toast.success(`Source "${connectorName}" created successfully!`)
        } else {
          await airbyteService.createDestination(user.id, {
            name: connectorName,
            definitionId: definition.id,
            configuration
          })
          toast.success(`Destination "${connectorName}" created successfully!`)
        }
      })
      
      onSuccess?.()
      onClose()
    } catch (error: any) {
      console.error('Error creating connector:', error)
      toast.error(error.message || 'Failed to create connector')
    } finally {
      setLoading(false)
    }
  }

  const renderConfigurationForm = () => {
    const connectorName = definition.name.toLowerCase()
    
    // Source forms
    if (type === 'source') {
      if (connectorName.includes('postgres')) {
        return <PostgreSQLForm configuration={configuration} onChange={setConfiguration} />
      }
      if (connectorName.includes('mysql')) {
        return <MySQLForm configuration={configuration} onChange={setConfiguration} />
      }
      if (connectorName.includes('mssql') || connectorName.includes('sql server')) {
        return <MSSQLForm configuration={configuration} onChange={setConfiguration} />
      }
      if (connectorName.includes('oracle')) {
        return <OracleForm configuration={configuration} onChange={setConfiguration} />
      }
      if (connectorName.includes('snowflake')) {
        return <SnowflakeForm configuration={configuration} onChange={setConfiguration} type="source" />
      }
      if (connectorName.includes('bigquery')) {
        return <BigQueryForm configuration={configuration} onChange={setConfiguration} type="source" />
      }
      if (connectorName.includes('redshift')) {
        return <RedshiftForm configuration={configuration} onChange={setConfiguration} type="source" />
      }
      if (connectorName.includes('google sheets')) {
        return <GoogleSheetsForm configuration={configuration} onChange={setConfiguration} />
      }
      if (connectorName.includes('gcs') || connectorName.includes('google cloud storage')) {
        return <GCSForm configuration={configuration} onChange={setConfiguration} type="source" />
      }
      if (connectorName.includes('azure blob')) {
        return <AzureBlobForm configuration={configuration} onChange={setConfiguration} type="source" />
      }
      if (connectorName.includes('github')) {
        return <GithubForm configuration={configuration} onChange={setConfiguration} />
      }
      if (connectorName.includes('gitlab')) {
        return <GitlabForm configuration={configuration} onChange={setConfiguration} />
      }
    }
    
    // Destination forms
    if (type === 'destination') {
      if (connectorName.includes('databricks')) {
        return <DatabricksForm configuration={configuration} onChange={setConfiguration} />
      }
      if (connectorName.includes('bigquery')) {
        return <BigQueryForm configuration={configuration} onChange={setConfiguration} type="destination" />
      }
      if (connectorName.includes('snowflake')) {
        return <SnowflakeForm configuration={configuration} onChange={setConfiguration} type="destination" />
      }
      if (connectorName.includes('redshift')) {
        return <RedshiftForm configuration={configuration} onChange={setConfiguration} type="destination" />
      }
      if (connectorName.includes('s3')) {
        return <S3Form configuration={configuration} onChange={setConfiguration} />
      }
      if (connectorName.includes('gcs') || connectorName.includes('google cloud storage')) {
        return <GCSForm configuration={configuration} onChange={setConfiguration} type="destination" />
      }
      if (connectorName.includes('azure blob')) {
        return <AzureBlobForm configuration={configuration} onChange={setConfiguration} type="destination" />
      }
    }

    // Fallback for unsupported connectors
    return (
      <div className="text-center py-8">
        <p className="text-neutral-600 mb-4">
          Configuration form for {definition.name} is not yet implemented.
        </p>
        <p className="text-sm text-neutral-500">
          Please configure this connector directly in Airbyte.
        </p>
      </div>
    )
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 overflow-y-auto">
      <div className="min-h-screen flex items-start justify-center p-0 md:p-4">
        <div className="bg-white md:rounded-2xl shadow-2xl max-w-4xl w-full min-h-screen md:min-h-0 md:max-h-[95vh] flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-200">
          <div className="flex items-center space-x-3">
            <button
              onClick={onBack}
              className="p-2 hover:bg-neutral-100 rounded-xl transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-neutral-500" />
            </button>
            <div>
              <h2 className="text-2xl font-bold text-neutral-900">
                Configure {definition.name}
              </h2>
              <p className="text-neutral-600 mt-1">
                Set up your {type} connector configuration
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-neutral-100 rounded-xl transition-colors"
          >
            <X className="w-5 h-5 text-neutral-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="flex flex-col flex-1 min-h-0">
          <div className="flex-1 overflow-y-auto p-6 min-h-0">
            {/* Connector Name */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Connector Name *
              </label>
              <input
                type="text"
                value={connectorName}
                onChange={(e) => setConnectorName(e.target.value)}
                placeholder={`My ${definition.name} ${type}`}
                className="input w-full"
                required
              />
            </div>

            {/* Configuration Form */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-neutral-900">Configuration</h3>
              {renderConfigurationForm()}
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-neutral-200 bg-neutral-50 flex-shrink-0 md:rounded-b-2xl">
            <button
              type="button"
              onClick={onBack}
              className="btn-secondary"
              disabled={loading}
            >
              Back
            </button>
            <button
              type="submit"
              className={cn(
                "btn-primary",
                loading && "opacity-50 cursor-not-allowed"
              )}
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  Creating...
                </>
              ) : (
                `Create ${type === 'source' ? 'Source' : 'Destination'}`
              )}
            </button>
          </div>
        </form>
        </div>
      </div>
    </div>
  )
}
