'use client'
import { useState, useMemo, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { Plus, Search, Database, Check, X, AlertCircle, Settings, Github, GitBranch, ChevronDown, Trash2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useAuthStore } from '@/store/useAuthStore'
import { airbyteService, type AirbyteSource, type AirbyteDestination } from '@/lib/airbyteService'
import { useAsyncWithTokenRefresh } from '@/components/hoc/withTokenRefresh'
import AddConnectorModal from './AddConnectorModal'
import EditConnectorModal from './EditConnectorModal'
import toast from 'react-hot-toast'

interface Connector {
  id: string
  name: string
  type: 'source' | 'destination'
  status: 'connected' | 'disconnected' | 'error'
  connectionCount: number
  lastConnected: string
  description: string
  icon: string
  category: string
  connectorType?: string | null
  database?: string | null
  schemas?: string[] | null
  additionalInfo?: string | null
}

export default function ConnectorsPage() {
  const searchParams = useSearchParams()
  const { user } = useAuthStore()
  const { withTokenRefresh } = useAsyncWithTokenRefresh()
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'source' | 'destination'>('all')
  const [filterStatus, setFilterStatus] = useState<'all' | 'connected' | 'disconnected' | 'error'>('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [addModalType, setAddModalType] = useState<'source' | 'destination'>('source')
  const [showAddDropdown, setShowAddDropdown] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [editingConnector, setEditingConnector] = useState<{
    id: string
    name: string
    type: 'source' | 'destination'
    connectorType: string
    connectionConfiguration: any
  } | null>(null)
  const [loading, setLoading] = useState(false)
  const [sources, setSources] = useState<AirbyteSource[]>([])
  const [destinations, setDestinations] = useState<AirbyteDestination[]>([])
  const [error, setError] = useState<string | null>(null)

  const handleAddConnector = (type: 'source' | 'destination') => {
    setAddModalType(type)
    setShowAddModal(true)
    setShowAddDropdown(false)
  }

  const handleEditConnector = (connector: Connector) => {
    // Find the original source or destination data
    const originalItem = connector.type === 'source'
      ? sources.find(s => s.sourceId === connector.id)
      : destinations.find(d => d.destinationId === connector.id)

    if (originalItem) {
      setEditingConnector({
        id: connector.id,
        name: connector.name,
        type: connector.type,
        connectorType: connector.connectorType || 'unknown',
        connectionConfiguration: originalItem.connectionConfiguration
      })
      setShowEditModal(true)
    }
  }

  const handleDeleteConnector = async (connector: Connector) => {
    if (!user?.id) {
      toast.error('User not authenticated')
      return
    }

    const confirmDelete = window.confirm(
      `Are you sure you want to delete the ${connector.type} "${connector.name}"? This action cannot be undone.`
    )

    if (!confirmDelete) return

    setLoading(true)
    try {
      await withTokenRefresh(async () => {
        if (connector.type === 'source') {
          await airbyteService.deleteSource(user.id, connector.id)
          toast.success(`Source "${connector.name}" deleted successfully!`)
        } else {
          await airbyteService.deleteDestination(user.id, connector.id)
          toast.success(`Destination "${connector.name}" deleted successfully!`)
        }
      })

      // Reload connectors after successful deletion
      loadConnectors()
    } catch (error) {
      console.error('Error deleting connector:', error)
      toast.error(`Failed to delete ${connector.type}`)
    } finally {
      setLoading(false)
    }
  }

  // Load actual sources and destinations from Airbyte
  const loadConnectors = async () => {
    if (!user?.id) return

    setLoading(true)
    setError(null)
    try {
      await withTokenRefresh(async () => {
        const [sourcesData, destinationsData] = await Promise.all([
          airbyteService.getSources(user.id),
          airbyteService.getDestinations(user.id)
        ])
        setSources(sourcesData)
        setDestinations(destinationsData)
      })
    } catch (err) {
      console.error('Error loading connectors:', err)
      setError('Failed to load connectors')
    } finally {
      setLoading(false)
    }
  }

  // Load connectors when component mounts or user changes
  useEffect(() => {
    if (user?.id) {
      loadConnectors()
    }
  }, [user?.id])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (showAddDropdown) {
        setShowAddDropdown(false)
      }
    }

    if (showAddDropdown) {
      document.addEventListener('click', handleClickOutside)
    }

    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [showAddDropdown])

  // Set initial filter based on URL parameter
  useEffect(() => {
    const tab = searchParams.get('tab')
    if (tab === 'sources') {
      setFilterType('source')
    } else if (tab === 'destinations') {
      setFilterType('destination')
    } else {
      setFilterType('all')
    }
  }, [searchParams])

  // Helper function to extract configuration details based on connector type
  const extractConfigurationDetails = (configuration: any) => {
    if (!configuration) {
      return { database: null, schemas: null, connectorType: null, additionalInfo: null }
    }

    // Extract connector type
    const connectorType = configuration.sourceType || configuration.destinationType || null

    if (!connectorType) {
      return { database: null, schemas: null, connectorType: null, additionalInfo: null }
    }

    let database = null
    let schemas = null
    let additionalInfo = null

    // Extract configuration based on specific connector type
    switch (connectorType) {
      // Sources
      case 'azure-blob-storage':
        if (configuration.sourceType) {
          // Source: Azure Blob Storage
          database = configuration.azure_blob_storage_container_name
          additionalInfo = `Account: ${configuration.azure_blob_storage_account_name || 'N/A'}`
        } else {
          // Destination: Azure Blob Storage
          database = configuration.azure_blob_storage_container_name
          additionalInfo = `Account: ${configuration.azure_blob_storage_account_name || 'N/A'}`
        }
        break

      case 'bigquery':
        database = configuration.dataset_id || configuration.project_id
        if (configuration.sourceType) {
          additionalInfo = `Project: ${configuration.project_id || 'N/A'}`
        } else {
          additionalInfo = `Project: ${configuration.project_id || 'N/A'} • Location: ${configuration.dataset_location || 'N/A'}`
        }
        break

      case 'gcs':
        database = configuration.bucket || configuration.gcs_bucket_name
        if (configuration.sourceType) {
          additionalInfo = `Service Account: ${configuration.service_account ? 'Configured' : 'N/A'}`
        } else {
          additionalInfo = `Region: ${configuration.gcs_bucket_region || 'us'} • Path: ${configuration.gcs_bucket_path || '/'}`
        }
        break

      case 'google-sheets':
        database = configuration.spreadsheet_id
        additionalInfo = `Auth: ${configuration.credentials?.auth_type || 'N/A'} • Batch: ${configuration.batch_size || 200}`
        break

      case 'mssql':
        database = configuration.database
        schemas = Array.isArray(configuration.schemas) ? configuration.schemas : (configuration.schemas ? [configuration.schemas] : ['dbo'])
        additionalInfo = `Host: ${configuration.host || 'N/A'}:${configuration.port || 1433}`
        break

      case 'oracle':
        database = configuration.connection_data?.service_name || configuration.connection_data?.sid
        schemas = Array.isArray(configuration.schemas) ? configuration.schemas : (configuration.schemas ? [configuration.schemas] : ['user'])
        additionalInfo = `Host: ${configuration.host || 'N/A'}:${configuration.port || 1521}`
        break

      case 'postgres':
        database = configuration.database
        schemas = configuration.schema ? [configuration.schema] : ['public']
        additionalInfo = `Host: ${configuration.host || 'N/A'}:${configuration.port || 5432}`
        break

      case 'mysql':
        database = configuration.database
        schemas = configuration.schema ? [configuration.schema] : ['public']
        additionalInfo = `Host: ${configuration.host || 'N/A'}:${configuration.port || 3306}`
        break

      case 'redshift':
        database = configuration.database
        if (configuration.sourceType) {
          schemas = configuration.schema ? [configuration.schema] : ['public']
          additionalInfo = `Host: ${configuration.host || 'N/A'}:${configuration.port || 5439}`
        } else {
          schemas = configuration.schema ? [configuration.schema] : ['public']
          additionalInfo = `Host: ${configuration.host || 'N/A'}:${configuration.port || 5439} • Raw Schema: ${configuration.raw_data_schema || 'ironbook_ingest'}`
        }
        break

      case 'snowflake':
        database = configuration.database
        schemas = configuration.schema ? [configuration.schema] : null
        if (configuration.sourceType) {
          additionalInfo = `Host: ${configuration.host || 'N/A'} • Warehouse: ${configuration.warehouse || 'N/A'}`
        } else {
          additionalInfo = `Host: ${configuration.host || 'N/A'} • Warehouse: ${configuration.warehouse || 'N/A'} • Role: ${configuration.role || 'N/A'}`
        }
        break

      case 'databricks':
        database = configuration.database
        schemas = configuration.schema ? [configuration.schema] : null
        const authType = configuration.authentication?.auth_type || 'N/A'
        additionalInfo = `Host: ${configuration.databricks_server_hostname || 'N/A'} • Port: ${configuration.databricks_port || 443} • Auth: ${authType}`
        break

      case 's3':
        database = configuration.s3_bucket_name
        additionalInfo = `Region: ${configuration.s3_bucket_region || 'N/A'} • Path: ${configuration.s3_bucket_path || '/'}`
        break

      case 'github':
        database = configuration.repositories?.length > 0 ? `${configuration.repositories.length} repositories` : 'No repositories'
        schemas = configuration.branches?.length > 0 ? configuration.branches : null
        additionalInfo = `API: ${configuration.api_url || 'https://api.github.com/'} • Auth: ${configuration.credentials?.option_title || 'PAT'}`
        break

      case 'gitlab':
        const projectsCount = configuration.projects_list?.length || 0
        const groupsCount = configuration.groups_list?.length || 0
        database = projectsCount > 0 ? `${projectsCount} projects` : groupsCount > 0 ? `${groupsCount} groups` : 'No projects/groups'
        additionalInfo = `API: ${configuration.api_url || 'gitlab.com'} • Auth: ${configuration.credentials?.auth_type || 'access_token'}`
        break

      default:
        // Fallback for unknown connector types
        database = configuration.database || configuration.bucket || configuration.container_name || null
        if (configuration.schemas) {
          schemas = Array.isArray(configuration.schemas) ? configuration.schemas : [configuration.schemas]
        } else if (configuration.schema) {
          schemas = [configuration.schema]
        }
        break
    }

    return { database, schemas, connectorType, additionalInfo }
  }

  // Transform Airbyte sources and destinations to Connector format
  const transformAirbyteToConnector = (item: AirbyteSource | AirbyteDestination, type: 'source' | 'destination'): Connector => {
    const isSource = 'sourceId' in item
    const id = isSource ? item.sourceId : item.destinationId
    const name = item.name || `${type} connector`
    const configuration = item.connectionConfiguration || {}

    // Get connector type from the item itself (not from configuration)
    const connectorTypeFromItem = (item as any).sourceType || (item as any).destinationType || null

    // Create enhanced configuration object with the connector type
    const enhancedConfiguration = {
      ...configuration,
      sourceType: type === 'source' ? connectorTypeFromItem : undefined,
      destinationType: type === 'destination' ? connectorTypeFromItem : undefined
    }



    // Extract configuration details
    const { database, schemas, connectorType, additionalInfo } = extractConfigurationDetails(enhancedConfiguration)

    // Determine category based on connector type or name
    const typeToCheck = connectorType || name.toLowerCase()
    let category = 'database' // default

    if (typeToCheck.includes('github') || typeToCheck.includes('gitlab')) {
      category = 'repository'
    } else if (typeToCheck.includes('salesforce') || typeToCheck.includes('crm')) {
      category = 'crm'
    } else if (typeToCheck.includes('databricks') || typeToCheck.includes('snowflake') || typeToCheck.includes('synapse')) {
      category = 'lakehouse'
    } else if (typeToCheck.includes('s3') || typeToCheck.includes('storage') || typeToCheck.includes('blob') || typeToCheck.includes('gcs')) {
      category = 'storage'
    } else if (typeToCheck.includes('sheets') || typeToCheck.includes('google-sheets')) {
      category = 'productivity'
    }

    // Determine icon based on connector type or name
    let icon = 'database' // default
    if (typeToCheck.includes('github')) {
      icon = 'github'
    } else if (typeToCheck.includes('gitlab')) {
      icon = 'gitlab'
    } else if (typeToCheck.includes('snowflake')) {
      icon = 'snowflake'
    } else if (typeToCheck.includes('salesforce')) {
      icon = 'cloud'
    } else if (typeToCheck.includes('s3')) {
      icon = 'cloud'
    } else if (typeToCheck.includes('azure')) {
      icon = 'cloud'
    } else if (typeToCheck.includes('gcs') || typeToCheck.includes('google')) {
      icon = 'cloud'
    }

    // Create a simple description
    let description = `${type.charAt(0).toUpperCase() + type.slice(1)} connector`
    if (connectorType) {
      description += ` for ${connectorType}`
    }

    const result = {
      id,
      name,
      type,
      status: 'connected' as const, // Assume connected since they exist in workspace
      connectionCount: 1, // We don't have this data from Airbyte API
      lastConnected: 'Recently', // We don't have this data from Airbyte API
      description,
      icon,
      category,
      // Add configuration details for display
      connectorType,
      database,
      schemas,
      additionalInfo
    }

    return result
  }

  // Combine sources and destinations into unified connector list
  const allConnectors = useMemo(() => {
    const sourceConnectors = sources.map(source => transformAirbyteToConnector(source, 'source'))
    const destinationConnectors = destinations.map(dest => transformAirbyteToConnector(dest, 'destination'))
    return [...sourceConnectors, ...destinationConnectors]
  }, [sources, destinations])



  const connectors = useMemo(() => {
    // Only use real data from Airbyte, no mock data fallback
    return allConnectors.filter((connector: Connector) => {
      const matchesSearch = connector.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        connector.description.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesType = filterType === 'all' || connector.type === filterType
      const matchesStatus = filterStatus === 'all' || connector.status === filterStatus

      return matchesSearch && matchesType && matchesStatus
    })
  }, [searchQuery, filterType, filterStatus, allConnectors])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <Check className="w-4 h-4 text-success-500" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-error-500" />
      default:
        return <X className="w-4 h-4 text-neutral-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'bg-success-100 text-success-700 border-success-200'
      case 'error':
        return 'bg-error-100 text-error-700 border-error-200'
      default:
        return 'bg-neutral-100 text-neutral-700 border-neutral-200'
    }
  }

  const sourceConnectors = allConnectors.filter(c => c.type === 'source')
  const destinationConnectors = allConnectors.filter(c => c.type === 'destination')

  // Stats based on current filter/tab
  const getStatsForCurrentView = () => {
    const currentConnectors = connectors
    const connectedInView = currentConnectors.filter((c: Connector) => c.status === 'connected').length
    const disconnectedInView = currentConnectors.filter((c: Connector) => c.status === 'disconnected').length
    const errorInView = currentConnectors.filter((c: Connector) => c.status === 'error').length

    return {
      total: currentConnectors.length,
      connected: connectedInView,
      disconnected: disconnectedInView,
      error: errorInView
    }
  }

  const currentStats = getStatsForCurrentView()

  // Get title and description based on current filter
  const getPageInfo = () => {
    switch (filterType) {
      case 'source':
        return {
          title: 'Data Sources',
          description: 'Connect your databases, APIs, and data sources',
          primaryStat: { label: 'Sources', value: currentStats.total, color: 'primary' },
          secondaryStat: { label: 'Connected Sources', value: currentStats.connected, color: 'success' }
        }
      case 'destination':
        return {
          title: 'Data Destinations',
          description: 'Connect your data warehouses, lakes, and destination systems',
          primaryStat: { label: 'Destinations', value: currentStats.total, color: 'secondary' },
          secondaryStat: { label: 'Connected Destinations', value: currentStats.connected, color: 'success' }
        }
      default:
        return {
          title: 'Data Connectors',
          description: 'Connect your data sources and destination lakehouses',
          primaryStat: { label: 'Total Connectors', value: currentStats.total, color: 'primary' },
          secondaryStat: { label: 'Connected', value: currentStats.connected, color: 'success' }
        }
    }
  }

  const pageInfo = getPageInfo()

  const getConnectorIcon = (icon: string) => {
    switch (icon) {
      case 'github':
        return <Github className="w-6 h-6 text-white" />
      case 'gitlab':
        return <GitBranch className="w-6 h-6 text-white" />
      default:
        return <Database className="w-6 h-6 text-white" />
    }
  }

  const getConnectorColor = (type: string, category: string) => {
    if (type === 'source') {
      if (category === 'repository') {
        return 'from-neutral-800 to-neutral-900'
      }
      return 'from-primary-500 to-primary-600'
    }
    return 'from-secondary-500 to-secondary-600'
  }

  return (
    <div className="p-6 space-y-6 relative">
      {/* Modern background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-50/30 via-transparent to-secondary-50/20 pointer-events-none" />
      
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between relative z-10">
        <div>
          <div className="flex items-center space-x-3">
            <h1 className="text-3xl font-bold text-gradient-primary">{pageInfo.title}</h1>
            {!loading && !error && (sources.length > 0 || destinations.length > 0) && (
              <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full font-medium">
                Live Data
              </span>
            )}
          </div>
          <p className="text-neutral-600 mt-2">{pageInfo.description}</p>

          {/* Quick Stats */}
          <div className="flex items-center space-x-6 mt-3">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 bg-${pageInfo.primaryStat.color}-500 rounded-full`}></div>
              <span className="text-sm text-neutral-600">{pageInfo.primaryStat.value} {pageInfo.primaryStat.label}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 bg-${pageInfo.secondaryStat.color}-500 rounded-full`}></div>
              <span className="text-sm text-neutral-600">{pageInfo.secondaryStat.value} {pageInfo.secondaryStat.label}</span>
            </div>
            {filterType === 'all' && (
              <>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <span className="text-sm text-neutral-600">{sourceConnectors.length} Sources</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-secondary-500 rounded-full"></div>
                  <span className="text-sm text-neutral-600">{destinationConnectors.length} Destinations</span>
                </div>
              </>
            )}
          </div>
        </div>
        <div className="relative mt-4 sm:mt-0">
          <button
            onClick={() => setShowAddDropdown(!showAddDropdown)}
            className="btn-gradient flex items-center space-x-2 hover-lift"
          >
            <Plus className="w-4 h-4" />
            <span>Add Connector</span>
            <ChevronDown className="w-4 h-4" />
          </button>

          {showAddDropdown && (
            <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-xl shadow-lg border border-neutral-200 py-2 z-10">
              <button
                onClick={() => handleAddConnector('source')}
                className="w-full px-4 py-2 text-left hover:bg-neutral-50 transition-colors flex items-center space-x-2"
              >
                <Database className="w-4 h-4 text-primary-600" />
                <span>Add Data Source</span>
              </button>
              <button
                onClick={() => handleAddConnector('destination')}
                className="w-full px-4 py-2 text-left hover:bg-neutral-50 transition-colors flex items-center space-x-2"
              >
                <Database className="w-4 h-4 text-secondary-600" />
                <span>Add Destination</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search connectors..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="input pl-10 w-full"
          />
        </div>

        {/* Type Filter */}
        {/* <select 
          value={filterType}
          onChange={(e) => setFilterType(e.target.value as any)}
          className="input w-auto"
        >
          <option value="all">All Types</option>
          <option value="source">Sources</option>
          <option value="destination">Destinations</option>
        </select> */}

        {/* Status Filter */}
        <select 
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value as any)}
          className="input w-auto"
        >
          <option value="all">All Status</option>
          <option value="connected">Connected</option>
          <option value="disconnected">Disconnected</option>
          <option value="error">Error</option>
        </select>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 relative z-10">
        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">{pageInfo.primaryStat.label}</p>
              <p className={`text-2xl font-bold text-${pageInfo.primaryStat.color}-600`}>{pageInfo.primaryStat.value}</p>
            </div>
            <div className={`w-12 h-12 bg-${pageInfo.primaryStat.color}-100 rounded-xl flex items-center justify-center`}>
              <Database className={`w-6 h-6 text-${pageInfo.primaryStat.color}-600`} />
            </div>
          </div>
        </div>

        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">{pageInfo.secondaryStat.label}</p>
              <p className={`text-2xl font-bold text-${pageInfo.secondaryStat.color}-600`}>{pageInfo.secondaryStat.value}</p>
            </div>
            <div className={`w-12 h-12 bg-${pageInfo.secondaryStat.color}-100 rounded-xl flex items-center justify-center`}>
              <Check className={`w-6 h-6 text-${pageInfo.secondaryStat.color}-600`} />
            </div>
          </div>
        </div>

        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">Disconnected</p>
              <p className="text-2xl font-bold text-warning-600">{currentStats.disconnected}</p>
            </div>
            <div className="w-12 h-12 bg-warning-100 rounded-xl flex items-center justify-center">
              <X className="w-6 h-6 text-warning-600" />
            </div>
          </div>
        </div>

        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">Errors</p>
              <p className="text-2xl font-bold text-error-600">{currentStats.error}</p>
            </div>
            <div className="w-12 h-12 bg-error-100 rounded-xl flex items-center justify-center">
              <AlertCircle className="w-6 h-6 text-error-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-neutral-600">Loading connectors...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && !loading && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
          <p className="text-red-700 font-medium mb-2">Failed to load connectors</p>
          <p className="text-red-600 text-sm mb-4">{error}</p>
          <button
            onClick={loadConnectors}
            className="btn-primary"
          >
            Try Again
          </button>
        </div>
      )}

      {/* Connectors Grid */}
      {!loading && !error && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {connectors.map((connector, index) => (
          <div 
            key={connector.id} 
            className="card-gradient p-6 hover-lift group cursor-pointer relative overflow-hidden"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            {/* Status indicator */}
            <div className="absolute top-4 right-4">
              <div className={cn(
                "status-dot-pulse",
                connector.status === 'connected' && "status-connected",
                connector.status === 'error' && "status-error",
                connector.status === 'disconnected' && "bg-neutral-400"
              )} />
            </div>
            
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className={cn(
                  "w-12 h-12 bg-gradient-to-br rounded-lg flex items-center justify-center hover-glow group-hover:scale-110 transition-all duration-300",
                  getConnectorColor(connector.type, connector.category)
                )}>
                  {getConnectorIcon(connector.icon)}
                </div>
                <div>
                  <h3 className="font-semibold text-neutral-900">{connector.name}</h3>
                  <div className="flex items-center space-x-2 mb-1">
                    <span className={cn(
                      "text-xs px-2 py-1 rounded-full font-medium",
                      connector.type === 'source'
                        ? "bg-primary-100 text-primary-700"
                        : "bg-secondary-100 text-secondary-700"
                    )}>
                      {connector.type}
                    </span>
                    {connector.connectorType && (
                      <span className="text-xs px-2 py-1 rounded-full bg-neutral-100 text-neutral-700 font-medium">
                        {connector.connectorType}
                      </span>
                    )}
                    <span className="text-xs text-neutral-500 capitalize">{connector.category}</span>
                  </div>
                  {/* Configuration details */}
                  <div className="space-y-1">
                    {connector.database && (
                      <div className="flex items-center space-x-1">
                        <span className="text-xs text-neutral-500">
                          {connector.connectorType === 'azure-blob-storage' ? 'Container:' :
                           connector.connectorType === 'gcs' || connector.connectorType === 's3' ? 'Bucket:' :
                           connector.connectorType === 'google-sheets' ? 'Sheet ID:' :
                           'Database:'}
                        </span>
                        <span className="text-xs text-neutral-700 font-medium">{connector.database}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleEditConnector(connector)}
                  className="text-neutral-400 hover:text-neutral-600"
                  title="Edit connector settings"
                >
                  <Settings className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDeleteConnector(connector)}
                  className="text-neutral-400 hover:text-red-600"
                  title="Delete connector"
                  disabled={loading}
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>

            <p className="text-sm text-neutral-600 mb-4">{connector.description}</p>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getStatusIcon(connector.status)}
                <span className={cn(
                  'text-xs px-2 py-1 rounded-full border font-medium',
                  getStatusColor(connector.status)
                )}>
                  {connector.status}
                </span>
              </div>
              
              <div className="text-right">
                <p className="text-sm font-medium text-neutral-900">{connector.connectionCount}</p>
                <p className="text-xs text-neutral-500">connections</p>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-neutral-100">
              <div className="flex items-center justify-between text-sm">
                <span className="text-neutral-600">Last connected:</span>
                <span className="text-neutral-900">{connector.lastConnected}</span>
              </div>
            </div>
          </div>
        ))}
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && connectors.length === 0 && (
        <div className="text-center py-12">
          <Database className="w-12 h-12 text-neutral-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-neutral-900 mb-2">
            {sources.length === 0 && destinations.length === 0
              ? 'No connectors configured'
              : 'No connectors match your filters'
            }
          </h3>
          <p className="text-neutral-600 mb-6">
            {sources.length === 0 && destinations.length === 0
              ? 'Create your first source or destination connector in Airbyte to get started'
              : 'Try adjusting your search or filter criteria'
            }
          </p>
          {/* <button
            onClick={() => setShowAddModal(true)}
            className="btn-primary"
          >
            Add Connector
          </button> */}
        </div>
      )}

      {/* Add Connector Modal */}
      <AddConnectorModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        type={addModalType}
        onSuccess={loadConnectors}
      />

      {/* Edit Connector Modal */}
      {editingConnector && (
        <EditConnectorModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false)
            setEditingConnector(null)
          }}
          connector={editingConnector}
          onSuccess={() => {
            loadConnectors()
            setShowEditModal(false)
            setEditingConnector(null)
          }}
        />
      )}
    </div>
  )
}
