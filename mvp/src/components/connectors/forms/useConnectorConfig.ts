import { useState, useEffect } from 'react'

/**
 * Custom hook to manage connector configuration state
 * Handles initialization and updates when configuration prop changes
 */
export function useConnectorConfig<T>(
  defaultConfig: T,
  configuration: any,
  onChange: (config: T) => void
) {
  const [config, setConfig] = useState<T>(() => {
    return { ...defaultConfig, ...configuration }
  })

  // Update form state when configuration prop changes (e.g., when data is loaded from API)
  useEffect(() => {
    console.log('Connector form received configuration:', configuration)
    if (configuration && Object.keys(configuration).length > 0) {
      const newConfig = { ...defaultConfig, ...configuration }
      console.log('Connector form updating config to:', newConfig)
      setConfig(newConfig)
    }
  }, [configuration, defaultConfig])

  // Notify parent component when config changes
  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig((prev: T) => ({
      ...prev,
      [field]: value
    }))
  }

  const updateNestedConfig = (parentField: string, field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      [parentField]: {
        ...prev[parentField],
        [field]: value
      }
    }))
  }

  return {
    config,
    setConfig,
    updateConfig,
    updateNestedConfig
  }
}
