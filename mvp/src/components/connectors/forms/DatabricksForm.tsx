'use client'
import { useState, useEffect } from 'react'

interface DatabricksFormProps {
  configuration: any
  onChange: (config: any) => void
}

// Databricks Destination configuration class
class DatabricksDestinationConfig {
  accept_terms: boolean = false
  databricks_server_hostname: string = ''
  databricks_http_path: string = ''
  databricks_port: number = 443
  databricks_personal_access_token: string = ''
  database?: string = ''
  schema?: string = ''
  enable_schema_evolution: boolean = true
  destinationType: string = 'databricks'
}

export default function DatabricksForm({ configuration, onChange }: DatabricksFormProps) {
  const [config, setConfig] = useState(() => {
    const destConfig = new DatabricksDestinationConfig()
    return { ...destConfig, ...configuration }
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Server Hostname *
        </label>
        <input
          type="text"
          value={config.databricks_server_hostname}
          onChange={(e) => updateConfig('databricks_server_hostname', e.target.value)}
          placeholder="dbc-12345678-9abc.cloud.databricks.com"
          className="input w-full"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          HTTP Path *
        </label>
        <input
          type="text"
          value={config.databricks_http_path}
          onChange={(e) => updateConfig('databricks_http_path', e.target.value)}
          placeholder="/sql/1.0/warehouses/12345678abcdef90"
          className="input w-full"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Personal Access Token *
        </label>
        <input
          type="password"
          value={config.databricks_personal_access_token}
          onChange={(e) => updateConfig('databricks_personal_access_token', e.target.value)}
          placeholder="dapi1234567890abcdef"
          className="input w-full"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Database/Catalog
          </label>
          <input
            type="text"
            value={config.database}
            onChange={(e) => updateConfig('database', e.target.value)}
            placeholder="main"
            className="input w-full"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Schema
          </label>
          <input
            type="text"
            value={config.schema}
            onChange={(e) => updateConfig('schema', e.target.value)}
            placeholder="default"
            className="input w-full"
          />
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="accept_terms"
          checked={config.accept_terms}
          onChange={(e) => updateConfig('accept_terms', e.target.checked)}
          className="rounded border-neutral-300"
          required
        />
        <label htmlFor="accept_terms" className="text-sm text-neutral-700">
          I accept the terms and conditions *
        </label>
      </div>
    </div>
  )
}
