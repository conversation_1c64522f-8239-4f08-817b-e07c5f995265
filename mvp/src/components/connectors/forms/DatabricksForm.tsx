'use client'
import { useState, useEffect } from 'react'

interface DatabricksFormProps {
  configuration: any
  onChange: (config: any) => void
}

// Databricks Destination configuration class
class DatabricksDestinationConfig {
  accept_terms: boolean = false
  databricks_server_hostname: string = ''
  databricks_http_path: string = ''
  databricks_port: number = 443
  authentication: {
    auth_type: string
    client_id: string
    secret: string
  } = {
    auth_type: 'OAUTH',
    client_id: '',
    secret: ''
  }
  database: string = ''
  schema: string = ''
  enable_schema_evolution: boolean = true
  destinationType: string = 'databricks'
}

export default function DatabricksForm({ configuration, onChange }: DatabricksFormProps) {
  const [config, setConfig] = useState(() => {
    const destConfig = new DatabricksDestinationConfig()
    return { ...destConfig, ...configuration }
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      [field]: value
    }))
  }

  const updateAuthentication = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      authentication: {
        ...prev.authentication,
        [field]: value
      }
    }))
  }

  return (
    <div className="space-y-6">
      {/* Accept Terms */}
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="accept_terms"
          checked={config.accept_terms}
          onChange={(e) => updateConfig('accept_terms', e.target.checked)}
          className="rounded border-neutral-300"
          required
        />
        <label htmlFor="accept_terms" className="text-sm text-neutral-700">
          I accept the terms and conditions *
        </label>
      </div>

      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Server Hostname *
        </label>
        <input
          type="text"
          value={config.databricks_server_hostname}
          onChange={(e) => updateConfig('databricks_server_hostname', e.target.value)}
          placeholder="dbc-12345678-9abc.cloud.databricks.com"
          className="input w-full"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          HTTP Path *
        </label>
        <input
          type="text"
          value={config.databricks_http_path}
          onChange={(e) => updateConfig('databricks_http_path', e.target.value)}
          placeholder="/sql/1.0/warehouses/12345678abcdef90"
          className="input w-full"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Port
        </label>
        <input
          type="number"
          value={config.databricks_port}
          onChange={(e) => updateConfig('databricks_port', parseInt(e.target.value) || 443)}
          placeholder="443"
          className="input w-full"
        />
      </div>

      {/* OAuth Authentication */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-neutral-900">Authentication *</h3>

        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Auth Type
          </label>
          <input
            type="text"
            value={config.authentication.auth_type}
            onChange={(e) => updateAuthentication('auth_type', e.target.value)}
            placeholder="OAUTH"
            className="input w-full"
            readOnly
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Client ID
          </label>
          <input
            type="text"
            value={config.authentication.client_id}
            onChange={(e) => updateAuthentication('client_id', e.target.value)}
            placeholder="your-oauth-client-id"
            className="input w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Client Secret
          </label>
          <input
            type="password"
            value={config.authentication.secret}
            onChange={(e) => updateAuthentication('secret', e.target.value)}
            placeholder="your-oauth-client-secret"
            className="input w-full"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Database (Catalog Name)
          </label>
          <input
            type="text"
            value={config.database}
            onChange={(e) => updateConfig('database', e.target.value)}
            placeholder="main"
            className="input w-full"
          />
          <p className="text-xs text-neutral-500 mt-1">
            Name of the catalog in Databricks
          </p>
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Schema Name
          </label>
          <input
            type="text"
            value={config.schema}
            onChange={(e) => updateConfig('schema', e.target.value)}
            placeholder="default"
            className="input w-full"
          />
          <p className="text-xs text-neutral-500 mt-1">
            Name of the schema within the catalog
          </p>
        </div>
      </div>

      {/* Schema Evolution */}
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="enable_schema_evolution"
          checked={config.enable_schema_evolution}
          onChange={(e) => updateConfig('enable_schema_evolution', e.target.checked)}
          className="rounded border-neutral-300"
        />
        <label htmlFor="enable_schema_evolution" className="text-sm text-neutral-700">
          Enable Schema Evolution (default: true)
        </label>
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">OAuth Setup Instructions:</h4>
        <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
          <li>Go to your Databricks workspace → Settings → Developer → OAuth</li>
          <li>Create a new OAuth application</li>
          <li>Set the redirect URI to your application's callback URL</li>
          <li>Copy the Client ID and Client Secret</li>
          <li>Ensure your OAuth app has the necessary scopes for data access</li>
        </ol>
      </div>
    </div>
  )
}
