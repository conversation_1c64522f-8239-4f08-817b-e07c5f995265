'use client'
import { useState, useEffect } from 'react'

interface BigQueryFormProps {
  configuration: any
  onChange: (config: any) => void
  type: 'source' | 'destination'
}

export default function BigQueryForm({ configuration, onChange, type }: BigQueryFormProps) {
  const [config, setConfig] = useState({
    project_id: '',
    credentials_json: '',
    // Source specific
    dataset_id: type === 'source' ? '' : undefined,
    sourceType: type === 'source' ? 'bigquery' : undefined,
    // Destination specific
    dataset_location: type === 'destination' ? '' : undefined,
    raw_data_dataset: type === 'destination' ? 'ironbook_ingest' : undefined,
    destinationType: type === 'destination' ? 'bigquery' : undefined,
    ...configuration
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="space-y-6">
      {/* Project ID */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Project ID *
        </label>
        <input
          type="text"
          value={config.project_id}
          onChange={(e) => updateConfig('project_id', e.target.value)}
          placeholder="my-gcp-project"
          className="input w-full"
          required
        />
        <p className="text-xs text-neutral-500 mt-1">
          Your Google Cloud Project ID
        </p>
      </div>

      {/* Dataset ID (Source only) */}
      {type === 'source' && (
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Dataset ID
          </label>
          <input
            type="text"
            value={config.dataset_id}
            onChange={(e) => updateConfig('dataset_id', e.target.value)}
            placeholder="my_dataset"
            className="input w-full"
          />
          <p className="text-xs text-neutral-500 mt-1">
            The dataset ID to sync from. Leave empty to sync all datasets.
          </p>
        </div>
      )}

      {/* Destination specific fields */}
      {type === 'destination' && (
        <>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Dataset Location *
            </label>
            <select
              value={config.dataset_location}
              onChange={(e) => updateConfig('dataset_location', e.target.value)}
              className="input w-full"
              required
            >
              <option value="">Select location</option>
              <option value="US">US (Multi-region)</option>
              <option value="EU">EU (Multi-region)</option>
              <option value="us-central1">us-central1</option>
              <option value="us-east1">us-east1</option>
              <option value="us-east4">us-east4</option>
              <option value="us-west1">us-west1</option>
              <option value="us-west2">us-west2</option>
              <option value="us-west3">us-west3</option>
              <option value="us-west4">us-west4</option>
              <option value="europe-north1">europe-north1</option>
              <option value="europe-west1">europe-west1</option>
              <option value="europe-west2">europe-west2</option>
              <option value="europe-west3">europe-west3</option>
              <option value="europe-west4">europe-west4</option>
              <option value="europe-west6">europe-west6</option>
              <option value="asia-east1">asia-east1</option>
              <option value="asia-east2">asia-east2</option>
              <option value="asia-northeast1">asia-northeast1</option>
              <option value="asia-northeast2">asia-northeast2</option>
              <option value="asia-northeast3">asia-northeast3</option>
              <option value="asia-south1">asia-south1</option>
              <option value="asia-southeast1">asia-southeast1</option>
              <option value="asia-southeast2">asia-southeast2</option>
              <option value="australia-southeast1">australia-southeast1</option>
            </select>
            <p className="text-xs text-neutral-500 mt-1">
              The location where your BigQuery datasets will be created
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Dataset ID *
            </label>
            <input
              type="text"
              value={config.dataset_id}
              onChange={(e) => updateConfig('dataset_id', e.target.value)}
              placeholder="my_destination_dataset"
              className="input w-full"
              required
            />
            <p className="text-xs text-neutral-500 mt-1">
              BigQuery Dataset ID that tables are replicated to if the source does not specify a namespace
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Raw Data Dataset
            </label>
            <input
              type="text"
              value={config.raw_data_dataset}
              onChange={(e) => updateConfig('raw_data_dataset', e.target.value)}
              placeholder="ironbook_ingest"
              className="input w-full"
            />
            <p className="text-xs text-neutral-500 mt-1">
              The dataset to write raw tables into (default: ironbook_ingest)
            </p>
          </div>
        </>
      )}

      {/* Service Account Credentials */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Service Account Key JSON *
        </label>
        <textarea
          value={config.credentials_json}
          onChange={(e) => updateConfig('credentials_json', e.target.value)}
          placeholder={`**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`}
          className="input w-full h-48 font-mono text-sm"
          required
        />
        <p className="text-xs text-neutral-500 mt-1">
          The contents of your Service Account Key JSON file. Make sure the service account has the necessary BigQuery permissions.
        </p>
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">Setup Instructions:</h4>
        <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
          <li>Go to the Google Cloud Console</li>
          <li>Create or select a project</li>
          <li>Enable the BigQuery API</li>
          <li>Create a service account with BigQuery permissions</li>
          <li>Download the service account key as JSON</li>
          <li>Copy and paste the JSON content above</li>
        </ol>
      </div>
    </div>
  )
}
