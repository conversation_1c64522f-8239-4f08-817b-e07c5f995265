'use client'
import { useState, useEffect } from 'react'

interface GoogleSheetsFormProps {
  configuration: any
  onChange: (config: any) => void
}

export default function GoogleSheetsForm({ configuration, onChange }: GoogleSheetsFormProps) {
  const [config, setConfig] = useState({
    spreadsheet_id: '',
    sourceType: 'google-sheets',
    ...configuration
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="space-y-6">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <p className="text-sm text-yellow-800">
          Google Sheets configuration form is under development. Basic fields are shown below.
        </p>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Spreadsheet ID *
        </label>
        <input
          type="text"
          value={config.spreadsheet_id}
          onChange={(e) => updateConfig('spreadsheet_id', e.target.value)}
          placeholder="1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
          className="input w-full"
          required
        />
        <p className="text-xs text-neutral-500 mt-1">
          The ID of the Google Spreadsheet (found in the URL)
        </p>
      </div>
    </div>
  )
}
