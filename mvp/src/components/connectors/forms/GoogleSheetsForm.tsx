'use client'
import { useState, useEffect } from 'react'

interface GoogleSheetsFormProps {
  configuration: any
  onChange: (config: any) => void
}

// Google Sheets Source configuration class
class GoogleSheetsSourceConfig {
  batch_size: number = 200
  spreadsheet_id: string = ''
  credentials: {
    auth_type: 'Service' | 'Client'
    client_id?: string
    client_secret?: string
    refresh_token?: string
    service_account_info?: string
  } = { auth_type: 'Service' }
  sourceType: string = 'google-sheets'
}

export default function GoogleSheetsForm({ configuration, onChange }: GoogleSheetsFormProps) {
  const [config, setConfig] = useState(() => {
    const sourceConfig = new GoogleSheetsSourceConfig()
    return { ...sourceConfig, ...configuration }
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      [field]: value
    }))
  }

  const updateCredentials = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      credentials: {
        ...prev.credentials,
        [field]: value
      }
    }))
  }

  return (
    <div className="space-y-6">
      {/* Spreadsheet ID */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Spreadsheet ID *
        </label>
        <input
          type="text"
          value={config.spreadsheet_id}
          onChange={(e) => updateConfig('spreadsheet_id', e.target.value)}
          placeholder="1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
          className="input w-full"
          required
        />
        <p className="text-xs text-neutral-500 mt-1">
          The ID of the Google Spreadsheet (found in the URL)
        </p>
      </div>

      {/* Batch Size */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Batch Size
        </label>
        <input
          type="number"
          value={config.batch_size}
          onChange={(e) => updateConfig('batch_size', parseInt(e.target.value))}
          placeholder="200"
          className="input w-full"
        />
        <p className="text-xs text-neutral-500 mt-1">
          Number of rows to fetch in each batch (default: 200)
        </p>
      </div>

      {/* Credentials */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-neutral-900">Credentials *</h3>

        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Authentication Type
          </label>
          <select
            value={config.credentials.auth_type}
            onChange={(e) => updateCredentials('auth_type', e.target.value)}
            className="input w-full"
          >
            <option value="Service">Service Account</option>
            <option value="Client">OAuth Client</option>
          </select>
        </div>

        {config.credentials.auth_type === 'Service' && (
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Service Account Info *
            </label>
            <textarea
              value={config.credentials.service_account_info || ''}
              onChange={(e) => updateCredentials('service_account_info', e.target.value)}
              placeholder={`{
  "type": "service_account",
  "project_id": "your-project-id",
  "private_key_id": "...",
  "private_key": "-----BEGIN PRIVATE KEY-----\\n...\\n-----END PRIVATE KEY-----\\n",
  "client_email": "<EMAIL>",
  "client_id": "...",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token"
}`}
              className="input w-full h-48 font-mono text-sm"
              required
            />
            <p className="text-xs text-neutral-500 mt-1">
              The JSON key of the service account to use for authorization
            </p>
          </div>
        )}

        {config.credentials.auth_type === 'Client' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Client ID *
              </label>
              <input
                type="text"
                value={config.credentials.client_id || ''}
                onChange={(e) => updateCredentials('client_id', e.target.value)}
                placeholder="your-client-id.googleusercontent.com"
                className="input w-full"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Client Secret *
              </label>
              <input
                type="password"
                value={config.credentials.client_secret || ''}
                onChange={(e) => updateCredentials('client_secret', e.target.value)}
                placeholder="••••••••"
                className="input w-full"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Refresh Token *
              </label>
              <input
                type="text"
                value={config.credentials.refresh_token || ''}
                onChange={(e) => updateCredentials('refresh_token', e.target.value)}
                placeholder="1//04..."
                className="input w-full"
                required
              />
            </div>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">Setup Instructions:</h4>
        {config.credentials.auth_type === 'Service' ? (
          <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
            <li>Go to the Google Cloud Console</li>
            <li>Create or select a project</li>
            <li>Enable the Google Sheets API</li>
            <li>Create a service account</li>
            <li>Download the service account key as JSON</li>
            <li>Share your spreadsheet with the service account email</li>
            <li>Copy and paste the JSON content above</li>
          </ol>
        ) : (
          <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
            <li>Go to the Google Cloud Console</li>
            <li>Create or select a project</li>
            <li>Enable the Google Sheets API</li>
            <li>Create OAuth 2.0 credentials</li>
            <li>Use the OAuth playground to get a refresh token</li>
            <li>Enter the client ID, secret, and refresh token above</li>
          </ol>
        )}
      </div>
    </div>
  )
}
