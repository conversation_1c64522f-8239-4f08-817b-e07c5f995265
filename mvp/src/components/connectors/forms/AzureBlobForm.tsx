'use client'
import { useState, useEffect } from 'react'

interface AzureBlobFormProps {
  configuration: any
  onChange: (config: any) => void
  type: 'source' | 'destination'
}

export default function AzureBlobForm({ configuration, onChange, type }: AzureBlobFormProps) {
  const [config, setConfig] = useState({
    azure_blob_storage_account_name: '',
    azure_blob_storage_container_name: '',
    sourceType: type === 'source' ? 'azure-blob-storage' : undefined,
    destinationType: type === 'destination' ? 'azure-blob-storage' : undefined,
    ...configuration
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="space-y-6">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <p className="text-sm text-yellow-800">
          Azure Blob Storage configuration form is under development. Basic fields are shown below.
        </p>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Storage Account Name *
        </label>
        <input
          type="text"
          value={config.azure_blob_storage_account_name}
          onChange={(e) => updateConfig('azure_blob_storage_account_name', e.target.value)}
          placeholder="mystorageaccount"
          className="input w-full"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Container Name *
        </label>
        <input
          type="text"
          value={config.azure_blob_storage_container_name}
          onChange={(e) => updateConfig('azure_blob_storage_container_name', e.target.value)}
          placeholder="mycontainer"
          className="input w-full"
          required
        />
      </div>
    </div>
  )
}
