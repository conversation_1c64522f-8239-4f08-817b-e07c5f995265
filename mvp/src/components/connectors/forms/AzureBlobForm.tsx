'use client'
import { useState, useEffect } from 'react'

interface AzureBlobFormProps {
  configuration: any
  onChange: (config: any) => void
  type: 'source' | 'destination'
}

// Source configuration class
class AzureBlobSourceConfig {
  streams: Array<{
    name: string
    format: {
      filetype: 'avro' | 'csv' | 'jsonl' | 'parquet'
      delimiter?: string
      quote_char?: string
      escape_char?: string
      encoding?: string
      double_quote?: boolean
      null_values?: string
      string_can_be_null?: boolean
      skip_rows_before_header?: number
      skip_rows_after_header?: number
      true_values?: string
      false_values?: string
    }
  }> = []
  credentials: {
    auth_type: 'oauth2' | 'storage_account_key'
    tenant_id?: string
    client_id?: string
    client_secret?: string
    refresh_token?: string
    azure_blob_storage_account_key?: string
  } = { auth_type: 'storage_account_key' }
  azure_blob_storage_account_name: string = ''
  azure_blob_storage_container_name: string = ''
  azure_blob_storage_endpoint?: string = ''
  sourceType: string = 'azure-blob-storage'
}

// Destination configuration class
class AzureBlobDestinationConfig {
  azure_blob_storage_endpoint_domain_name?: string = ''
  azure_blob_storage_container_name: string = ''
  azure_blob_storage_account_name: string = ''
  azure_blob_storage_account_key: string = ''
  azure_blob_storage_output_buffer_size: number = 5
  azure_blob_storage_spill_size: number = 500
  format: {
    format_type: 'CSV' | 'JSONL'
    flattening: 'Root level flattening' | 'No Flattening'
  } = { format_type: 'JSONL', flattening: 'No Flattening' }
  destinationType: string = 'azure-blob-storage'
}

export default function AzureBlobForm({ configuration, onChange, type }: AzureBlobFormProps) {
  const [config, setConfig] = useState(() => {
    if (type === 'source') {
      const sourceConfig = new AzureBlobSourceConfig()
      return { ...sourceConfig, ...configuration }
    } else {
      const destConfig = new AzureBlobDestinationConfig()
      return { ...destConfig, ...configuration }
    }
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const updateNestedConfig = (parent: string, field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value
      }
    }))
  }

  const addStream = () => {
    if (type === 'source') {
      setConfig(prev => ({
        ...prev,
        streams: [
          ...prev.streams,
          {
            name: '',
            format: {
              filetype: 'csv',
              delimiter: ',',
              quote_char: '"',
              escape_char: '\\',
              encoding: 'utf8',
              double_quote: true,
              null_values: '',
              string_can_be_null: true,
              skip_rows_before_header: 0,
              skip_rows_after_header: 0,
              true_values: 'y,yes,t,true,on,1',
              false_values: 'n,no,f,false,off,0'
            }
          }
        ]
      }))
    }
  }

  const removeStream = (index: number) => {
    if (type === 'source') {
      setConfig(prev => ({
        ...prev,
        streams: prev.streams.filter((_, i) => i !== index)
      }))
    }
  }

  const updateStream = (index: number, field: string, value: any) => {
    if (type === 'source') {
      setConfig(prev => ({
        ...prev,
        streams: prev.streams.map((stream, i) =>
          i === index ? { ...stream, [field]: value } : stream
        )
      }))
    }
  }

  const updateStreamFormat = (index: number, field: string, value: any) => {
    if (type === 'source') {
      setConfig(prev => ({
        ...prev,
        streams: prev.streams.map((stream, i) =>
          i === index ? {
            ...stream,
            format: { ...stream.format, [field]: value }
          } : stream
        )
      }))
    }
  }

  return (
    <div className="space-y-6">
      {/* Storage Account Name */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Storage Account Name *
        </label>
        <input
          type="text"
          value={config.azure_blob_storage_account_name}
          onChange={(e) => updateConfig('azure_blob_storage_account_name', e.target.value)}
          placeholder="mystorageaccount"
          className="input w-full"
          required
        />
      </div>

      {/* Container Name */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Container Name *
        </label>
        <input
          type="text"
          value={config.azure_blob_storage_container_name}
          onChange={(e) => updateConfig('azure_blob_storage_container_name', e.target.value)}
          placeholder="mycontainer"
          className="input w-full"
          required
        />
      </div>

      {/* Source-specific fields */}
      {type === 'source' && (
        <>
          {/* Endpoint */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Endpoint
            </label>
            <input
              type="text"
              value={config.azure_blob_storage_endpoint || ''}
              onChange={(e) => updateConfig('azure_blob_storage_endpoint', e.target.value)}
              placeholder="https://mystorageaccount.blob.core.windows.net"
              className="input w-full"
            />
          </div>

          {/* Credentials */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-neutral-900">Credentials *</h3>

            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Authentication Type
              </label>
              <select
                value={config.credentials.auth_type}
                onChange={(e) => updateNestedConfig('credentials', 'auth_type', e.target.value)}
                className="input w-full"
              >
                <option value="storage_account_key">Storage Account Key</option>
                <option value="oauth2">OAuth2</option>
              </select>
            </div>

            {config.credentials.auth_type === 'storage_account_key' && (
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  Storage Account Key *
                </label>
                <input
                  type="password"
                  value={config.credentials.azure_blob_storage_account_key || ''}
                  onChange={(e) => updateNestedConfig('credentials', 'azure_blob_storage_account_key', e.target.value)}
                  placeholder="••••••••"
                  className="input w-full"
                  required
                />
              </div>
            )}

            {config.credentials.auth_type === 'oauth2' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Tenant ID *
                  </label>
                  <input
                    type="text"
                    value={config.credentials.tenant_id || ''}
                    onChange={(e) => updateNestedConfig('credentials', 'tenant_id', e.target.value)}
                    placeholder="tenant-id"
                    className="input w-full"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Client ID *
                  </label>
                  <input
                    type="text"
                    value={config.credentials.client_id || ''}
                    onChange={(e) => updateNestedConfig('credentials', 'client_id', e.target.value)}
                    placeholder="client-id"
                    className="input w-full"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Client Secret *
                  </label>
                  <input
                    type="password"
                    value={config.credentials.client_secret || ''}
                    onChange={(e) => updateNestedConfig('credentials', 'client_secret', e.target.value)}
                    placeholder="••••••••"
                    className="input w-full"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Refresh Token *
                  </label>
                  <input
                    type="text"
                    value={config.credentials.refresh_token || ''}
                    onChange={(e) => updateNestedConfig('credentials', 'refresh_token', e.target.value)}
                    placeholder="refresh-token"
                    className="input w-full"
                    required
                  />
                </div>
              </div>
            )}
          </div>

          {/* Streams */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-neutral-900">Streams *</h3>
              <button
                type="button"
                onClick={addStream}
                className="btn-secondary text-sm"
              >
                Add Stream
              </button>
            </div>

            {config.streams.map((stream, index) => (
              <div key={index} className="border border-neutral-200 rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-neutral-900">Stream {index + 1}</h4>
                  <button
                    type="button"
                    onClick={() => removeStream(index)}
                    className="text-red-600 hover:text-red-700 text-sm"
                  >
                    Remove
                  </button>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Stream Name *
                  </label>
                  <input
                    type="text"
                    value={stream.name}
                    onChange={(e) => updateStream(index, 'name', e.target.value)}
                    placeholder="my-stream"
                    className="input w-full"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    File Type *
                  </label>
                  <select
                    value={stream.format.filetype}
                    onChange={(e) => updateStreamFormat(index, 'filetype', e.target.value)}
                    className="input w-full"
                  >
                    <option value="csv">CSV</option>
                    <option value="avro">Avro</option>
                    <option value="jsonl">JSONL</option>
                    <option value="parquet">Parquet</option>
                  </select>
                </div>

                {stream.format.filetype === 'csv' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Delimiter
                      </label>
                      <input
                        type="text"
                        value={stream.format.delimiter || ','}
                        onChange={(e) => updateStreamFormat(index, 'delimiter', e.target.value)}
                        placeholder=","
                        className="input w-full"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Quote Character
                      </label>
                      <input
                        type="text"
                        value={stream.format.quote_char || '"'}
                        onChange={(e) => updateStreamFormat(index, 'quote_char', e.target.value)}
                        placeholder='"'
                        className="input w-full"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Escape Character
                      </label>
                      <input
                        type="text"
                        value={stream.format.escape_char || '\\'}
                        onChange={(e) => updateStreamFormat(index, 'escape_char', e.target.value)}
                        placeholder="\"
                        className="input w-full"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Encoding
                      </label>
                      <input
                        type="text"
                        value={stream.format.encoding || 'utf8'}
                        onChange={(e) => updateStreamFormat(index, 'encoding', e.target.value)}
                        placeholder="utf8"
                        className="input w-full"
                      />
                    </div>
                  </div>
                )}
              </div>
            ))}

            {config.streams.length === 0 && (
              <div className="text-center py-4 text-neutral-500">
                No streams configured. Click "Add Stream" to get started.
              </div>
            )}
          </div>
        </>
      )}

      {/* Destination-specific fields */}
      {type === 'destination' && (
        <>
          {/* Endpoint Domain Name */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Endpoint Domain Name
            </label>
            <input
              type="text"
              value={config.azure_blob_storage_endpoint_domain_name || ''}
              onChange={(e) => updateConfig('azure_blob_storage_endpoint_domain_name', e.target.value)}
              placeholder="blob.core.windows.net"
              className="input w-full"
            />
          </div>

          {/* Account Key */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Account Key *
            </label>
            <input
              type="password"
              value={config.azure_blob_storage_account_key}
              onChange={(e) => updateConfig('azure_blob_storage_account_key', e.target.value)}
              placeholder="••••••••"
              className="input w-full"
              required
            />
          </div>

          {/* Buffer and Spill Size */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Output Buffer Size
              </label>
              <input
                type="number"
                value={config.azure_blob_storage_output_buffer_size}
                onChange={(e) => updateConfig('azure_blob_storage_output_buffer_size', parseInt(e.target.value))}
                placeholder="5"
                className="input w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Spill Size
              </label>
              <input
                type="number"
                value={config.azure_blob_storage_spill_size}
                onChange={(e) => updateConfig('azure_blob_storage_spill_size', parseInt(e.target.value))}
                placeholder="500"
                className="input w-full"
              />
            </div>
          </div>

          {/* Format */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-neutral-900">Output Format</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  Format Type
                </label>
                <select
                  value={config.format.format_type}
                  onChange={(e) => updateNestedConfig('format', 'format_type', e.target.value)}
                  className="input w-full"
                >
                  <option value="JSONL">JSONL</option>
                  <option value="CSV">CSV</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  Flattening
                </label>
                <select
                  value={config.format.flattening}
                  onChange={(e) => updateNestedConfig('format', 'flattening', e.target.value)}
                  className="input w-full"
                >
                  <option value="No Flattening">No Flattening</option>
                  <option value="Root level flattening">Root level flattening</option>
                </select>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
