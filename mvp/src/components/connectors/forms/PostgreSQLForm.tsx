'use client'
import { useConnectorConfig } from './useConnectorConfig'

interface PostgreSQLFormProps {
  configuration: any
  onChange: (config: any) => void
}

// PostgreSQL Source configuration class
class PostgreSQLSourceConfig {
  host: string = ''
  port: number = 5432
  database: string = ''
  schema: string = 'public'
  username: string = ''
  password: string = ''
  jdbc_url_params?: string = ''
  ssl_mode: {
    mode: 'disable' | 'allow' | 'prefer' | 'require' | 'verify-ca' | 'verify-full'
    ca_certificate?: string
    client_certificate?: string
    client_key?: string
    client_key_password?: string
  } = { mode: 'prefer' }
  tunnel_method: {
    tunnel_method: 'NO_TUNNEL' | 'SSH_KEY_AUTH' | 'PASSWORD_AUTH'
    tunnel_host?: string
    tunnel_port?: number
    tunnel_user?: string
    ssh_key?: string
    tunnel_user_password?: string
  } = { tunnel_method: 'NO_TUNNEL' }
  sourceType: string = 'postgres'
}

export default function PostgreSQLForm({ configuration, onChange }: PostgreSQLFormProps) {
  const defaultConfig = new PostgreSQLSourceConfig()
  const { config, updateConfig, updateNestedConfig } = useConnectorConfig(
    defaultConfig,
    configuration,
    onChange
  )

  return (
    <div className="space-y-6">
      {/* Connection Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Host *
          </label>
          <input
            type="text"
            value={config.host}
            onChange={(e) => updateConfig('host', e.target.value)}
            placeholder="localhost"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Port *
          </label>
          <input
            type="number"
            value={config.port}
            onChange={(e) => updateConfig('port', parseInt(e.target.value))}
            placeholder="5432"
            className="input w-full"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Database *
          </label>
          <input
            type="text"
            value={config.database}
            onChange={(e) => updateConfig('database', e.target.value)}
            placeholder="my_database"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Schema
          </label>
          <input
            type="text"
            value={config.schema}
            onChange={(e) => updateConfig('schema', e.target.value)}
            placeholder="public"
            className="input w-full"
          />
        </div>
      </div>

      {/* Authentication */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Username *
          </label>
          <input
            type="text"
            value={config.username}
            onChange={(e) => updateConfig('username', e.target.value)}
            placeholder="postgres"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Password *
          </label>
          <input
            type="password"
            value={config.password}
            onChange={(e) => updateConfig('password', e.target.value)}
            placeholder="••••••••"
            className="input w-full"
            required
          />
        </div>
      </div>

      {/* SSL Configuration */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          SSL Mode
        </label>
        <select
          value={config.ssl_mode.mode}
          onChange={(e) => updateNestedConfig('ssl_mode', 'mode', e.target.value)}
          className="input w-full"
        >
          <option value="disable">Disable</option>
          <option value="allow">Allow</option>
          <option value="prefer">Prefer</option>
          <option value="require">Require</option>
          <option value="verify-ca">Verify CA</option>
          <option value="verify-full">Verify Full</option>
        </select>
      </div>

      {/* SSL Certificates (only show for verify-ca and verify-full) */}
      {(config.ssl_mode.mode === 'verify-ca' || config.ssl_mode.mode === 'verify-full') && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              CA Certificate
            </label>
            <textarea
              value={config.ssl_mode.ca_certificate || ''}
              onChange={(e) => updateNestedConfig('ssl_mode', 'ca_certificate', e.target.value)}
              placeholder="-----BEGIN CERTIFICATE-----"
              className="input w-full h-24"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Client Certificate
            </label>
            <textarea
              value={config.ssl_mode.client_certificate || ''}
              onChange={(e) => updateNestedConfig('ssl_mode', 'client_certificate', e.target.value)}
              placeholder="-----BEGIN CERTIFICATE-----"
              className="input w-full h-24"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Client Key
            </label>
            <textarea
              value={config.ssl_mode.client_key || ''}
              onChange={(e) => updateNestedConfig('ssl_mode', 'client_key', e.target.value)}
              placeholder="-----BEGIN PRIVATE KEY-----"
              className="input w-full h-24"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Client Key Password
            </label>
            <input
              type="password"
              value={config.ssl_mode.client_key_password || ''}
              onChange={(e) => updateNestedConfig('ssl_mode', 'client_key_password', e.target.value)}
              placeholder="••••••••"
              className="input w-full"
            />
          </div>
        </div>
      )}

      {/* Tunnel Method */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Tunnel Method
        </label>
        <select
          value={config.tunnel_method.tunnel_method}
          onChange={(e) => updateNestedConfig('tunnel_method', 'tunnel_method', e.target.value)}
          className="input w-full"
        >
          <option value="NO_TUNNEL">No Tunnel</option>
          <option value="SSH_KEY_AUTH">SSH Key Authentication</option>
          <option value="PASSWORD_AUTH">SSH Password Authentication</option>
        </select>
      </div>

      {/* Tunnel Configuration */}
      {config.tunnel_method.tunnel_method !== 'NO_TUNNEL' && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Tunnel Host
              </label>
              <input
                type="text"
                value={config.tunnel_method.tunnel_host || ''}
                onChange={(e) => updateNestedConfig('tunnel_method', 'tunnel_host', e.target.value)}
                placeholder="tunnel.example.com"
                className="input w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Tunnel Port
              </label>
              <input
                type="number"
                value={config.tunnel_method.tunnel_port || 22}
                onChange={(e) => updateNestedConfig('tunnel_method', 'tunnel_port', parseInt(e.target.value))}
                placeholder="22"
                className="input w-full"
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Tunnel User
            </label>
            <input
              type="text"
              value={config.tunnel_method.tunnel_user || ''}
              onChange={(e) => updateNestedConfig('tunnel_method', 'tunnel_user', e.target.value)}
              placeholder="tunnel_user"
              className="input w-full"
            />
          </div>
          
          {config.tunnel_method.tunnel_method === 'SSH_KEY_AUTH' && (
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                SSH Private Key
              </label>
              <textarea
                value={config.tunnel_method.ssh_key || ''}
                onChange={(e) => updateNestedConfig('tunnel_method', 'ssh_key', e.target.value)}
                placeholder="-----BEGIN PRIVATE KEY-----"
                className="input w-full h-24"
              />
            </div>
          )}
          
          {config.tunnel_method.tunnel_method === 'PASSWORD_AUTH' && (
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Tunnel Password
              </label>
              <input
                type="password"
                value={config.tunnel_method.tunnel_user_password || ''}
                onChange={(e) => updateNestedConfig('tunnel_method', 'tunnel_user_password', e.target.value)}
                placeholder="••••••••"
                className="input w-full"
              />
            </div>
          )}
        </div>
      )}

      {/* Advanced Options */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          JDBC URL Parameters
        </label>
        <input
          type="text"
          value={config.jdbc_url_params}
          onChange={(e) => updateConfig('jdbc_url_params', e.target.value)}
          placeholder="key1=value1&key2=value2"
          className="input w-full"
        />
        <p className="text-xs text-neutral-500 mt-1">
          Additional properties formatted as 'key=value' pairs separated by '&'
        </p>
      </div>
    </div>
  )
}
