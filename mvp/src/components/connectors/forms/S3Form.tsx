'use client'
import { useState, useEffect } from 'react'

interface S3FormProps {
  configuration: any
  onChange: (config: any) => void
}

// S3 Destination configuration class
class S3DestinationConfig {
  access_key_id?: string = ''
  secret_access_key?: string = ''
  s3_bucket_name: string = ''
  s3_bucket_path: string = ''
  s3_bucket_region: string = ''
  format: {
    format_type: 'Avro' | 'CSV' | 'JSONL' | 'Parquet'
    compression_codec?: {
      codec: 'No compression' | 'Deflate' | 'bzip2' | 'xz' | 'zstandard' | 'snappy' | 'GZIP'
      compression_level?: number
      include_checksum?: boolean
    }
  } = { format_type: 'JSONL' }
  s3_endpoint?: string = ''
  s3_path_format?: string = ''
  file_name_pattern?: string = ''
  destinationType: string = 's3'
}

export default function S3Form({ configuration, onChange }: S3FormProps) {
  const [config, setConfig] = useState(() => {
    const destConfig = new S3DestinationConfig()
    return { ...destConfig, ...configuration }
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      [field]: value
    }))
  }

  const updateFormat = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      format: {
        ...prev.format,
        [field]: value
      }
    }))
  }

  const updateCompressionCodec = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      format: {
        ...prev.format,
        compression_codec: {
          ...prev.format.compression_codec,
          [field]: value
        }
      }
    }))
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Access Key ID
          </label>
          <input
            type="text"
            value={config.access_key_id}
            onChange={(e) => updateConfig('access_key_id', e.target.value)}
            placeholder="AKIAIOSFODNN7EXAMPLE"
            className="input w-full"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Secret Access Key
          </label>
          <input
            type="password"
            value={config.secret_access_key}
            onChange={(e) => updateConfig('secret_access_key', e.target.value)}
            placeholder="••••••••"
            className="input w-full"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          S3 Bucket Name *
        </label>
        <input
          type="text"
          value={config.s3_bucket_name}
          onChange={(e) => updateConfig('s3_bucket_name', e.target.value)}
          placeholder="my-s3-bucket"
          className="input w-full"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            S3 Bucket Path *
          </label>
          <input
            type="text"
            value={config.s3_bucket_path}
            onChange={(e) => updateConfig('s3_bucket_path', e.target.value)}
            placeholder="data/"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            S3 Bucket Region *
          </label>
          <select
            value={config.s3_bucket_region}
            onChange={(e) => updateConfig('s3_bucket_region', e.target.value)}
            className="input w-full"
            required
          >
            <option value="">Select region</option>
            <option value="us-east-1">us-east-1</option>
            <option value="us-east-2">us-east-2</option>
            <option value="us-west-1">us-west-1</option>
            <option value="us-west-2">us-west-2</option>
            <option value="eu-west-1">eu-west-1</option>
            <option value="eu-central-1">eu-central-1</option>
            <option value="ap-southeast-1">ap-southeast-1</option>
            <option value="ap-northeast-1">ap-northeast-1</option>
          </select>
        </div>
      </div>

      {/* Output Format */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-neutral-900">Output Format *</h3>

        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Format Type *
          </label>
          <select
            value={config.format.format_type}
            onChange={(e) => updateFormat('format_type', e.target.value)}
            className="input w-full"
            required
          >
            <option value="JSONL">JSONL</option>
            <option value="Avro">Avro</option>
            <option value="CSV">CSV</option>
            <option value="Parquet">Parquet</option>
          </select>
        </div>

        {/* Compression Codec */}
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Compression Codec
          </label>
          <select
            value={config.format.compression_codec?.codec || 'No compression'}
            onChange={(e) => updateCompressionCodec('codec', e.target.value)}
            className="input w-full"
          >
            <option value="No compression">No compression</option>
            <option value="GZIP">GZIP</option>
            <option value="Deflate">Deflate</option>
            <option value="bzip2">bzip2</option>
            <option value="xz">xz</option>
            <option value="zstandard">zstandard</option>
            <option value="snappy">snappy</option>
          </select>
        </div>

        {config.format.compression_codec?.codec && config.format.compression_codec.codec !== 'No compression' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Compression Level
              </label>
              <input
                type="number"
                value={config.format.compression_codec?.compression_level || ''}
                onChange={(e) => updateCompressionCodec('compression_level', parseInt(e.target.value))}
                placeholder="6"
                className="input w-full"
              />
            </div>
            <div className="flex items-center space-x-2 pt-8">
              <input
                type="checkbox"
                id="include_checksum"
                checked={config.format.compression_codec?.include_checksum || false}
                onChange={(e) => updateCompressionCodec('include_checksum', e.target.checked)}
                className="rounded border-neutral-300"
              />
              <label htmlFor="include_checksum" className="text-sm text-neutral-700">
                Include Checksum
              </label>
            </div>
          </div>
        )}
      </div>

      {/* Advanced Options */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-neutral-900">Advanced Options</h3>

        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            S3 Endpoint
          </label>
          <input
            type="text"
            value={config.s3_endpoint}
            onChange={(e) => updateConfig('s3_endpoint', e.target.value)}
            placeholder="https://s3.amazonaws.com"
            className="input w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            S3 Path Format
          </label>
          <input
            type="text"
            value={config.s3_path_format}
            onChange={(e) => updateConfig('s3_path_format', e.target.value)}
            placeholder="${NAMESPACE}/${STREAM_NAME}/${YEAR}_${MONTH}_${DAY}_${EPOCH}_"
            className="input w-full"
          />
          <p className="text-xs text-neutral-500 mt-1">
            {"Use variables like ${NAMESPACE}, ${STREAM_NAME}, ${YEAR}, ${MONTH}, ${DAY}, ${EPOCH}"}
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            File Name Pattern
          </label>
          <input
            type="text"
            value={config.file_name_pattern}
            onChange={(e) => updateConfig('file_name_pattern', e.target.value)}
            placeholder="{date:yyyy_MM}"
            className="input w-full"
          />
          <p className="text-xs text-neutral-500 mt-1">
            {"Use date patterns like {date:yyyy_MM}"}
          </p>
        </div>
      </div>
    </div>
  )
}
