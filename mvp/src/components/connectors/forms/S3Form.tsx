'use client'
import { useState, useEffect } from 'react'

interface S3FormProps {
  configuration: any
  onChange: (config: any) => void
}

export default function S3Form({ configuration, onChange }: S3FormProps) {
  const [config, setConfig] = useState({
    access_key_id: '',
    secret_access_key: '',
    s3_bucket_name: '',
    s3_bucket_path: '',
    s3_bucket_region: '',
    format: {
      format_type: 'JSONL'
    },
    destinationType: 's3',
    ...configuration
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const updateFormat = (field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      format: {
        ...prev.format,
        [field]: value
      }
    }))
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Access Key ID
          </label>
          <input
            type="text"
            value={config.access_key_id}
            onChange={(e) => updateConfig('access_key_id', e.target.value)}
            placeholder="AKIAIOSFODNN7EXAMPLE"
            className="input w-full"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Secret Access Key
          </label>
          <input
            type="password"
            value={config.secret_access_key}
            onChange={(e) => updateConfig('secret_access_key', e.target.value)}
            placeholder="••••••••"
            className="input w-full"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          S3 Bucket Name *
        </label>
        <input
          type="text"
          value={config.s3_bucket_name}
          onChange={(e) => updateConfig('s3_bucket_name', e.target.value)}
          placeholder="my-s3-bucket"
          className="input w-full"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            S3 Bucket Path *
          </label>
          <input
            type="text"
            value={config.s3_bucket_path}
            onChange={(e) => updateConfig('s3_bucket_path', e.target.value)}
            placeholder="data/"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            S3 Bucket Region *
          </label>
          <select
            value={config.s3_bucket_region}
            onChange={(e) => updateConfig('s3_bucket_region', e.target.value)}
            className="input w-full"
            required
          >
            <option value="">Select region</option>
            <option value="us-east-1">us-east-1</option>
            <option value="us-east-2">us-east-2</option>
            <option value="us-west-1">us-west-1</option>
            <option value="us-west-2">us-west-2</option>
            <option value="eu-west-1">eu-west-1</option>
            <option value="eu-central-1">eu-central-1</option>
            <option value="ap-southeast-1">ap-southeast-1</option>
            <option value="ap-northeast-1">ap-northeast-1</option>
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Output Format *
        </label>
        <select
          value={config.format.format_type}
          onChange={(e) => updateFormat('format_type', e.target.value)}
          className="input w-full"
          required
        >
          <option value="JSONL">JSONL</option>
          <option value="Avro">Avro</option>
          <option value="CSV">CSV</option>
          <option value="Parquet">Parquet</option>
        </select>
      </div>
    </div>
  )
}
