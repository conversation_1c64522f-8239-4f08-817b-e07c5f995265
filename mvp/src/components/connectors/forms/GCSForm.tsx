'use client'
import { useState, useEffect } from 'react'

interface GCSFormProps {
  configuration: any
  onChange: (config: any) => void
  type: 'source' | 'destination'
}

// GCS Source configuration class
class GCSSourceConfig {
  streams: Array<{
    name: string
    globs?: string[]
    validation_policy?: 'Emit Record' | 'Skip Record' | 'Wait for Discover'
    input_schema?: string
    format: {
      filetype: 'avro' | 'csv' | 'jsonl' | 'parquet'
      delimiter?: string
      quote_char?: string
      escape_char?: string
      encoding?: string
      double_quote?: boolean
      null_values?: string
      string_can_be_null?: boolean
      skip_rows_before_header?: number
      skip_rows_after_header?: number
      true_values?: string
      false_values?: string
    }
  }> = []
  service_account: string = ''
  bucket: string = ''
  sourceType: string = 'gcs'
}

// GCS Destination configuration class
class GCSDestinationConfig {
  gcs_bucket_name: string = ''
  gcs_bucket_path: string = ''
  gcs_bucket_region: string = 'us'
  credential: {
    credential_type: string
    hmac_key_access_id: string
    hmac_key_secret: string
  } = { credential_type: 'HMAC_KEY', hmac_key_access_id: '', hmac_key_secret: '' }
  format: {
    format_type: 'Avro' | 'CSV' | 'JSONL' | 'Parquet'
    compression_codec?: {
      codec: 'No compression' | 'Deflate' | 'bzip2' | 'xz' | 'zstandard' | 'snappy' | 'GZIP'
      compression_level?: number
      include_checksum?: boolean
    }
  } = { format_type: 'JSONL' }
  destinationType: string = 'gcs'
}

export default function GCSForm({ configuration, onChange, type }: GCSFormProps) {
  const [config, setConfig] = useState(() => {
    if (type === 'source') {
      const sourceConfig = new GCSSourceConfig()
      return { ...sourceConfig, ...configuration }
    } else {
      const destConfig = new GCSDestinationConfig()
      return { ...destConfig, ...configuration }
    }
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      [field]: value
    }))
  }

  const updateNestedConfig = (parent: string, field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value
      }
    }))
  }

  const addStream = () => {
    if (type === 'source') {
      setConfig((prev: any) => ({
        ...prev,
        streams: [
          ...prev.streams,
          {
            name: '',
            globs: [],
            validation_policy: 'Emit Record',
            input_schema: '',
            format: {
              filetype: 'csv',
              delimiter: ',',
              quote_char: '"',
              escape_char: '\\',
              encoding: 'utf8',
              double_quote: true,
              null_values: '',
              string_can_be_null: true,
              skip_rows_before_header: 0,
              skip_rows_after_header: 0,
              true_values: 'y,yes,t,true,on,1',
              false_values: 'n,no,f,false,off,0'
            }
          }
        ]
      }))
    }
  }

  const removeStream = (index: number) => {
    if (type === 'source') {
      setConfig((prev: any) => ({
        ...prev,
        streams: prev.streams.filter((_: any, i: number) => i !== index)
      }))
    }
  }

  const updateStream = (index: number, field: string, value: any) => {
    if (type === 'source') {
      setConfig((prev: any) => ({
        ...prev,
        streams: prev.streams.map((stream: any, i: number) =>
          i === index ? { ...stream, [field]: value } : stream
        )
      }))
    }
  }

  const updateStreamFormat = (index: number, field: string, value: any) => {
    if (type === 'source') {
      setConfig((prev: any) => ({
        ...prev,
        streams: prev.streams.map((stream: any, i: number) =>
          i === index ? {
            ...stream,
            format: { ...stream.format, [field]: value }
          } : stream
        )
      }))
    }
  }

  const updateStreamGlobs = (index: number, globsString: string) => {
    if (type === 'source') {
      const globs = globsString.split(',').map(g => g.trim()).filter(g => g.length > 0)
      updateStream(index, 'globs', globs)
    }
  }

  return (
    <div className="space-y-6">
      {/* Bucket Name */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Bucket Name *
        </label>
        <input
          type="text"
          value={type === 'source' ? config.bucket : config.gcs_bucket_name}
          onChange={(e) => updateConfig(type === 'source' ? 'bucket' : 'gcs_bucket_name', e.target.value)}
          placeholder="my-gcs-bucket"
          className="input w-full"
          required
        />
      </div>

      {/* Source-specific fields */}
      {type === 'source' && (
        <>
          {/* Service Account */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Service Account Key JSON *
            </label>
            <textarea
              value={config.service_account}
              onChange={(e) => updateConfig('service_account', e.target.value)}
              placeholder={`{
  "type": "service_account",
  "project_id": "your-project-id",
  "private_key_id": "...",
  "private_key": "-----BEGIN PRIVATE KEY-----\\n...\\n-----END PRIVATE KEY-----\\n",
  "client_email": "<EMAIL>",
  "client_id": "...",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token"
}`}
              className="input w-full h-48 font-mono text-sm"
              required
            />
            <p className="text-xs text-neutral-500 mt-1">
              Enter your Google Cloud service account key in JSON format
            </p>
          </div>

          {/* Streams */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-neutral-900">Streams *</h3>
              <button
                type="button"
                onClick={addStream}
                className="btn-secondary text-sm"
              >
                Add Stream
              </button>
            </div>

            {config.streams.map((stream: any, index: number) => (
              <div key={index} className="border border-neutral-200 rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-neutral-900">Stream {index + 1}</h4>
                  <button
                    type="button"
                    onClick={() => removeStream(index)}
                    className="text-red-600 hover:text-red-700 text-sm"
                  >
                    Remove
                  </button>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Stream Name *
                  </label>
                  <input
                    type="text"
                    value={stream.name}
                    onChange={(e) => updateStream(index, 'name', e.target.value)}
                    placeholder="my-stream"
                    className="input w-full"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Globs (comma-separated)
                  </label>
                  <input
                    type="text"
                    value={stream.globs?.join(', ') || ''}
                    onChange={(e) => updateStreamGlobs(index, e.target.value)}
                    placeholder="*.csv, data/*.json"
                    className="input w-full"
                  />
                  <p className="text-xs text-neutral-500 mt-1">
                    File patterns to match, separated by commas
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Validation Policy
                  </label>
                  <select
                    value={stream.validation_policy || 'Emit Record'}
                    onChange={(e) => updateStream(index, 'validation_policy', e.target.value)}
                    className="input w-full"
                  >
                    <option value="Emit Record">Emit Record</option>
                    <option value="Skip Record">Skip Record</option>
                    <option value="Wait for Discover">Wait for Discover</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Input Schema
                  </label>
                  <textarea
                    value={stream.input_schema || ''}
                    onChange={(e) => updateStream(index, 'input_schema', e.target.value)}
                    placeholder="Optional JSON schema"
                    className="input w-full h-20"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    File Type *
                  </label>
                  <select
                    value={stream.format.filetype}
                    onChange={(e) => updateStreamFormat(index, 'filetype', e.target.value)}
                    className="input w-full"
                  >
                    <option value="csv">CSV</option>
                    <option value="avro">Avro</option>
                    <option value="jsonl">JSONL</option>
                    <option value="parquet">Parquet</option>
                  </select>
                </div>

                {stream.format.filetype === 'csv' && (
                  <div className="space-y-4">
                    <h5 className="font-medium text-neutral-900">CSV Format Options</h5>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          Delimiter
                        </label>
                        <input
                          type="text"
                          value={stream.format.delimiter || ','}
                          onChange={(e) => updateStreamFormat(index, 'delimiter', e.target.value)}
                          placeholder=","
                          className="input w-full"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          Quote Character
                        </label>
                        <input
                          type="text"
                          value={stream.format.quote_char || '"'}
                          onChange={(e) => updateStreamFormat(index, 'quote_char', e.target.value)}
                          placeholder='"'
                          className="input w-full"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          Escape Character
                        </label>
                        <input
                          type="text"
                          value={stream.format.escape_char || '\\'}
                          onChange={(e) => updateStreamFormat(index, 'escape_char', e.target.value)}
                          placeholder="\"
                          className="input w-full"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          Encoding
                        </label>
                        <input
                          type="text"
                          value={stream.format.encoding || 'utf8'}
                          onChange={(e) => updateStreamFormat(index, 'encoding', e.target.value)}
                          placeholder="utf8"
                          className="input w-full"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          Skip Rows Before Header
                        </label>
                        <input
                          type="number"
                          value={stream.format.skip_rows_before_header || 0}
                          onChange={(e) => updateStreamFormat(index, 'skip_rows_before_header', parseInt(e.target.value))}
                          placeholder="0"
                          className="input w-full"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          Skip Rows After Header
                        </label>
                        <input
                          type="number"
                          value={stream.format.skip_rows_after_header || 0}
                          onChange={(e) => updateStreamFormat(index, 'skip_rows_after_header', parseInt(e.target.value))}
                          placeholder="0"
                          className="input w-full"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          True Values
                        </label>
                        <input
                          type="text"
                          value={stream.format.true_values || 'y,yes,t,true,on,1'}
                          onChange={(e) => updateStreamFormat(index, 'true_values', e.target.value)}
                          placeholder="y,yes,t,true,on,1"
                          className="input w-full"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          False Values
                        </label>
                        <input
                          type="text"
                          value={stream.format.false_values || 'n,no,f,false,off,0'}
                          onChange={(e) => updateStreamFormat(index, 'false_values', e.target.value)}
                          placeholder="n,no,f,false,off,0"
                          className="input w-full"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 mb-2">
                          Null Values
                        </label>
                        <input
                          type="text"
                          value={stream.format.null_values || ''}
                          onChange={(e) => updateStreamFormat(index, 'null_values', e.target.value)}
                          placeholder="NULL,null,N/A"
                          className="input w-full"
                        />
                      </div>
                      <div className="flex items-center space-x-4 pt-8">
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={`double_quote_${index}`}
                            checked={stream.format.double_quote || false}
                            onChange={(e) => updateStreamFormat(index, 'double_quote', e.target.checked)}
                            className="rounded border-neutral-300"
                          />
                          <label htmlFor={`double_quote_${index}`} className="text-sm text-neutral-700">
                            Double Quote
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={`string_can_be_null_${index}`}
                            checked={stream.format.string_can_be_null || false}
                            onChange={(e) => updateStreamFormat(index, 'string_can_be_null', e.target.checked)}
                            className="rounded border-neutral-300"
                          />
                          <label htmlFor={`string_can_be_null_${index}`} className="text-sm text-neutral-700">
                            String Can Be Null
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}

            {config.streams.length === 0 && (
              <div className="text-center py-4 text-neutral-500">
                No streams configured. Click "Add Stream" to get started.
              </div>
            )}
          </div>
        </>
      )}

      {/* Destination-specific fields */}
      {type === 'destination' && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Bucket Path *
              </label>
              <input
                type="text"
                value={config.gcs_bucket_path}
                onChange={(e) => updateConfig('gcs_bucket_path', e.target.value)}
                placeholder="data/"
                className="input w-full"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Bucket Region
              </label>
              <input
                type="text"
                value={config.gcs_bucket_region}
                onChange={(e) => updateConfig('gcs_bucket_region', e.target.value)}
                placeholder="us"
                className="input w-full"
              />
            </div>
          </div>

          {/* Credentials */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-neutral-900">Credentials</h3>

            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Credential Type
              </label>
              <select
                value={config.credential.credential_type}
                onChange={(e) => updateNestedConfig('credential', 'credential_type', e.target.value)}
                className="input w-full"
              >
                <option value="HMAC_KEY">HMAC Key</option>
              </select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  HMAC Key Access ID *
                </label>
                <input
                  type="text"
                  value={config.credential.hmac_key_access_id}
                  onChange={(e) => updateNestedConfig('credential', 'hmac_key_access_id', e.target.value)}
                  placeholder="GOOG1234567890ABCDEF"
                  className="input w-full"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  HMAC Key Secret *
                </label>
                <input
                  type="password"
                  value={config.credential.hmac_key_secret}
                  onChange={(e) => updateNestedConfig('credential', 'hmac_key_secret', e.target.value)}
                  placeholder="••••••••"
                  className="input w-full"
                  required
                />
              </div>
            </div>
          </div>

          {/* Format */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-neutral-900">Output Format</h3>

            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Format Type
              </label>
              <select
                value={config.format.format_type}
                onChange={(e) => updateNestedConfig('format', 'format_type', e.target.value)}
                className="input w-full"
              >
                <option value="JSONL">JSONL</option>
                <option value="Avro">Avro</option>
                <option value="CSV">CSV</option>
                <option value="Parquet">Parquet</option>
              </select>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
