'use client'
import { useState, useEffect } from 'react'

interface GCSFormProps {
  configuration: any
  onChange: (config: any) => void
  type: 'source' | 'destination'
}

export default function GCSForm({ configuration, onChange, type }: GCSFormProps) {
  const [config, setConfig] = useState({
    bucket: type === 'source' ? '' : undefined,
    gcs_bucket_name: type === 'destination' ? '' : undefined,
    sourceType: type === 'source' ? 'gcs' : undefined,
    destinationType: type === 'destination' ? 'gcs' : undefined,
    ...configuration
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="space-y-6">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <p className="text-sm text-yellow-800">
          Google Cloud Storage configuration form is under development. Basic fields are shown below.
        </p>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Bucket Name *
        </label>
        <input
          type="text"
          value={type === 'source' ? config.bucket : config.gcs_bucket_name}
          onChange={(e) => updateConfig(type === 'source' ? 'bucket' : 'gcs_bucket_name', e.target.value)}
          placeholder="my-gcs-bucket"
          className="input w-full"
          required
        />
      </div>
    </div>
  )
}
