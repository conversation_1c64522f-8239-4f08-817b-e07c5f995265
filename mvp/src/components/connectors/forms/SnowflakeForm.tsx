'use client'
import { useState, useEffect } from 'react'

interface SnowflakeFormProps {
  configuration: any
  onChange: (config: any) => void
  type: 'source' | 'destination'
}

export default function SnowflakeForm({ configuration, onChange, type }: SnowflakeFormProps) {
  const [config, setConfig] = useState({
    host: '',
    role: '',
    warehouse: '',
    database: '',
    schema: '',
    jdbc_url_params: '',
    // Auth configuration
    auth_type: 'username/password',
    username: '',
    password: '',
    // OAuth fields
    client_id: '',
    client_secret: '',
    access_token: '',
    refresh_token: '',
    // Key pair fields
    private_key: '',
    private_key_password: '',
    // Destination specific
    credentials: type === 'destination' ? {
      auth_type: 'Username and Password',
      password: ''
    } : undefined,
    // Type identifier
    sourceType: type === 'source' ? 'snowflake' : undefined,
    destinationType: type === 'destination' ? 'snowflake' : undefined,
    ...configuration
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const updateCredentials = (field: string, value: any) => {
    if (type === 'destination') {
      setConfig(prev => ({
        ...prev,
        credentials: {
          ...prev.credentials,
          [field]: value
        }
      }))
    } else {
      updateConfig(field, value)
    }
  }

  const authType = type === 'destination' ? config.credentials?.auth_type : config.auth_type

  return (
    <div className="space-y-6">
      {/* Connection Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Host *
          </label>
          <input
            type="text"
            value={config.host}
            onChange={(e) => updateConfig('host', e.target.value)}
            placeholder="account.snowflakecomputing.com"
            className="input w-full"
            required
          />
          <p className="text-xs text-neutral-500 mt-1">
            Your Snowflake account URL
          </p>
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Role *
          </label>
          <input
            type="text"
            value={config.role}
            onChange={(e) => updateConfig('role', e.target.value)}
            placeholder="ACCOUNTADMIN"
            className="input w-full"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Warehouse *
          </label>
          <input
            type="text"
            value={config.warehouse}
            onChange={(e) => updateConfig('warehouse', e.target.value)}
            placeholder="COMPUTE_WH"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Database *
          </label>
          <input
            type="text"
            value={config.database}
            onChange={(e) => updateConfig('database', e.target.value)}
            placeholder="MY_DATABASE"
            className="input w-full"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Schema *
        </label>
        <input
          type="text"
          value={config.schema}
          onChange={(e) => updateConfig('schema', e.target.value)}
          placeholder="PUBLIC"
          className="input w-full"
          required
        />
      </div>

      {/* Authentication Method */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Authentication Method
        </label>
        <select
          value={authType}
          onChange={(e) => {
            if (type === 'destination') {
              updateCredentials('auth_type', e.target.value)
            } else {
              updateConfig('auth_type', e.target.value)
            }
          }}
          className="input w-full"
        >
          {type === 'source' ? (
            <>
              <option value="username/password">Username/Password</option>
              <option value="OAuth">OAuth</option>
              <option value="Key Pair Authentication">Key Pair Authentication</option>
            </>
          ) : (
            <>
              <option value="Username and Password">Username and Password</option>
              <option value="OAuth2.0">OAuth2.0</option>
              <option value="Key Pair Authentication">Key Pair Authentication</option>
            </>
          )}
        </select>
      </div>

      {/* Username/Password Authentication */}
      {(authType === 'username/password' || authType === 'Username and Password') && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Username *
            </label>
            <input
              type="text"
              value={type === 'destination' ? config.username : config.username}
              onChange={(e) => updateConfig('username', e.target.value)}
              placeholder="snowflake_user"
              className="input w-full"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Password *
            </label>
            <input
              type="password"
              value={type === 'destination' ? config.credentials?.password : config.password}
              onChange={(e) => {
                if (type === 'destination') {
                  updateCredentials('password', e.target.value)
                } else {
                  updateConfig('password', e.target.value)
                }
              }}
              placeholder="••••••••"
              className="input w-full"
              required
            />
          </div>
        </div>
      )}

      {/* OAuth Authentication */}
      {(authType === 'OAuth' || authType === 'OAuth2.0') && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Client ID *
              </label>
              <input
                type="text"
                value={type === 'destination' ? config.credentials?.client_id : config.client_id}
                onChange={(e) => {
                  if (type === 'destination') {
                    updateCredentials('client_id', e.target.value)
                  } else {
                    updateConfig('client_id', e.target.value)
                  }
                }}
                placeholder="oauth_client_id"
                className="input w-full"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Client Secret *
              </label>
              <input
                type="password"
                value={type === 'destination' ? config.credentials?.client_secret : config.client_secret}
                onChange={(e) => {
                  if (type === 'destination') {
                    updateCredentials('client_secret', e.target.value)
                  } else {
                    updateConfig('client_secret', e.target.value)
                  }
                }}
                placeholder="••••••••"
                className="input w-full"
                required
              />
            </div>
          </div>
          
          {type === 'destination' && (
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Access Token *
              </label>
              <input
                type="text"
                value={config.credentials?.access_token || ''}
                onChange={(e) => updateCredentials('access_token', e.target.value)}
                placeholder="access_token"
                className="input w-full"
                required
              />
            </div>
          )}
          
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Refresh Token {type === 'destination' ? '*' : ''}
            </label>
            <input
              type="text"
              value={type === 'destination' ? config.credentials?.refresh_token : config.refresh_token}
              onChange={(e) => {
                if (type === 'destination') {
                  updateCredentials('refresh_token', e.target.value)
                } else {
                  updateConfig('refresh_token', e.target.value)
                }
              }}
              placeholder="refresh_token"
              className="input w-full"
              required={type === 'destination'}
            />
          </div>
          
          {type === 'source' && (
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Access Token
              </label>
              <input
                type="text"
                value={config.access_token}
                onChange={(e) => updateConfig('access_token', e.target.value)}
                placeholder="access_token"
                className="input w-full"
              />
            </div>
          )}
        </div>
      )}

      {/* Key Pair Authentication */}
      {authType === 'Key Pair Authentication' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Username *
            </label>
            <input
              type="text"
              value={config.username}
              onChange={(e) => updateConfig('username', e.target.value)}
              placeholder="snowflake_user"
              className="input w-full"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Private Key *
            </label>
            <textarea
              value={type === 'destination' ? config.credentials?.private_key : config.private_key}
              onChange={(e) => {
                if (type === 'destination') {
                  updateCredentials('private_key', e.target.value)
                } else {
                  updateConfig('private_key', e.target.value)
                }
              }}
              placeholder="-----BEGIN PRIVATE KEY-----"
              className="input w-full h-32 font-mono text-sm"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Private Key Password
            </label>
            <input
              type="password"
              value={type === 'destination' ? config.credentials?.private_key_password : config.private_key_password}
              onChange={(e) => {
                if (type === 'destination') {
                  updateCredentials('private_key_password', e.target.value)
                } else {
                  updateConfig('private_key_password', e.target.value)
                }
              }}
              placeholder="••••••••"
              className="input w-full"
            />
          </div>
        </div>
      )}

      {/* Advanced Options */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          JDBC URL Parameters
        </label>
        <input
          type="text"
          value={config.jdbc_url_params}
          onChange={(e) => updateConfig('jdbc_url_params', e.target.value)}
          placeholder="key1=value1&key2=value2"
          className="input w-full"
        />
        <p className="text-xs text-neutral-500 mt-1">
          Additional JDBC parameters formatted as 'key=value' pairs separated by '&'
        </p>
      </div>
    </div>
  )
}
