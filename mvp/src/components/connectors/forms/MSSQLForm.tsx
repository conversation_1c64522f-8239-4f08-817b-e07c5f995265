'use client'
import { useState, useEffect } from 'react'

interface MSSQLFormProps {
  configuration: any
  onChange: (config: any) => void
}

export default function MSSQLForm({ configuration, onChange }: MSSQLFormProps) {
  const [config, setConfig] = useState({
    host: '',
    port: 1433,
    database: '',
    username: '',
    password: '',
    sourceType: 'mssql',
    ...configuration
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="space-y-6">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <p className="text-sm text-yellow-800">
          MSSQL configuration form is under development. Basic fields are shown below.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Host *
          </label>
          <input
            type="text"
            value={config.host}
            onChange={(e) => updateConfig('host', e.target.value)}
            placeholder="localhost"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Port *
          </label>
          <input
            type="number"
            value={config.port}
            onChange={(e) => updateConfig('port', parseInt(e.target.value))}
            placeholder="1433"
            className="input w-full"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Database *
        </label>
        <input
          type="text"
          value={config.database}
          onChange={(e) => updateConfig('database', e.target.value)}
          placeholder="my_database"
          className="input w-full"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Username *
          </label>
          <input
            type="text"
            value={config.username}
            onChange={(e) => updateConfig('username', e.target.value)}
            placeholder="sa"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Password *
          </label>
          <input
            type="password"
            value={config.password}
            onChange={(e) => updateConfig('password', e.target.value)}
            placeholder="••••••••"
            className="input w-full"
            required
          />
        </div>
      </div>
    </div>
  )
}
