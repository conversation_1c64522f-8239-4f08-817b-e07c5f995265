'use client'
import { useState, useEffect } from 'react'

interface MSSQLFormProps {
  configuration: any
  onChange: (config: any) => void
}

// MSSQL Source configuration class
class MSSQLSourceConfig {
  host: string = ''
  port: number = 1433
  database: string = ''
  schemas: string = 'dbo'
  username: string = ''
  password: string = ''
  jdbc_url_params?: string = ''
  ssl_method: {
    ssl_method: 'unencrypted' | 'encrypted_trust_server_certificate' | 'encrypted_verify_certificate'
    hostNameInCertificate?: string
    certificate?: string
  } = { ssl_method: 'unencrypted' }
  sourceType: string = 'mssql'
}

export default function MSSQLForm({ configuration, onChange }: MSSQLFormProps) {
  const [config, setConfig] = useState(() => {
    const sourceConfig = new MSSQLSourceConfig()
    return { ...sourceConfig, ...configuration }
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      [field]: value
    }))
  }

  const updateNestedConfig = (parent: string, field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value
      }
    }))
  }

  return (
    <div className="space-y-6">
      {/* Connection Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Host *
          </label>
          <input
            type="text"
            value={config.host}
            onChange={(e) => updateConfig('host', e.target.value)}
            placeholder="localhost"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Port *
          </label>
          <input
            type="number"
            value={config.port}
            onChange={(e) => updateConfig('port', parseInt(e.target.value))}
            placeholder="1433"
            className="input w-full"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Database *
          </label>
          <input
            type="text"
            value={config.database}
            onChange={(e) => updateConfig('database', e.target.value)}
            placeholder="my_database"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Schemas
          </label>
          <input
            type="text"
            value={config.schemas}
            onChange={(e) => updateConfig('schemas', e.target.value)}
            placeholder="dbo"
            className="input w-full"
          />
        </div>
      </div>

      {/* Authentication */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Username *
          </label>
          <input
            type="text"
            value={config.username}
            onChange={(e) => updateConfig('username', e.target.value)}
            placeholder="sa"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Password *
          </label>
          <input
            type="password"
            value={config.password}
            onChange={(e) => updateConfig('password', e.target.value)}
            placeholder="••••••••"
            className="input w-full"
            required
          />
        </div>
      </div>

      {/* SSL Configuration */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          SSL Method
        </label>
        <select
          value={config.ssl_method.ssl_method}
          onChange={(e) => updateNestedConfig('ssl_method', 'ssl_method', e.target.value)}
          className="input w-full"
        >
          <option value="unencrypted">Unencrypted</option>
          <option value="encrypted_trust_server_certificate">Encrypted (Trust Server Certificate)</option>
          <option value="encrypted_verify_certificate">Encrypted (Verify Certificate)</option>
        </select>
      </div>

      {config.ssl_method.ssl_method === 'encrypted_verify_certificate' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Host Name in Certificate
            </label>
            <input
              type="text"
              value={config.ssl_method.hostNameInCertificate || ''}
              onChange={(e) => updateNestedConfig('ssl_method', 'hostNameInCertificate', e.target.value)}
              placeholder="hostname"
              className="input w-full"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Certificate
            </label>
            <textarea
              value={config.ssl_method.certificate || ''}
              onChange={(e) => updateNestedConfig('ssl_method', 'certificate', e.target.value)}
              placeholder="-----BEGIN CERTIFICATE-----"
              className="input w-full h-24"
            />
          </div>
        </div>
      )}

      {/* JDBC URL Parameters */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          JDBC URL Parameters
        </label>
        <input
          type="text"
          value={config.jdbc_url_params}
          onChange={(e) => updateConfig('jdbc_url_params', e.target.value)}
          placeholder="key1=value1&key2=value2&key3=value3"
          className="input w-full"
        />
        <p className="text-xs text-neutral-500 mt-1">
          Additional properties formatted as 'key=value' pairs separated by '&'
        </p>
      </div>
    </div>
  )
}
