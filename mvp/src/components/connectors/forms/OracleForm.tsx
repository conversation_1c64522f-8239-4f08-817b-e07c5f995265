'use client'
import { useState, useEffect } from 'react'

interface OracleFormProps {
  configuration: any
  onChange: (config: any) => void
}

// Oracle Source configuration class
class OracleSourceConfig {
  host: string = ''
  port: number = 1521
  connection_data: {
    connection_type: 'service_name' | 'sid'
    service_name?: string
    sid?: string
  } = { connection_type: 'service_name' }
  username: string = ''
  password: string = ''
  schemas: string[] = ['user']
  jdbc_url_params?: string = ''
  encryption: {
    encryption_method: 'unencrypted' | 'client_nne' | 'encrypted_verify_certificate'
  } = { encryption_method: 'unencrypted' }
  tunnel_method: {
    tunnel_method: 'NO_TUNNEL' | 'SSH_KEY_AUTH' | 'PASSWORD_AUTH'
    tunnel_host?: string
    tunnel_port?: number
    tunnel_user?: string
    ssh_key?: string
    tunnel_user_password?: string
  } = { tunnel_method: 'NO_TUNNEL' }
  sourceType: string = 'oracle'
}

export default function OracleForm({ configuration, onChange }: OracleFormProps) {
  const [config, setConfig] = useState(() => {
    const sourceConfig = new OracleSourceConfig()
    return { ...sourceConfig, ...configuration }
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      [field]: value
    }))
  }

  const updateNestedConfig = (parent: string, field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value
      }
    }))
  }

  // Helper function to convert schemas array to comma-separated string for display
  const getSchemasString = (): string => {
    return config.schemas.join(', ')
  }

  // Helper function to convert comma-separated string to schemas array
  const setSchemasFromString = (schemasString: string) => {
    const schemas = schemasString
      .split(',')
      .map(s => s.trim())
      .filter(s => s.length > 0)

    updateConfig('schemas', schemas.length > 0 ? schemas : ['user'])
  }

  return (
    <div className="space-y-6">
      {/* Connection Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Host *
          </label>
          <input
            type="text"
            value={config.host}
            onChange={(e) => updateConfig('host', e.target.value)}
            placeholder="localhost"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Port *
          </label>
          <input
            type="number"
            value={config.port}
            onChange={(e) => updateConfig('port', parseInt(e.target.value))}
            placeholder="1521"
            className="input w-full"
            required
          />
        </div>
      </div>

      {/* Connection Data */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-neutral-900">Connection Data</h3>

        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Connection Type
          </label>
          <select
            value={config.connection_data.connection_type}
            onChange={(e) => updateNestedConfig('connection_data', 'connection_type', e.target.value)}
            className="input w-full"
          >
            <option value="service_name">Service Name</option>
            <option value="sid">SID</option>
          </select>
        </div>

        {config.connection_data.connection_type === 'service_name' && (
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Service Name *
            </label>
            <input
              type="text"
              value={config.connection_data.service_name || ''}
              onChange={(e) => updateNestedConfig('connection_data', 'service_name', e.target.value)}
              placeholder="ORCL"
              className="input w-full"
              required
            />
          </div>
        )}

        {config.connection_data.connection_type === 'sid' && (
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              SID *
            </label>
            <input
              type="text"
              value={config.connection_data.sid || ''}
              onChange={(e) => updateNestedConfig('connection_data', 'sid', e.target.value)}
              placeholder="ORCL"
              className="input w-full"
              required
            />
          </div>
        )}
      </div>

      {/* Authentication */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Username *
          </label>
          <input
            type="text"
            value={config.username}
            onChange={(e) => updateConfig('username', e.target.value)}
            placeholder="oracle_user"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Password *
          </label>
          <input
            type="password"
            value={config.password}
            onChange={(e) => updateConfig('password', e.target.value)}
            placeholder="••••••••"
            className="input w-full"
            required
          />
        </div>
      </div>

      {/* Schemas */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Schemas *
        </label>
        <input
          type="text"
          value={getSchemasString()}
          onChange={(e) => setSchemasFromString(e.target.value)}
          placeholder="user, schema1, schema2"
          className="input w-full"
          required
        />
        <p className="text-xs text-neutral-500 mt-1">
          Enter schema names separated by commas (e.g., user, schema1, schema2). Case sensitive.
        </p>
      </div>

      {/* Encryption */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Encryption Method *
        </label>
        <select
          value={config.encryption.encryption_method}
          onChange={(e) => updateNestedConfig('encryption', 'encryption_method', e.target.value)}
          className="input w-full"
        >
          <option value="unencrypted">Unencrypted</option>
          <option value="client_nne">Client NNE</option>
          <option value="encrypted_verify_certificate">Encrypted (Verify Certificate)</option>
        </select>
      </div>

      {/* Tunnel Method */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Tunnel Method *
        </label>
        <select
          value={config.tunnel_method.tunnel_method}
          onChange={(e) => updateNestedConfig('tunnel_method', 'tunnel_method', e.target.value)}
          className="input w-full"
        >
          <option value="NO_TUNNEL">No Tunnel</option>
          <option value="SSH_KEY_AUTH">SSH Key Authentication</option>
          <option value="PASSWORD_AUTH">SSH Password Authentication</option>
        </select>
      </div>

      {/* Tunnel Configuration */}
      {config.tunnel_method.tunnel_method !== 'NO_TUNNEL' && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Tunnel Host
              </label>
              <input
                type="text"
                value={config.tunnel_method.tunnel_host || ''}
                onChange={(e) => updateNestedConfig('tunnel_method', 'tunnel_host', e.target.value)}
                placeholder="tunnel.example.com"
                className="input w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Tunnel Port
              </label>
              <input
                type="number"
                value={config.tunnel_method.tunnel_port || 22}
                onChange={(e) => updateNestedConfig('tunnel_method', 'tunnel_port', parseInt(e.target.value))}
                placeholder="22"
                className="input w-full"
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Tunnel User
            </label>
            <input
              type="text"
              value={config.tunnel_method.tunnel_user || ''}
              onChange={(e) => updateNestedConfig('tunnel_method', 'tunnel_user', e.target.value)}
              placeholder="tunnel_user"
              className="input w-full"
            />
          </div>

          {config.tunnel_method.tunnel_method === 'SSH_KEY_AUTH' && (
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                SSH Private Key
              </label>
              <textarea
                value={config.tunnel_method.ssh_key || ''}
                onChange={(e) => updateNestedConfig('tunnel_method', 'ssh_key', e.target.value)}
                placeholder="-----BEGIN PRIVATE KEY-----"
                className="input w-full h-24"
              />
            </div>
          )}

          {config.tunnel_method.tunnel_method === 'PASSWORD_AUTH' && (
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Tunnel Password
              </label>
              <input
                type="password"
                value={config.tunnel_method.tunnel_user_password || ''}
                onChange={(e) => updateNestedConfig('tunnel_method', 'tunnel_user_password', e.target.value)}
                placeholder="••••••••"
                className="input w-full"
              />
            </div>
          )}
        </div>
      )}

      {/* Advanced Options */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          JDBC URL Parameters
        </label>
        <input
          type="text"
          value={config.jdbc_url_params}
          onChange={(e) => updateConfig('jdbc_url_params', e.target.value)}
          placeholder="key1=value1&key2=value2"
          className="input w-full"
        />
        <p className="text-xs text-neutral-500 mt-1">
          Additional properties formatted as 'key=value' pairs separated by '&'
        </p>
      </div>
    </div>
  )
}
