'use client'
import { useState, useEffect } from 'react'

interface GithubFormProps {
  configuration: any
  onChange: (config: any) => void
}

// Github Source configuration class
class GithubSourceConfig {
  credentials: {
    option_title: string
    personal_access_token: string
  } = {
    option_title: 'PAT Credentials',
    personal_access_token: ''
  }
  repositories: string[] = []
  api_url: string = 'https://api.github.com/'
  branches: string[] = []
  sourceType: string = 'github'
}

export default function GithubForm({ configuration, onChange }: GithubFormProps) {
  const [config, setConfig] = useState(() => {
    const sourceConfig = new GithubSourceConfig()
    return { ...sourceConfig, ...configuration }
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      [field]: value
    }))
  }

  const updateCredentials = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      credentials: {
        ...prev.credentials,
        [field]: value
      }
    }))
  }

  // Helper function to convert repositories array to comma-separated string for display
  const getRepositoriesString = (): string => {
    return config.repositories.join(', ')
  }

  // Helper function to convert comma-separated string to repositories array
  const setRepositoriesFromString = (repositoriesString: string) => {
    const repositories = repositoriesString
      .split(',')
      .map(r => r.trim())
      .filter(r => r.length > 0)
    
    updateConfig('repositories', repositories)
  }

  // Helper function to convert branches array to comma-separated string for display
  const getBranchesString = (): string => {
    return config.branches.join(', ')
  }

  // Helper function to convert comma-separated string to branches array
  const setBranchesFromString = (branchesString: string) => {
    const branches = branchesString
      .split(',')
      .map(b => b.trim())
      .filter(b => b.length > 0)
    
    updateConfig('branches', branches)
  }

  return (
    <div className="space-y-6">
      {/* Credentials */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-neutral-900">Credentials *</h3>
        
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Option Title
          </label>
          <input
            type="text"
            value={config.credentials.option_title}
            onChange={(e) => updateCredentials('option_title', e.target.value)}
            placeholder="PAT Credentials"
            className="input w-full"
            readOnly
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Personal Access Token *
          </label>
          <input
            type="password"
            value={config.credentials.personal_access_token}
            onChange={(e) => updateCredentials('personal_access_token', e.target.value)}
            placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
            className="input w-full"
            required
          />
          <p className="text-xs text-neutral-500 mt-1">
            Generate a personal access token from GitHub Settings → Developer settings → Personal access tokens
          </p>
        </div>
      </div>

      {/* Repositories */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Repositories *
        </label>
        <input
          type="text"
          value={getRepositoriesString()}
          onChange={(e) => setRepositoriesFromString(e.target.value)}
          placeholder="airbytehq/airbyte, facebook/react, microsoft/vscode"
          className="input w-full"
          required
        />
        <p className="text-xs text-neutral-500 mt-1">
          Enter repository names in format owner/repo, separated by commas (e.g., airbytehq/airbyte)
        </p>
      </div>

      {/* API URL */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          API URL
        </label>
        <input
          type="url"
          value={config.api_url}
          onChange={(e) => updateConfig('api_url', e.target.value)}
          placeholder="https://api.github.com/"
          className="input w-full"
        />
        <p className="text-xs text-neutral-500 mt-1">
          Your basic URL from self-hosted GitHub instance or leave it empty to use GitHub. Defaults to https://api.github.com/
        </p>
      </div>

      {/* Branches */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Branches
        </label>
        <input
          type="text"
          value={getBranchesString()}
          onChange={(e) => setBranchesFromString(e.target.value)}
          placeholder="airbytehq/airbyte/master, facebook/react/main"
          className="input w-full"
        />
        <p className="text-xs text-neutral-500 mt-1">
          List of GitHub repository branches to pull commits for (e.g., airbytehq/airbyte/master), separated by commas
        </p>
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">Setup Instructions:</h4>
        <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
          <li>Go to GitHub Settings → Developer settings → Personal access tokens</li>
          <li>Click "Generate new token (classic)"</li>
          <li>Select appropriate scopes (repo, read:org, read:user, read:discussion)</li>
          <li>Copy the generated token and paste it above</li>
          <li>Specify the repositories you want to sync from</li>
          <li>Optionally specify specific branches to sync</li>
        </ol>
      </div>
    </div>
  )
}
