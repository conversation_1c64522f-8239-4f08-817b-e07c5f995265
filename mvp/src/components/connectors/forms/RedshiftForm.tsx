'use client'
import { useState, useEffect } from 'react'

interface RedshiftFormProps {
  configuration: any
  onChange: (config: any) => void
  type: 'source' | 'destination'
}

export default function RedshiftForm({ configuration, onChange, type }: RedshiftFormProps) {
  const [config, setConfig] = useState({
    host: '',
    port: 5439,
    database: '',
    schema: 'public',
    username: '',
    password: '',
    sourceType: type === 'source' ? 'redshift' : undefined,
    destinationType: type === 'destination' ? 'redshift' : undefined,
    ...configuration
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Host *
          </label>
          <input
            type="text"
            value={config.host}
            onChange={(e) => updateConfig('host', e.target.value)}
            placeholder="redshift-cluster.amazonaws.com"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Port *
          </label>
          <input
            type="number"
            value={config.port}
            onChange={(e) => updateConfig('port', parseInt(e.target.value))}
            placeholder="5439"
            className="input w-full"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Database *
          </label>
          <input
            type="text"
            value={config.database}
            onChange={(e) => updateConfig('database', e.target.value)}
            placeholder="dev"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Schema
          </label>
          <input
            type="text"
            value={config.schema}
            onChange={(e) => updateConfig('schema', e.target.value)}
            placeholder="public"
            className="input w-full"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Username *
          </label>
          <input
            type="text"
            value={config.username}
            onChange={(e) => updateConfig('username', e.target.value)}
            placeholder="redshift_user"
            className="input w-full"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Password *
          </label>
          <input
            type="password"
            value={config.password}
            onChange={(e) => updateConfig('password', e.target.value)}
            placeholder="••••••••"
            className="input w-full"
            required
          />
        </div>
      </div>
    </div>
  )
}
