'use client'
import { useState, useEffect } from 'react'

interface GitlabFormProps {
  configuration: any
  onChange: (config: any) => void
}

// Gitlab Source configuration class
class GitlabSourceConfig {
  credentials: {
    auth_type: string
    access_token: string
  } = {
    auth_type: 'access_token',
    access_token: ''
  }
  api_url: string = 'gitlab.com'
  groups_list: string[] = []
  projects_list: string[] = []
  sourceType: string = 'gitlab'
}

export default function GitlabForm({ configuration, onChange }: GitlabFormProps) {
  const [config, setConfig] = useState(() => {
    const sourceConfig = new GitlabSourceConfig()
    return { ...sourceConfig, ...configuration }
  })

  useEffect(() => {
    onChange(config)
  }, [config, onChange])

  const updateConfig = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      [field]: value
    }))
  }

  const updateCredentials = (field: string, value: any) => {
    setConfig((prev: any) => ({
      ...prev,
      credentials: {
        ...prev.credentials,
        [field]: value
      }
    }))
  }

  // Helper function to convert groups_list array to comma-separated string for display
  const getGroupsString = (): string => {
    return config.groups_list.join(', ')
  }

  // Helper function to convert comma-separated string to groups_list array
  const setGroupsFromString = (groupsString: string) => {
    const groups = groupsString
      .split(',')
      .map(g => g.trim())
      .filter(g => g.length > 0)
    
    updateConfig('groups_list', groups)
  }

  // Helper function to convert projects_list array to space-delimited string for display
  const getProjectsString = (): string => {
    return config.projects_list.join(' ')
  }

  // Helper function to convert space-delimited string to projects_list array
  const setProjectsFromString = (projectsString: string) => {
    const projects = projectsString
      .split(' ')
      .map(p => p.trim())
      .filter(p => p.length > 0)
    
    updateConfig('projects_list', projects)
  }

  return (
    <div className="space-y-6">
      {/* Credentials */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-neutral-900">Credentials *</h3>
        
        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Auth Type
          </label>
          <input
            type="text"
            value={config.credentials.auth_type}
            onChange={(e) => updateCredentials('auth_type', e.target.value)}
            placeholder="access_token"
            className="input w-full"
            readOnly
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-neutral-700 mb-2">
            Access Token *
          </label>
          <input
            type="password"
            value={config.credentials.access_token}
            onChange={(e) => updateCredentials('access_token', e.target.value)}
            placeholder="glpat-xxxxxxxxxxxxxxxxxxxx"
            className="input w-full"
            required
          />
          <p className="text-xs text-neutral-500 mt-1">
            Generate a personal access token from GitLab Settings → Access Tokens
          </p>
        </div>
      </div>

      {/* API URL */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          API URL
        </label>
        <input
          type="text"
          value={config.api_url}
          onChange={(e) => updateConfig('api_url', e.target.value)}
          placeholder="gitlab.com"
          className="input w-full"
        />
        <p className="text-xs text-neutral-500 mt-1">
          GitLab instance URL. Defaults to gitlab.com for GitLab.com or specify your self-hosted instance
        </p>
      </div>

      {/* Groups List */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Groups List
        </label>
        <input
          type="text"
          value={getGroupsString()}
          onChange={(e) => setGroupsFromString(e.target.value)}
          placeholder="airbyte.io, meltano, gitlab-org"
          className="input w-full"
        />
        <p className="text-xs text-neutral-500 mt-1">
          List of groups to sync from, separated by commas (e.g., airbyte.io)
        </p>
      </div>

      {/* Projects List */}
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          Projects List
        </label>
        <input
          type="text"
          value={getProjectsString()}
          onChange={(e) => setProjectsFromString(e.target.value)}
          placeholder="airbyte.io/documentation meltano/tap-gitlab gitlab-org/gitlab"
          className="input w-full"
        />
        <p className="text-xs text-neutral-500 mt-1">
          Space-delimited list of projects to sync from (e.g., airbyte.io/documentation meltano/tap-gitlab)
        </p>
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">Setup Instructions:</h4>
        <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
          <li>Go to GitLab Settings → Access Tokens</li>
          <li>Create a new personal access token</li>
          <li>Select appropriate scopes (api, read_api, read_repository)</li>
          <li>Copy the generated token and paste it above</li>
          <li>Specify either groups or projects (or both) to sync from</li>
          <li>For self-hosted GitLab, update the API URL accordingly</li>
        </ol>
      </div>

      {/* Additional Info */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 className="font-medium text-yellow-900 mb-2">Note:</h4>
        <p className="text-sm text-yellow-800">
          You can specify either groups or projects (or both). If you specify groups, all projects within those groups will be synced. 
          If you specify individual projects, only those specific projects will be synced.
        </p>
      </div>
    </div>
  )
}
