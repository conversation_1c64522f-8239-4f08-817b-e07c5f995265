'use client'
import { useState, useEffect } from 'react'
import { X } from 'lucide-react'
import { useAuthStore } from '@/store/useAuthStore'
import { airbyteService, type AirbyteSource, type AirbyteDestination } from '@/lib/airbyteService'
import { useAsyncWithTokenRefresh } from '@/components/hoc/withTokenRefresh'
import { cn } from '@/lib/utils'
import toast from 'react-hot-toast'
// Import all connector forms
import AzureBlobForm from './forms/AzureBlobForm'
import BigQueryForm from './forms/BigQueryForm'
import DatabricksForm from './forms/DatabricksForm'
import GCSForm from './forms/GCSForm'
import GithubForm from './forms/GithubForm'
import GitlabForm from './forms/GitlabForm'
import GoogleSheetsForm from './forms/GoogleSheetsForm'
import MSSQLForm from './forms/MSSQLForm'
import MySQLForm from './forms/MySQLForm'
import OracleForm from './forms/OracleForm'
import PostgreSQLForm from './forms/PostgreSQLForm'
import RedshiftForm from './forms/RedshiftForm'
import S3Form from './forms/S3Form'
import SnowflakeForm from './forms/SnowflakeForm'

interface EditConnectorModalProps {
  isOpen: boolean
  onClose: () => void
  connector: {
    id: string
    name: string
    type: 'source' | 'destination'
    connectorType: string
    connectionConfiguration: any
  }
  onSuccess: () => void
}

export default function EditConnectorModal({ isOpen, onClose, connector, onSuccess }: EditConnectorModalProps) {
  const { user } = useAuthStore()
  const { withTokenRefresh } = useAsyncWithTokenRefresh()
  const [loading, setLoading] = useState(false)
  const [connectorName, setConnectorName] = useState('')
  const [configuration, setConfiguration] = useState<any>({})

  // Initialize form with current connector data
  useEffect(() => {
    if (isOpen && connector) {
      setConnectorName(connector.name)
      // Ensure we have a valid configuration object
      const config = connector.connectionConfiguration || {}
      console.log('Initializing edit form with configuration:', config)
      setConfiguration(config)
    }
  }, [isOpen, connector])

  const handleClose = () => {
    if (!loading) {
      onClose()
      // Reset form
      setConnectorName('')
      setConfiguration({})
    }
  }

  const handleTestConnection = async () => {
    if (!user?.id) {
      toast.error('User not authenticated')
      return
    }

    setLoading(true)
    try {
      await withTokenRefresh(async () => {
        toast.loading('Testing connection...', { id: 'connection-test' })

        let connectionResult
        if (connector.type === 'source') {
          connectionResult = await airbyteService.checkSourceConnection(configuration)
        } else {
          connectionResult = await airbyteService.checkDestinationConnection(configuration)
        }

        toast.dismiss('connection-test')

        if (connectionResult.status === 'failed') {
          toast.error(`Connection test failed: ${connectionResult.message || 'Unknown error'}`)
        } else {
          toast.success('Connection test successful!')
        }
      })
    } catch (error) {
      console.error('Error testing connection:', error)
      toast.error('Failed to test connection')
    } finally {
      setLoading(false)
    }
  }

  // Function to render the appropriate connector form
  const renderConnectorForm = () => {
    console.log('Rendering form for connector type:', connector.connectorType, 'with configuration:', configuration)

    const baseProps = {
      configuration,
      onChange: setConfiguration
    }

    const propsWithType = {
      ...baseProps,
      type: connector.type as 'source' | 'destination'
    }

    switch (connector.connectorType) {
      case 'azure-blob-storage':
        return <AzureBlobForm {...propsWithType} />
      case 'bigquery':
        return <BigQueryForm type={'source'} {...baseProps} />
      case 'databricks':
        return <DatabricksForm {...baseProps} />
      case 'gcs':
        return <GCSForm {...propsWithType} />
      case 'github':
        return <GithubForm {...baseProps} />
      case 'gitlab':
        return <GitlabForm {...baseProps} />
      case 'google-sheets':
        return <GoogleSheetsForm {...baseProps} />
      case 'mssql':
        return <MSSQLForm {...baseProps} />
      case 'mysql':
        return <MySQLForm {...baseProps} />
      case 'oracle':
        return <OracleForm {...baseProps} />
      case 'postgres':
        return <PostgreSQLForm {...baseProps} />
      case 'redshift':
        return <RedshiftForm type={'source'} {...baseProps} />
      case 's3':
        return <S3Form {...baseProps} />
      case 'snowflake':
        return <SnowflakeForm {...propsWithType} />
      default:
        return (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              Configuration form for {connector.connectorType} is not yet implemented.
            </p>
          </div>
        )
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user?.id) {
      toast.error('User not authenticated')
      return
    }

    if (!connectorName.trim()) {
      toast.error('Connector name is required')
      return
    }

    setLoading(true)
    try {
      await withTokenRefresh(async () => {
        // First, test the connection with new configuration
        toast.loading('Testing connection...', { id: 'connection-test' })

        let connectionResult
        if (connector.type === 'source') {
          connectionResult = await airbyteService.checkSourceConnection(configuration)
        } else {
          connectionResult = await airbyteService.checkDestinationConnection(configuration)
        }

        toast.dismiss('connection-test')

        if (connectionResult.status === 'failed') {
          toast.error(`Connection test failed: ${connectionResult.message || 'Unknown error'}`)
          return
        }

        toast.success('Connection test successful!')

        // If connection test passes, update the connector
        if (connector.type === 'source') {
          await airbyteService.updateSource(user.id, connector.id, {
            name: connectorName,
            configuration
          })
          toast.success(`Source "${connectorName}" updated successfully!`)
        } else {
          await airbyteService.updateDestination(user.id, connector.id, {
            name: connectorName,
            configuration
          })
          toast.success(`Destination "${connectorName}" updated successfully!`)
        }
      })

      onSuccess()
      handleClose()
    } catch (error) {
      console.error('Error updating connector:', error)
      toast.error(`Failed to update ${connector.type}`)
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-200">
          <div>
            <h2 className="text-xl font-semibold text-neutral-900">
              Edit {connector.type === 'source' ? 'Source' : 'Destination'}
            </h2>
            <p className="text-sm text-neutral-600 mt-1">
              Update configuration for {connector.connectorType} {connector.type}
            </p>
          </div>
          <button
            onClick={handleClose}
            disabled={loading}
            className="text-neutral-400 hover:text-neutral-600 disabled:opacity-50"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Connector Name */}
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                {connector.type === 'source' ? 'Source' : 'Destination'} Name *
              </label>
              <input
                type="text"
                value={connectorName}
                onChange={(e) => setConnectorName(e.target.value)}
                placeholder={`Enter ${connector.type} name`}
                className="input w-full"
                required
                disabled={loading}
              />
            </div>

            {/* Configuration Form */}
            <div>
              <h3 className="text-lg font-semibold text-neutral-900 mb-4">Configuration</h3>
              {renderConnectorForm()}
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-neutral-200 bg-neutral-50 rounded-b-2xl">
          <button
            type="button"
            onClick={handleTestConnection}
            disabled={loading}
            className="btn-secondary"
          >
            Test Connection
          </button>
          <div className="flex items-center space-x-3">
            <button
              type="button"
              onClick={handleClose}
              disabled={loading}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              disabled={loading || !connectorName.trim()}
              className={cn(
                "btn-primary",
                loading && "opacity-50 cursor-not-allowed"
              )}
            >
              {loading ? 'Updating...' : `Update ${connector.type === 'source' ? 'Source' : 'Destination'}`}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
