'use client'
import { useState, useEffect } from 'react'
import { X } from 'lucide-react'
import { useAuthStore } from '@/store/useAuthStore'
import { airbyteService, type AirbyteSource, type AirbyteDestination } from '@/lib/airbyteService'
import { useAsyncWithTokenRefresh } from '@/components/hoc/withTokenRefresh'
import { cn } from '@/lib/utils'
import toast from 'react-hot-toast'
import ConnectorConfigForm from './ConnectorConfigForm'

interface EditConnectorModalProps {
  isOpen: boolean
  onClose: () => void
  connector: {
    id: string
    name: string
    type: 'source' | 'destination'
    connectorType: string
    connectionConfiguration: any
  }
  onSuccess: () => void
}

export default function EditConnectorModal({ isOpen, onClose, connector, onSuccess }: EditConnectorModalProps) {
  const { user } = useAuthStore()
  const { withTokenRefresh } = useAsyncWithTokenRefresh()
  const [loading, setLoading] = useState(false)
  const [connectorName, setConnectorName] = useState('')
  const [configuration, setConfiguration] = useState<any>({})

  // Initialize form with current connector data
  useEffect(() => {
    if (isOpen && connector) {
      setConnectorName(connector.name)
      setConfiguration(connector.connectionConfiguration || {})
    }
  }, [isOpen, connector])

  const handleClose = () => {
    if (!loading) {
      onClose()
      // Reset form
      setConnectorName('')
      setConfiguration({})
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user?.id) {
      toast.error('User not authenticated')
      return
    }

    if (!connectorName.trim()) {
      toast.error('Connector name is required')
      return
    }

    setLoading(true)
    try {
      await withTokenRefresh(async () => {
        if (connector.type === 'source') {
          await airbyteService.updateSource(user.id, connector.id, {
            name: connectorName,
            configuration
          })
          toast.success(`Source "${connectorName}" updated successfully!`)
        } else {
          await airbyteService.updateDestination(user.id, connector.id, {
            name: connectorName,
            configuration
          })
          toast.success(`Destination "${connectorName}" updated successfully!`)
        }
      })
      
      onSuccess()
      handleClose()
    } catch (error) {
      console.error('Error updating connector:', error)
      toast.error(`Failed to update ${connector.type}`)
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  // Create a mock definition object for the ConnectorConfigForm
  const mockDefinition = {
    id: connector.connectorType,
    name: connector.connectorType,
    dockerRepository: '',
    dockerImageTag: '',
    documentationUrl: '',
    icon: '',
    sourceDefinitionId: connector.type === 'source' ? connector.id : undefined,
    destinationDefinitionId: connector.type === 'destination' ? connector.id : undefined,
    releaseStage: 'generally_available' as const,
    releaseDate: '',
    supportLevel: 'certified' as const,
    supportsDbt: false,
    normalizationConfig: undefined,
    suggestedStreams: undefined
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-200">
          <div>
            <h2 className="text-xl font-semibold text-neutral-900">
              Edit {connector.type === 'source' ? 'Source' : 'Destination'}
            </h2>
            <p className="text-sm text-neutral-600 mt-1">
              Update configuration for {connector.connectorType} {connector.type}
            </p>
          </div>
          <button
            onClick={handleClose}
            disabled={loading}
            className="text-neutral-400 hover:text-neutral-600 disabled:opacity-50"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Connector Name */}
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                {connector.type === 'source' ? 'Source' : 'Destination'} Name *
              </label>
              <input
                type="text"
                value={connectorName}
                onChange={(e) => setConnectorName(e.target.value)}
                placeholder={`Enter ${connector.type} name`}
                className="input w-full"
                required
                disabled={loading}
              />
            </div>

            {/* Configuration Form */}
            <div>
              <h3 className="text-lg font-semibold text-neutral-900 mb-4">Configuration</h3>
              <ConnectorConfigForm
                definition={mockDefinition}
                type={connector.type}
                initialConfiguration={configuration}
                onConfigurationChange={setConfiguration}
                isEditMode={true}
              />
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-neutral-200 bg-neutral-50 rounded-b-2xl">
          <button
            type="button"
            onClick={handleClose}
            disabled={loading}
            className="btn-secondary"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={loading || !connectorName.trim()}
            className={cn(
              "btn-primary",
              loading && "opacity-50 cursor-not-allowed"
            )}
          >
            {loading ? 'Updating...' : `Update ${connector.type === 'source' ? 'Source' : 'Destination'}`}
          </button>
        </div>
      </div>
    </div>
  )
}
