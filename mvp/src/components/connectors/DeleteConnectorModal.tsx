'use client'
import { Al<PERSON><PERSON>riangle, X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface DeleteConnectorModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  connector: {
    name: string
    type: 'source' | 'destination'
  } | null
  loading: boolean
}

export default function DeleteConnectorModal({ 
  isOpen, 
  onClose, 
  onConfirm, 
  connector, 
  loading 
}: DeleteConnectorModalProps) {
  if (!isOpen || !connector) return null

  const handleClose = () => {
    if (!loading) {
      onClose()
    }
  }

  const handleConfirm = () => {
    if (!loading) {
      onConfirm()
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-neutral-900">
                Delete {connector.type === 'source' ? 'Source' : 'Destination'}
              </h2>
              <p className="text-sm text-neutral-600">
                This action cannot be undone
              </p>
            </div>
          </div>
          <button
            onClick={handleClose}
            disabled={loading}
            className="text-neutral-400 hover:text-neutral-600 disabled:opacity-50"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <p className="text-neutral-700 mb-4">
            Are you sure you want to delete the {connector.type}{' '}
            <span className="font-semibold text-neutral-900">"{connector.name}"</span>?
          </p>
          
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-red-900 mb-1">Warning</h4>
                <ul className="text-sm text-red-800 space-y-1">
                  <li>• This will permanently delete the {connector.type} connector</li>
                  <li>• All associated connections will be affected</li>
                  <li>• Data sync processes using this connector will stop</li>
                  <li>• This action cannot be reversed</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-neutral-200 bg-neutral-50 rounded-b-2xl">
          <button
            type="button"
            onClick={handleClose}
            disabled={loading}
            className="btn-secondary"
          >
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            disabled={loading}
            className={cn(
              "px-4 py-2 rounded-lg font-medium text-sm transition-colors",
              "bg-red-600 text-white hover:bg-red-700",
              "disabled:opacity-50 disabled:cursor-not-allowed"
            )}
          >
            {loading ? 'Deleting...' : `Delete ${connector.type === 'source' ? 'Source' : 'Destination'}`}
          </button>
        </div>
      </div>
    </div>
  )
}
