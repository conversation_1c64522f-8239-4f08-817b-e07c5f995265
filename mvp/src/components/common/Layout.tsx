'use client'
import { useAppStore } from '@/store/useAppStore'
import { cn } from '@/lib/utils'
import SidebarWrapper from './SidebarWrapper'
import Header from './Header'

interface LayoutProps {
  children: React.ReactNode
}

export default function Layout({ children }: LayoutProps) {
  const { sidebarCollapsed } = useAppStore()

  return (
    <div className="h-screen bg-gradient-to-br from-neutral-50 via-white to-neutral-100 flex overflow-hidden">
      {/* Sidebar */}
      <SidebarWrapper />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0 overflow-hidden">
        {/* Header */}
        <Header />
        
        {/* Page Content */}
        <main className={cn(
          "flex-1 overflow-hidden bg-transparent",
          "transition-all duration-300 ease-out"
        )}>
          <div className="h-full overflow-y-auto">
            <div className="min-h-full">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
