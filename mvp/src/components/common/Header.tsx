'use client'
import { useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import {
  Search,
  Bell,
  User,
  LogOut,
  Settings as SettingsIcon,
  Palette,
  Globe,
  ChevronDown,
  X,
  CheckCircle
} from 'lucide-react'
import { useAppStore, useNotifications } from '@/store/useAppStore'
import { useAuthStore } from '@/store/useAuthStore'
import { cn } from '@/lib/utils'
import toast from 'react-hot-toast'

export default function Header() {
  const router = useRouter()
  const pathname = usePathname()
  const { searchQuery, setSearchQuery } = useAppStore()
  const { notifications, unreadCount, markRead, markAllRead } = useNotifications()
  const { user, logout } = useAuthStore()
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)

  const handleLogout = () => {
    logout()
    toast.success('Successfully logged out')
    router.push('/login')
  }

  const getCurrentTab = () => {
    if (pathname === '/' || pathname === '/dashboard') return 'dashboard'
    if (pathname.startsWith('/connectors')) return 'connectors'
    if (pathname.startsWith('/ingestions')) return 'ingestions'
    if (pathname.startsWith('/migration')) return 'migration'
    if (pathname.startsWith('/objectives')) return 'objectives'
    if (pathname.startsWith('/mapping')) return 'mapping'
    if (pathname.startsWith('/settings')) return 'settings'
    if (pathname.startsWith('/api-docs')) return 'api-docs'
    return 'dashboard'
  }

  const activeTab = getCurrentTab()

  const getPageTitle = (tab: string) => {
    switch (tab) {
      case 'dashboard': return 'Dashboard'
      case 'connectors': return 'Connectors'
      case 'ingestions': return 'Data Ingestions'
      case 'migration': return 'Pipeline Migration'
      case 'objectives': return 'Business Objectives'
      case 'mapping': return 'Data Model Mapping'
      case 'api-docs': return 'API Documentation'
      case 'settings': return 'Settings'
      default: return 'Dashboard'
    }
  }

  const getPageDescription = (tab: string) => {
    switch (tab) {
      case 'dashboard': return 'Monitor your data integration pipelines and system health'
      case 'connectors': return 'Manage your data sources and destinations'
      case 'ingestions': return 'Monitor and configure your data ingestion pipelines'
      case 'migration': return 'AI-powered analysis and automated migration of legacy systems'
      case 'objectives': return 'AI-driven pipeline generation based on business goals'
      case 'mapping': return 'Automated mapping to industry-standard data models'
      case 'api-docs': return 'Comprehensive documentation for AIM DataFlow Platform APIs'
      case 'settings': return 'Manage your account, workspace, and platform preferences'
      default: return 'Monitor your data integration pipelines and system health'
    }
  }

  return (
    <header className="bg-white/80 backdrop-blur-xl shadow-sm border-b border-neutral-200/50 px-6 py-4 sticky top-0 z-40">
      <div className="flex items-center justify-between">
        {/* Page Title */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-4">
            <div>
              <h1 className="text-2xl font-bold text-gradient-primary">
                {getPageTitle(activeTab)}
              </h1>
              <p className="text-sm text-neutral-600 mt-1 max-w-2xl">
                {getPageDescription(activeTab)}
              </p>
            </div>
            {activeTab === 'dashboard' && (
              <div className="hidden lg:flex items-center space-x-2 ml-8">
                <div className="flex items-center space-x-1 bg-success-100 text-success-700 px-3 py-1 rounded-full text-xs font-medium">
                  <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
                  <span>All Systems Operational</span>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Right Side Actions */}
        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative hidden md:block">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="w-4 h-4 text-neutral-400" />
            </div>
            <input 
              type="text" 
              placeholder="Search..." 
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 w-64 bg-neutral-50 border border-neutral-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 text-sm"
            />
          </div>
          
          {/* Notifications */}
          <div className="relative">
            <button 
              onClick={() => setShowNotifications(!showNotifications)}
              className="p-2 rounded-xl hover:bg-neutral-100 transition-colors duration-200 relative"
            >
              <Bell className="w-5 h-5 text-neutral-600" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-error-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              )}
            </button>
            
            {/* Notifications Dropdown */}
            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 bg-white rounded-2xl shadow-2xl border border-neutral-200 z-50 animate-fade-in-down">
                <div className="p-4 border-b border-neutral-200">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-neutral-900">Notifications</h3>
                    <div className="flex items-center space-x-2">
                      {unreadCount > 0 && (
                        <button 
                          onClick={markAllRead}
                          className="text-xs text-primary-600 hover:text-primary-700 font-medium"
                        >
                          Mark all read
                        </button>
                      )}
                      <button 
                        onClick={() => setShowNotifications(false)}
                        className="p-1 hover:bg-neutral-100 rounded-lg"
                      >
                        <X className="w-4 h-4 text-neutral-500" />
                      </button>
                    </div>
                  </div>
                </div>
                
                <div className="max-h-96 overflow-y-auto">
                  {notifications.length > 0 ? (
                    notifications.slice(0, 10).map((notification) => (
                      <div 
                        key={notification.id}
                        className={cn(
                          "p-4 border-b border-neutral-100 hover:bg-neutral-50 transition-colors duration-200 cursor-pointer",
                          !notification.read && "bg-primary-50/50"
                        )}
                        onClick={() => markRead(notification.id)}
                      >
                        <div className="flex items-start space-x-3">
                          <div className={cn(
                            "w-2 h-2 rounded-full mt-2",
                            notification.read ? "bg-neutral-300" : "bg-primary-500"
                          )} />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2">
                              <h4 className="text-sm font-medium text-neutral-900">
                                {notification.title}
                              </h4>
                              <div className={cn(
                                "w-2 h-2 rounded-full",
                                notification.type === 'success' && "bg-success-500",
                                notification.type === 'error' && "bg-error-500",
                                notification.type === 'warning' && "bg-warning-500",
                                notification.type === 'info' && "bg-info-500"
                              )} />
                            </div>
                            <p className="text-sm text-neutral-600 mt-1">
                              {notification.message}
                            </p>
                            <p className="text-xs text-neutral-500 mt-2">
                              {new Date(notification.timestamp).toLocaleString()}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-8 text-center">
                      <CheckCircle className="w-12 h-12 text-neutral-400 mx-auto mb-3" />
                      <p className="text-neutral-600">No notifications</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          
          {/* User Menu */}
          <div className="relative">
            <button 
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-3 p-2 rounded-xl hover:bg-neutral-100 transition-colors duration-200"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center shadow-lg">
                <User className="w-4 h-4 text-white" />
              </div>
              <div className="hidden md:block text-left">
                <div className="text-sm font-medium text-neutral-900">{user?.name || 'User'}</div>
                <div className="text-xs text-neutral-500">{user?.email || '<EMAIL>'}</div>
              </div>
              <ChevronDown className="w-4 h-4 text-neutral-500" />
            </button>
            
            {/* User Menu Dropdown */}
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-64 bg-white rounded-2xl shadow-2xl border border-neutral-200 z-50 animate-fade-in-down">
                <div className="p-4 border-b border-neutral-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center shadow-lg">
                      <User className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <div className="font-semibold text-neutral-900">{user?.name || 'User'}</div>
                      <div className="text-sm text-neutral-600">{user?.email || '<EMAIL>'}</div>
                      <div className="text-xs text-neutral-500 mt-1">{user?.jobTitle || 'User'}</div>
                    </div>
                  </div>
                </div>
                
                <div className="p-2">
                  <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-neutral-700 hover:bg-neutral-100 rounded-xl transition-colors duration-200">
                    <SettingsIcon className="w-4 h-4" />
                    <span>Account Settings</span>
                  </button>
                  {/* <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-neutral-700 hover:bg-neutral-100 rounded-xl transition-colors duration-200">
                    <Palette className="w-4 h-4" />
                    <span>Appearance</span>
                  </button>
                  <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-neutral-700 hover:bg-neutral-100 rounded-xl transition-colors duration-200">
                    <Globe className="w-4 h-4" />
                    <span>Language & Region</span>
                  </button> */}
                  <div className="border-t border-neutral-200 mt-2 pt-2">
                    <button
                      onClick={handleLogout}
                      className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-error-600 hover:bg-error-50 rounded-xl transition-colors duration-200"
                    >
                      <LogOut className="w-4 h-4" />
                      <span>Sign Out</span>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
