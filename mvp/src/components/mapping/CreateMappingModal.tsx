'use client'
import { useState } from 'react'
import { X, Database, Target, Search, Filter, CheckCircle, ArrowRight, Lightbulb, FileText, Zap } from 'lucide-react'
import { DataMapping, DataModel } from '@/types/api'
import { mockDataService } from '@/lib/mockData'
import { cn } from '@/lib/utils'

interface CreateMappingModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: (mapping: DataMapping) => void
}

// Standard data models available for mapping
const standardDataModels = [
  {
    id: 'fhir-r4',
    name: 'Healthcare FHIR R4',
    description: 'Fast Healthcare Interoperability Resources standard for healthcare data exchange',
    industry: 'Healthcare',
    category: 'healthcare',
    compliance_frameworks: ['HIPAA', 'HL7', 'FDA'],
    icon: '🏥',
    popularity: 95,
    entities_count: 150,
    official_website: 'https://hl7.org/fhir/',
    example_usage: 'Electronic health records, clinical systems, healthcare analytics'
  },
  {
    id: 'acord-insurance',
    name: 'ACORD Insurance Data Model',
    description: 'Association for Cooperative Operations Research and Development standard for insurance',
    industry: 'Insurance',
    category: 'finance',
    compliance_frameworks: ['NAIC', 'Solvency II', 'IFRS'],
    icon: '🛡️',
    popularity: 88,
    entities_count: 120,
    official_website: 'https://www.acord.org/',
    example_usage: 'Insurance claims, policy management, regulatory reporting'
  },
  {
    id: 'retail-nrf',
    name: 'Retail Analytics Standard (NRF)',
    description: 'National Retail Federation standard for retail data analytics',
    industry: 'Retail',
    category: 'retail',
    compliance_frameworks: ['PCI DSS', 'GDPR', 'CCPA'],
    icon: '🛒',
    popularity: 82,
    entities_count: 85,
    official_website: 'https://nrf.com/resources/retail-data-standards',
    example_usage: 'E-commerce platforms, inventory management, customer analytics'
  },
  {
    id: 'manufacturing-isa95',
    name: 'Manufacturing ISA-95',
    description: 'International standard for enterprise-control system integration in manufacturing',
    industry: 'Manufacturing',
    category: 'manufacturing',
    compliance_frameworks: ['ISO 9001', 'Six Sigma', 'Lean Manufacturing'],
    icon: '🏭',
    popularity: 79,
    entities_count: 95,
    official_website: 'https://www.isa.org/standards/isa-95/',
    example_usage: 'Production planning, quality management, supply chain optimization'
  },
  {
    id: 'financial-mismo',
    name: 'Financial Services MISMO',
    description: 'Mortgage Industry Standards Maintenance Organization data standards',
    industry: 'Financial Services',
    category: 'finance',
    compliance_frameworks: ['SOX', 'Basel III', 'GDPR'],
    icon: '🏦',
    popularity: 75,
    entities_count: 110,
    official_website: 'https://www.mismo.org/',
    example_usage: 'Mortgage lending, credit reporting, financial risk assessment'
  },
  {
    id: 'logistics-scor',
    name: 'Supply Chain SCOR Model',
    description: 'Supply Chain Operations Reference model for logistics and supply chain',
    industry: 'Logistics',
    category: 'logistics',
    compliance_frameworks: ['ISO 28000', 'C-TPAT', 'WCO'],
    icon: '📦',
    popularity: 71,
    entities_count: 75,
    official_website: 'https://www.apics.org/apics-for-business/frameworks/scor',
    example_usage: 'Supply chain management, logistics planning, vendor management'
  }
]

export default function CreateMappingModal({ isOpen, onClose, onSuccess }: CreateMappingModalProps) {
  const [step, setStep] = useState<'source' | 'target' | 'configure'>('source')
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedIndustry, setSelectedIndustry] = useState<string>('all')
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    source_schema: '',
    source_description: '',
    target_model: '',
    auto_generate_rules: true,
    enable_ai_suggestions: true,
    validation_level: 'standard' as 'basic' | 'standard' | 'strict'
  })
  
  const [selectedTargetModel, setSelectedTargetModel] = useState<any>(null)

  const handleClose = () => {
    setStep('source')
    setFormData({
      name: '',
      description: '',
      source_schema: '',
      source_description: '',
      target_model: '',
      auto_generate_rules: true,
      enable_ai_suggestions: true,
      validation_level: 'standard'
    })
    setSelectedTargetModel(null)
    setSearchQuery('')
    setSelectedIndustry('all')
    onClose()
  }

  const handleSourceNext = () => {
    if (formData.source_schema && formData.name) {
      setStep('target')
    }
  }

  const handleTargetSelect = (model: any) => {
    setSelectedTargetModel(model)
    setFormData(prev => ({ ...prev, target_model: model.id }))
    setStep('configure')
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      
      const mappingData = {
        name: formData.name,
        description: formData.description,
        source_schema: formData.source_schema,
        target_model: selectedTargetModel ? selectedTargetModel.name : formData.target_model,
        mapping_rules: [], // Will be generated by AI
        validation_status: 'pending' as const,
        completion_percentage: 0,
        ai_suggestions: []
      }

      const newMapping = await mockDataService.createDataMapping(mappingData)
      onSuccess(newMapping)
      handleClose()
    } catch (error) {
      console.error('Error creating mapping:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredModels = standardDataModels.filter(model => {
    const matchesSearch = model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      model.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      model.industry.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesIndustry = selectedIndustry === 'all' || model.category === selectedIndustry
    
    return matchesSearch && matchesIndustry
  })

  const industries = ['all', 'healthcare', 'finance', 'retail', 'manufacturing', 'logistics']

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-5xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-200 flex-shrink-0">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-xl flex items-center justify-center">
              <Database className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-neutral-900">Create Data Model Mapping</h2>
              <p className="text-sm text-neutral-600">
                {step === 'source' && 'Define your source data schema'}
                {step === 'target' && 'Select industry-standard target model'}
                {step === 'configure' && 'Configure mapping settings'}
              </p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="w-8 h-8 bg-neutral-100 hover:bg-neutral-200 rounded-lg flex items-center justify-center transition-colors"
          >
            <X className="w-4 h-4 text-neutral-600" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 bg-neutral-50 border-b border-neutral-200 flex-shrink-0">
          <div className="flex items-center space-x-8">
            <div className={cn(
              "flex items-center space-x-2",
              step === 'source' ? "text-primary-600" : "text-neutral-400"
            )}>
              <div className={cn(
                "w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium",
                step === 'source' 
                  ? "bg-primary-100 text-primary-600" 
                  : (step === 'target' || step === 'configure')
                    ? "bg-primary-500 text-white"
                    : "bg-neutral-200 text-neutral-500"
              )}>
                {(step === 'target' || step === 'configure') ? <CheckCircle className="w-4 h-4" /> : '1'}
              </div>
              <span className="text-sm font-medium">Source Schema</span>
            </div>
            
            <ArrowRight className="w-4 h-4 text-neutral-300" />
            
            <div className={cn(
              "flex items-center space-x-2",
              step === 'target' ? "text-primary-600" : "text-neutral-400"
            )}>
              <div className={cn(
                "w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium",
                step === 'target' 
                  ? "bg-primary-100 text-primary-600" 
                  : step === 'configure'
                    ? "bg-primary-500 text-white"
                    : "bg-neutral-200 text-neutral-500"
              )}>
                {step === 'configure' ? <CheckCircle className="w-4 h-4" /> : '2'}
              </div>
              <span className="text-sm font-medium">Target Model</span>
            </div>
            
            <ArrowRight className="w-4 h-4 text-neutral-300" />
            
            <div className={cn(
              "flex items-center space-x-2",
              step === 'configure' ? "text-primary-600" : "text-neutral-400"
            )}>
              <div className={cn(
                "w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium",
                step === 'configure' 
                  ? "bg-primary-100 text-primary-600" 
                  : "bg-neutral-200 text-neutral-500"
              )}>
                3
              </div>
              <span className="text-sm font-medium">Configure</span>
            </div>
          </div>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto">
          {step === 'source' && (
            <div className="p-6 space-y-6">
              <div>
                <h3 className="text-lg font-medium text-neutral-900 mb-4">Source Data Information</h3>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Mapping Name
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        className="input w-full"
                        placeholder="e.g., Customer Data Standardization"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Source Schema Name
                      </label>
                      <input
                        type="text"
                        value={formData.source_schema}
                        onChange={(e) => setFormData(prev => ({ ...prev, source_schema: e.target.value }))}
                        className="input w-full"
                        placeholder="e.g., legacy_crm, user_database"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Description
                      </label>
                      <textarea
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        rows={3}
                        className="input w-full"
                        placeholder="Describe the mapping purpose and scope"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Source Description
                      </label>
                      <textarea
                        value={formData.source_description}
                        onChange={(e) => setFormData(prev => ({ ...prev, source_description: e.target.value }))}
                        rows={3}
                        className="input w-full"
                        placeholder="Describe your source data structure and fields"
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Data Source Examples */}
              <div className="bg-primary-50 border border-primary-200 rounded-xl p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Lightbulb className="w-4 h-4 text-primary-600" />
                  <span className="text-sm font-medium text-primary-700">Tips for Better Mapping</span>
                </div>
                <ul className="text-sm text-primary-600 space-y-1">
                  <li>• Use descriptive names for your source schema (e.g., "customer_data_v1" instead of "table1")</li>
                  <li>• Include field types and constraints in the source description</li>
                  <li>• Mention any business rules or data quality issues</li>
                </ul>
              </div>
            </div>
          )}

          {step === 'target' && (
            <div className="p-6 space-y-6">
              <div>
                <h3 className="text-lg font-medium text-neutral-900 mb-4">Select Target Data Model</h3>
                
                {/* Search and Filters */}
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search models..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="input pl-10 w-full"
                    />
                  </div>
                  
                  <select 
                    value={selectedIndustry}
                    onChange={(e) => setSelectedIndustry(e.target.value)}
                    className="input w-auto min-w-[150px]"
                  >
                    {industries.map(industry => (
                      <option key={industry} value={industry}>
                        {industry === 'all' ? 'All Industries' : industry.charAt(0).toUpperCase() + industry.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              {/* Models Grid */}
              <div className="grid md:grid-cols-2 gap-4">
                {filteredModels.map((model, index) => (
                  <div 
                    key={model.id}
                    className="border-2 border-neutral-200 rounded-xl p-6 hover:border-primary-300 hover:bg-primary-50 transition-all cursor-pointer group"
                    onClick={() => handleTargetSelect(model)}
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="text-2xl">{model.icon}</div>
                        <div>
                          <h4 className="font-medium text-neutral-900 group-hover:text-primary-700">
                            {model.name}
                          </h4>
                          <span className="text-xs px-2 py-1 bg-neutral-100 text-neutral-600 rounded capitalize">
                            {model.industry}
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-neutral-500">Popularity</div>
                        <div className="text-sm font-medium text-neutral-700">{model.popularity}%</div>
                      </div>
                    </div>
                    
                    <p className="text-sm text-neutral-600 mb-4">{model.description}</p>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-neutral-500">Entities:</span>
                        <span className="text-neutral-700">{model.entities_count}</span>
                      </div>
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-neutral-500">Compliance:</span>
                        <span className="text-neutral-700">{model.compliance_frameworks.slice(0, 2).join(', ')}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 text-xs text-primary-600">
                      <FileText className="w-3 h-3" />
                      <span>{model.example_usage}</span>
                    </div>
                  </div>
                ))}
              </div>
              
              {filteredModels.length === 0 && (
                <div className="text-center py-12">
                  <Target className="w-12 h-12 text-neutral-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-neutral-900 mb-2">No models found</h3>
                  <p className="text-neutral-600">Try adjusting your search or filter criteria</p>
                </div>
              )}
            </div>
          )}

          {step === 'configure' && selectedTargetModel && (
            <div className="p-6 space-y-6">
              <div>
                <h3 className="text-lg font-medium text-neutral-900 mb-4">Configure Mapping Settings</h3>
                
                {/* Selected Model Summary */}
                <div className="bg-primary-50 border border-primary-200 rounded-xl p-4 mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{selectedTargetModel.icon}</div>
                    <div>
                      <h4 className="font-medium text-primary-900">{selectedTargetModel.name}</h4>
                      <p className="text-sm text-primary-600">{selectedTargetModel.industry} • {selectedTargetModel.entities_count} entities</p>
                    </div>
                  </div>
                </div>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Validation Level
                      </label>
                      <select
                        value={formData.validation_level}
                        onChange={(e) => setFormData(prev => ({ ...prev, validation_level: e.target.value as any }))}
                        className="input w-full"
                      >
                        <option value="basic">Basic - Fast validation</option>
                        <option value="standard">Standard - Balanced validation</option>
                        <option value="strict">Strict - Comprehensive validation</option>
                      </select>
                      <p className="text-xs text-neutral-500 mt-1">
                        Higher levels provide more thorough validation but take longer to process
                      </p>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="auto_generate"
                          checked={formData.auto_generate_rules}
                          onChange={(e) => setFormData(prev => ({ ...prev, auto_generate_rules: e.target.checked }))}
                          className="rounded border-neutral-300"
                        />
                        <label htmlFor="auto_generate" className="text-sm text-neutral-700">
                          Auto-generate mapping rules using AI
                        </label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="ai_suggestions"
                          checked={formData.enable_ai_suggestions}
                          onChange={(e) => setFormData(prev => ({ ...prev, enable_ai_suggestions: e.target.checked }))}
                          className="rounded border-neutral-300"
                        />
                        <label htmlFor="ai_suggestions" className="text-sm text-neutral-700">
                          Enable AI mapping suggestions
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="bg-secondary-50 border border-secondary-200 rounded-xl p-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <Zap className="w-4 h-4 text-secondary-600" />
                        <span className="text-sm font-medium text-secondary-700">AI-Powered Features</span>
                      </div>
                      <ul className="text-sm text-secondary-600 space-y-1">
                        <li>• Automatic field name matching</li>
                        <li>• Data type compatibility analysis</li>
                        <li>• Transformation suggestions</li>
                        <li>• Validation rule recommendations</li>
                      </ul>
                    </div>
                    
                    <div className="bg-neutral-50 border border-neutral-200 rounded-xl p-4">
                      <h5 className="text-sm font-medium text-neutral-700 mb-2">Compliance Frameworks</h5>
                      <div className="flex flex-wrap gap-2">
                        {selectedTargetModel.compliance_frameworks.map((framework: string) => (
                          <span key={framework} className="text-xs px-2 py-1 bg-white border border-neutral-200 rounded text-neutral-600">
                            {framework}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Fixed Footer */}
        <div className="flex items-center justify-between p-6 border-t border-neutral-200 bg-neutral-50 flex-shrink-0">
          <div className="flex items-center space-x-3">
            {step !== 'source' && (
              <button
                onClick={() => {
                  if (step === 'target') setStep('source')
                  if (step === 'configure') setStep('target')
                }}
                className="btn-secondary"
              >
                Back
              </button>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={handleClose}
              className="btn-secondary"
            >
              Cancel
            </button>
            
            {step === 'source' && (
              <button
                onClick={handleSourceNext}
                disabled={!formData.name || !formData.source_schema}
                className="btn-gradient"
              >
                Next: Select Target
              </button>
            )}
            
            {step === 'target' && (
              <button
                disabled={!selectedTargetModel}
                onClick={() => setStep('configure')}
                className="btn-gradient"
              >
                Next: Configure
              </button>
            )}
            
            {step === 'configure' && (
              <button
                onClick={handleSubmit}
                disabled={loading}
                className="btn-gradient flex items-center space-x-2"
              >
                {loading ? (
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                ) : (
                  <Database className="w-4 h-4" />
                )}
                <span>{loading ? 'Creating...' : 'Create Mapping'}</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
