'use client'
import { useEffect, ComponentType } from 'react'
import { useTokenRefresh } from '@/hooks/useTokenRefresh'

interface WithTokenRefreshOptions {
  refreshOnMount?: boolean
  refreshOnPropsChange?: boolean
}

/**
 * Higher-order component that ensures token is refreshed when component mounts
 * or when specific props change
 */
export function withTokenRefresh<P extends object>(
  WrappedComponent: ComponentType<P>,
  options: WithTokenRefreshOptions = {}
) {
  const {
    refreshOnMount = true,
    refreshOnPropsChange = false
  } = options

  return function WithTokenRefreshComponent(props: P) {
    const { ensureValidToken } = useTokenRefresh({ refreshOnPageChange: false })

    // Refresh token on mount
    useEffect(() => {
      if (refreshOnMount) {
        ensureValidToken()
      }
    }, [ensureValidToken])

    // Refresh token when props change (if enabled)
    useEffect(() => {
      if (refreshOnPropsChange) {
        ensureValidToken()
      }
    }, [props, ensureValidToken, refreshOnPropsChange])

    return <WrappedComponent {...props} />
  }
}

/**
 * Hook that provides a function to wrap async operations with token refresh
 */
export function useAsyncWithTokenRefresh() {
  const { ensureValidToken } = useTokenRefresh({ refreshOnPageChange: false })

  const withTokenRefresh = async <T,>(
    asyncOperation: () => Promise<T>
  ): Promise<T> => {
    await ensureValidToken()
    return asyncOperation()
  }

  return { withTokenRefresh, ensureValidToken }
}
