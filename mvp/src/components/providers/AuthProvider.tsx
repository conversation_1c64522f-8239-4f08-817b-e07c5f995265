'use client'
import { useAuthInit } from '@/hooks/useAuthInit'
import { useTokenRefresh } from '@/hooks/useTokenRefresh'

interface AuthProviderProps {
  children: React.ReactNode
}

export default function AuthProvider({ children }: AuthProviderProps) {
  // Initialize auth state
  useAuthInit()

  // Set up automatic token refresh
  useTokenRefresh({
    refreshOnPageChange: true,
    refreshInterval: 5 * 60 * 1000, // 5 minutes
  })

  return <>{children}</>
}
