'use client'
import { useRouter } from 'next/navigation'
import { ChevronRight, Play, Users, Zap, Target, BarChart3 } from 'lucide-react'
import { cn } from '@/lib/utils'

const onboardingSteps = [
  {
    id: 1,
    title: 'Connect Data Sources',
    description: 'Link your databases, APIs, and systems',
    action: 'Add Connectors',
    href: '/connectors',
    icon: Users,
    color: 'from-blue-500 to-blue-600'
  },
  {
    id: 2,
    title: 'Create Ingestion Pipelines',
    description: 'Set up automated data flows',
    action: 'Create Pipeline',
    href: '/ingestions',
    icon: Zap,
    color: 'from-emerald-500 to-emerald-600'
  },
  {
    id: 3,
    title: 'AI-Powered Migration',
    description: 'Modernize legacy systems automatically',
    action: 'Start Migration',
    href: '/migration',
    icon: BarChart3,
    color: 'from-purple-500 to-purple-600'
  },
  {
    id: 4,
    title: 'Define Business Goals',
    description: 'Let AI generate pipelines from objectives',
    action: 'Set Objectives',
    href: '/objectives',
    icon: Target,
    color: 'from-orange-500 to-orange-600'
  },
  {
    id: 5,
    title: 'Data Model Mapping',
    description: 'Standardize with industry models',
    action: 'Map Models',
    href: '/mapping',
    icon: Play,
    color: 'from-pink-500 to-pink-600'
  }
]

export default function OnboardingGuide() {
  const router = useRouter()

  const handleStepClick = (href: string) => {
    router.push(href)
  }

  return (
    <div className="card-glass p-8 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500" />
      </div>
      
      <div className="relative z-10">
        {/* Header */}
        <div className="flex justify-between items-start mb-8">
          <div className="space-y-2">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gradient-primary">
                  Welcome to Ironbook AI
                </h3>
                <p className="text-neutral-600 mt-1">
                  Get started with these essential steps to unlock the full potential of AI-driven data integration
                </p>
              </div>
            </div>
            

          </div>

        </div>

        {/* Steps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {onboardingSteps.map((step, index) => {
            const Icon = step.icon

            return (
              <button
                key={step.id}
                className={cn(
                  "group relative p-6 rounded-2xl border-2 transition-all duration-300 cursor-pointer text-left",
                  "hover:shadow-lg hover:-translate-y-1",
                  "border-neutral-200 bg-white/50 hover:border-primary-300 hover:bg-primary-50/50"
                )}
                style={{ animationDelay: `${index * 100}ms` }}
                onClick={() => handleStepClick(step.href)}
              >
                {/* Step Icon */}
                <div className="flex items-center justify-between mb-4">
                  <div className={cn(
                    "w-10 h-10 rounded-xl flex items-center justify-center text-sm font-bold transition-all duration-300",
                    `bg-gradient-to-br ${step.color} text-white group-hover:scale-110`
                  )}>
                    <Icon className="w-5 h-5" />
                  </div>

                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <ChevronRight className="w-4 h-4 text-neutral-400" />
                  </div>
                </div>

                {/* Content */}
                <div className="space-y-3">
                  <h4 className="font-semibold text-base transition-colors duration-200 text-neutral-900 group-hover:text-primary-700">
                    {step.title}
                  </h4>
                  <p className="text-sm text-neutral-600 leading-relaxed">
                    {step.description}
                  </p>

                  <div className="flex items-center text-sm font-medium text-primary-600 group-hover:text-primary-700 transition-colors duration-200 mt-4">
                    <span>{step.action}</span>
                    <ChevronRight className="w-3 h-3 ml-1" />
                  </div>
                </div>
              </button>
            )
          })}
        </div>


      </div>
    </div>
  )
}
