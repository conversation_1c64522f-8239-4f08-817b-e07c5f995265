'use client'
import { useAppStore } from '@/store/useAppStore'
import OnboardingGuide from './OnboardingGuide'
import StatsCards from './StatsCards'
import RecentJobs from './RecentJobs'
import QuickActions from './QuickActions'
import ActivityFeed from './ActivityFeed'

export default function DashboardPage() {
  
  return (
    <div className="p-6 space-y-8 min-h-full relative">
      {/* Modern background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-50/30 via-transparent to-secondary-50/20 pointer-events-none" />
      
      {/* Onboarding Guide */}
      {/* {showOnboarding && (
        <div className="animate-fade-in-down relative z-10">
          <OnboardingGuide />
        </div>
      )} */}
        <div className="animate-fade-in-down relative z-10">
          <OnboardingGuide />
        </div>
      
      {/* Welcome Header */}
      {/* <div className="animate-fade-in-up relative z-10" style={{ animationDelay: '50ms' }}>
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gradient-primary mb-2">
            Welcome to IronBook.ai
          </h1>
          <p className="text-neutral-600 text-lg">
            Your AI-driven data migration platform dashboard
          </p>
        </div>
      </div> */}
      
      {/* Stats Cards */}
      {/* <div className="animate-fade-in-up relative z-10" style={{ animationDelay: '100ms' }}>
        <StatsCards />
      </div> */}
      
      {/* Main Content Grid */}
      {/* <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 relative z-10"> */}
        {/* Recent Jobs - Takes 2 columns */}
        {/* <div className="xl:col-span-2 animate-fade-in-left" style={{ animationDelay: '200ms' }}>
          <RecentJobs />
        </div> */}
        
        {/* Right Sidebar */}
        {/* <div className="space-y-6 animate-fade-in-right" style={{ animationDelay: '300ms' }}> */}
          {/* Quick Actions */}
          {/* <QuickActions /> */}
          
          {/* Activity Feed */}
          {/* <ActivityFeed />
        </div>
      </div> */}
      
      {/* Floating Action Button */}
      {/* <div className="fixed bottom-8 right-8 z-50">
        <button className="btn-gradient w-14 h-14 rounded-full shadow-2xl hover-lift interactive">
          <span className="text-2xl">+</span>
        </button>
      </div> */}
    </div>
  )
}
