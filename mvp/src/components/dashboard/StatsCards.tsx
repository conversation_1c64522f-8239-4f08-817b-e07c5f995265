'use client'
import { useMemo } from 'react'
import { 
  TrendingUp, 
  TrendingDown, 
  Database, 
  Activity, 
  Clock, 
  CheckCircle,
  AlertTriangle,
  Users,
  BarChart3,
  Zap
} from 'lucide-react'
import { mockDataService } from '@/lib/mockData'
import { cn } from '@/lib/utils'

interface StatCard {
  id: string
  title: string
  value: string
  subtitle: string
  change: string
  changeType: 'positive' | 'negative' | 'neutral'
  icon: any
  color: string
  trend: number[]
}

export default function StatsCards() {
  const stats = useMemo<StatCard[]>(() => {
    const dashboardStats = mockDataService.getDashboardStats()
    
    return [
      {
        id: 'active-connections',
        title: 'Active Connections',
        value: dashboardStats.activeConnections.toString(),
        subtitle: 'Data sources connected',
        change: '+12% this month',
        changeType: 'positive',
        icon: Database,
        color: 'primary',
        trend: [65, 70, 68, 75, 82, 78, 85]
      },
      {
        id: 'data-processed',
        title: 'Data Processed',
        value: `${dashboardStats.dataProcessedTB.toFixed(1)}TB`,
        subtitle: 'In the last 24 hours',
        change: '+28% vs yesterday',
        changeType: 'positive',
        icon: Activity,
        color: 'success',
        trend: [45, 52, 48, 61, 55, 67, 73]
      },
      {
        id: 'active-pipelines',
        title: 'Active Pipelines',
        value: dashboardStats.activePipelines.toString(),
        subtitle: 'Currently running',
        change: '2 new this week',
        changeType: 'positive',
        icon: Zap,
        color: 'warning',
        trend: [30, 35, 32, 38, 42, 39, 45]
      },
      {
        id: 'success-rate',
        title: 'Success Rate',
        value: `${dashboardStats.successRate}%`,
        subtitle: 'Pipeline reliability',
        change: '+3.2% improvement',
        changeType: 'positive',
        icon: CheckCircle,
        color: 'success',
        trend: [88, 90, 89, 92, 91, 94, 96]
      },
      {
        id: 'avg-latency',
        title: 'Avg Latency',
        value: `${dashboardStats.avgLatencyMs}ms`,
        subtitle: 'Response time',
        change: '-15% faster',
        changeType: 'positive',
        icon: Clock,
        color: 'info',
        trend: [120, 115, 118, 110, 105, 102, 98]
      },
      {
        id: 'alerts',
        title: 'Active Alerts',
        value: dashboardStats.activeAlerts.toString(),
        subtitle: 'Require attention',
        change: dashboardStats.activeAlerts > 0 ? 'Action needed' : 'All clear',
        changeType: dashboardStats.activeAlerts > 0 ? 'negative' : 'positive',
        icon: AlertTriangle,
        color: dashboardStats.activeAlerts > 0 ? 'error' : 'neutral',
        trend: [8, 6, 9, 4, 7, 3, dashboardStats.activeAlerts]
      }
    ]
  }, [])

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'primary':
        return {
          bg: 'bg-primary-500',
          text: 'text-primary-700',
          iconBg: 'bg-primary-100',
          border: 'border-primary-200'
        }
      case 'success':
        return {
          bg: 'bg-success-500',
          text: 'text-success-700',
          iconBg: 'bg-success-100',
          border: 'border-success-200'
        }
      case 'warning':
        return {
          bg: 'bg-warning-500',
          text: 'text-warning-700',
          iconBg: 'bg-warning-100',
          border: 'border-warning-200'
        }
      case 'error':
        return {
          bg: 'bg-error-500',
          text: 'text-error-700',
          iconBg: 'bg-error-100',
          border: 'border-error-200'
        }
      case 'info':
        return {
          bg: 'bg-info-500',
          text: 'text-info-700',
          iconBg: 'bg-info-100',
          border: 'border-info-200'
        }
      default:
        return {
          bg: 'bg-neutral-500',
          text: 'text-neutral-700',
          iconBg: 'bg-neutral-100',
          border: 'border-neutral-200'
        }
    }
  }

  const renderMiniChart = (trend: number[], color: string) => {
    const max = Math.max(...trend)
    const min = Math.min(...trend)
    const range = max - min || 1

    return (
      <div className="flex items-end space-x-1 h-8">
        {trend.map((value, index) => {
          const height = ((value - min) / range) * 100
          return (
            <div
              key={index}
              className={cn(
                "w-1 rounded-full transition-all duration-300",
                getColorClasses(color).bg,
                "opacity-60 hover:opacity-100"
              )}
              style={{ height: `${Math.max(height, 10)}%` }}
            />
          )
        })}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
      {stats.map((stat, index) => {
        const Icon = stat.icon
        const colorClasses = getColorClasses(stat.color)
        
        return (
          <div
            key={stat.id}
            className={cn(
              "card-gradient p-6 group cursor-pointer hover-lift",
              "border-l-4 border-l-transparent hover:border-l-4",
              `hover:${colorClasses.border.replace('border-', 'border-l-')}`,
              "relative overflow-hidden"
            )}
            style={{ animationDelay: `${index * 50}ms` }}
          >
            {/* Subtle background pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 to-secondary-500/10" />
            </div>
            
            {/* Header */}
            <div className="flex items-center justify-between mb-4 relative z-10">
              <div className={cn(
                "w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300",
                colorClasses.iconBg,
                "group-hover:scale-110 group-hover:shadow-lg hover-glow"
              )}>
                <Icon className={cn("w-6 h-6", colorClasses.text)} />
              </div>
              
              {/* Mini Chart */}
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                {renderMiniChart(stat.trend, stat.color)}
              </div>
            </div>
            
            {/* Content */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-neutral-600 uppercase tracking-wide">
                {stat.title}
              </h3>
              <div className="flex items-baseline space-x-2">
                <p className="text-2xl font-bold text-neutral-900 group-hover:text-3xl transition-all duration-300">
                  {stat.value}
                </p>
              </div>
              <p className="text-xs text-neutral-500">
                {stat.subtitle}
              </p>
              
              {/* Change Indicator */}
              <div className="flex items-center space-x-1 mt-3">
                {stat.changeType === 'positive' && (
                  <TrendingUp className="w-3 h-3 text-success-500" />
                )}
                {stat.changeType === 'negative' && (
                  <TrendingDown className="w-3 h-3 text-error-500" />
                )}
                <span className={cn(
                  "text-xs font-medium",
                  stat.changeType === 'positive' && 'text-success-600',
                  stat.changeType === 'negative' && 'text-error-600',
                  stat.changeType === 'neutral' && 'text-neutral-600'
                )}>
                  {stat.change}
                </span>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="w-full bg-neutral-200 rounded-full h-1">
                <div 
                  className={cn(
                    "h-1 rounded-full transition-all duration-500",
                    colorClasses.bg
                  )}
                  style={{ 
                    width: `${Math.random() * 60 + 40}%` // Simulated progress
                  }}
                />
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}
