'use client'
import { Plus, Database, Cable, RefreshCw, BarChart3 } from 'lucide-react'
import { useAppStore } from '@/store/useAppStore'

export default function QuickActions() {
  const { setActiveTab } = useAppStore()

  const actions = [
    {
      id: 'add-connector',
      title: 'Add Connector',
      description: 'Connect new data source',
      icon: Database,
      color: 'from-blue-500 to-blue-600',
      action: () => setActiveTab('connectors')
    },
    {
      id: 'create-pipeline',
      title: 'Create Pipeline',
      description: 'Set up data ingestion',
      icon: Cable,
      color: 'from-emerald-500 to-emerald-600',
      action: () => setActiveTab('ingestions')
    },
    {
      id: 'start-migration',
      title: 'Start Migration',
      description: 'Modernize legacy systems',
      icon: RefreshCw,
      color: 'from-purple-500 to-purple-600',
      action: () => setActiveTab('migration')
    },
    {
      id: 'view-analytics',
      title: 'View Analytics',
      description: 'Check performance metrics',
      icon: BarChart3,
      color: 'from-orange-500 to-orange-600',
      action: () => setActiveTab('dashboard')
    }
  ]

  return (
    <div className="card p-6">
      <h3 className="text-lg font-semibold text-neutral-900 mb-4">Quick Actions</h3>
      
      <div className="space-y-3">
        {actions.map((action, index) => {
          const Icon = action.icon
          return (
            <button
              key={action.id}
              onClick={action.action}
              className="w-full flex items-center space-x-3 p-3 bg-neutral-50 hover:bg-neutral-100 rounded-xl transition-all duration-200 group"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <div className={`w-10 h-10 bg-gradient-to-br ${action.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}>
                <Icon className="w-5 h-5 text-white" />
              </div>
              <div className="flex-1 text-left">
                <h4 className="font-medium text-neutral-900">{action.title}</h4>
                <p className="text-sm text-neutral-600">{action.description}</p>
              </div>
            </button>
          )
        })}
      </div>
    </div>
  )
}
