'use client'
import { useState, useEffect } from 'react'
import { Book, Code, Globe, Lock, ChevronDown, ChevronRight, Copy, Check } from 'lucide-react'
import { cn } from '@/lib/utils'
import toast from 'react-hot-toast'

interface OpenAPISpec {
  openapi: string
  info: {
    title: string
    description: string
    version: string
  }
  paths: Record<string, any>
  components?: {
    schemas?: Record<string, any>
  }
}

interface ApiDocumentationProps {
  type: 'public' | 'private'
}

export default function ApiDocumentation({ type }: ApiDocumentationProps) {
  const [spec, setSpec] = useState<OpenAPISpec | null>(null)
  const [loading, setLoading] = useState(true)
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set())
  const [copiedText, setCopiedText] = useState<string | null>(null)

  useEffect(() => {
    const loadSpec = async () => {
      try {
        const response = await fetch(`/api/api-docs/${type}`)
        const data = await response.json()
        setSpec(data)
      } catch (error) {
        console.error('Failed to load API spec:', error)
        toast.error('Failed to load API documentation')
      } finally {
        setLoading(false)
      }
    }

    loadSpec()
  }, [type])

  const togglePath = (path: string) => {
    const newExpanded = new Set(expandedPaths)
    if (newExpanded.has(path)) {
      newExpanded.delete(path)
    } else {
      newExpanded.add(path)
    }
    setExpandedPaths(newExpanded)
  }

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedText(label)
      toast.success('Copied to clipboard')
      setTimeout(() => setCopiedText(null), 2000)
    } catch (error) {
      toast.error('Failed to copy to clipboard')
    }
  }

  const getMethodColor = (method: string) => {
    switch (method.toLowerCase()) {
      case 'get': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'post': return 'bg-green-100 text-green-800 border-green-200'
      case 'put': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'patch': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'delete': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!spec) {
    return (
      <div className="text-center py-12">
        <Book className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Documentation not available</h3>
        <p className="text-gray-600">Unable to load API documentation.</p>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-4">
          {type === 'public' ? (
            <Globe className="h-8 w-8 text-blue-600" />
          ) : (
            <Lock className="h-8 w-8 text-red-600" />
          )}
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{spec.info.title}</h1>
            <p className="text-gray-600 mt-1">{spec.info.description}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-4 text-sm">
          <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full">
            Version {spec.info.version}
          </span>
          <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full">
            OpenAPI {spec.openapi}
          </span>
          <span className={cn(
            "px-3 py-1 rounded-full",
            type === 'public' 
              ? "bg-blue-100 text-blue-800" 
              : "bg-red-100 text-red-800"
          )}>
            {type === 'public' ? 'Public API' : 'Private API'}
          </span>
        </div>
      </div>

      {/* Base URL */}
      <div className="mb-8 bg-gray-50 rounded-xl p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Base URL</h3>
        <div className="flex items-center space-x-2">
          <code className="bg-white px-3 py-2 rounded-lg border text-sm font-mono flex-1">
            {process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'}
          </code>
          <button
            onClick={() => copyToClipboard(
              process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000',
              'base-url'
            )}
            className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
          >
            {copiedText === 'base-url' ? (
              <Check className="h-4 w-4 text-green-600" />
            ) : (
              <Copy className="h-4 w-4" />
            )}
          </button>
        </div>
      </div>

      {/* Endpoints */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Endpoints</h2>
        
        {Object.entries(spec.paths).map(([path, pathData]) => (
          <div key={path} className="border border-gray-200 rounded-xl overflow-hidden">
            {Object.entries(pathData as Record<string, any>).map(([method, methodData]) => {
              const isExpanded = expandedPaths.has(`${method}-${path}`)
              
              return (
                <div key={`${method}-${path}`} className="border-b border-gray-200 last:border-b-0">
                  <button
                    onClick={() => togglePath(`${method}-${path}`)}
                    className="w-full p-4 text-left hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <span className={cn(
                          "px-3 py-1 rounded-lg text-xs font-semibold uppercase border",
                          getMethodColor(method)
                        )}>
                          {method}
                        </span>
                        <code className="text-sm font-mono text-gray-900">{path}</code>
                        <span className="text-gray-600">{methodData.summary}</span>
                      </div>
                      {isExpanded ? (
                        <ChevronDown className="h-5 w-5 text-gray-400" />
                      ) : (
                        <ChevronRight className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                  </button>
                  
                  {isExpanded && (
                    <div className="px-4 pb-4 bg-gray-50">
                      <div className="space-y-4">
                        {/* Description */}
                        {methodData.description && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">Description</h4>
                            <p className="text-gray-700">{methodData.description}</p>
                          </div>
                        )}
                        
                        {/* Request Body */}
                        {methodData.requestBody && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">Request Body</h4>
                            <div className="bg-white rounded-lg p-3 border">
                              <code className="text-sm">
                                {JSON.stringify(methodData.requestBody, null, 2)}
                              </code>
                            </div>
                          </div>
                        )}
                        
                        {/* Responses */}
                        {methodData.responses && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">Responses</h4>
                            <div className="space-y-2">
                              {Object.entries(methodData.responses).map(([status, response]: [string, any]) => (
                                <div key={status} className="bg-white rounded-lg p-3 border">
                                  <div className="flex items-center space-x-2 mb-2">
                                    <span className={cn(
                                      "px-2 py-1 rounded text-xs font-semibold",
                                      status.startsWith('2') ? "bg-green-100 text-green-800" :
                                      status.startsWith('4') ? "bg-red-100 text-red-800" :
                                      "bg-gray-100 text-gray-800"
                                    )}>
                                      {status}
                                    </span>
                                    <span className="text-sm text-gray-700">{response.description}</span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        ))}
      </div>
    </div>
  )
}
