'use client'
import { useState } from 'react'
import { X, Target, Calendar, Users, DollarSign, CheckCircle, Plus, Minus } from 'lucide-react'
import { BusinessObjective, ObjectiveTemplate } from '@/types/api'
import { mockDataService } from '@/lib/mockData'
import { cn } from '@/lib/utils'

interface CreateObjectiveModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: (objective: BusinessObjective) => void
  templates: ObjectiveTemplate[]
}

interface MetricForm {
  name: string
  description: string
  target_value: string
  unit: string
  type: 'kpi' | 'sla' | 'threshold'
}

// Domain-specific objective templates
const domainTemplates = {
  retail: [
    {
      title: 'Monthly Sales Tracking',
      description: 'Track and optimize monthly sales performance across all channels',
      category: 'analytics',
      metrics: [
        { name: 'Monthly Revenue', description: 'Total monthly sales revenue', target_value: '500000', unit: 'USD', type: 'kpi' as const },
        { name: 'Conversion Rate', description: 'Percentage of visitors who make a purchase', target_value: '3.5', unit: '%', type: 'kpi' as const }
      ],
      business_impact: 'Improved revenue tracking and sales optimization'
    },
    {
      title: 'Customer Retention Analysis',
      description: 'Monitor and improve customer retention rates',
      category: 'analytics',
      metrics: [
        { name: 'Customer Retention Rate', description: 'Percentage of customers retained over 12 months', target_value: '85', unit: '%', type: 'kpi' as const },
        { name: 'Customer Lifetime Value', description: 'Average value of customer relationship', target_value: '1200', unit: 'USD', type: 'kpi' as const }
      ],
      business_impact: 'Enhanced customer loyalty and increased revenue per customer'
    }
  ],
  healthcare: [
    {
      title: 'Patient Care Quality',
      description: 'Monitor and improve patient care quality metrics',
      category: 'compliance',
      metrics: [
        { name: 'Patient Satisfaction Score', description: 'Average patient satisfaction rating', target_value: '4.5', unit: '/5', type: 'kpi' as const },
        { name: 'Treatment Success Rate', description: 'Percentage of successful treatments', target_value: '95', unit: '%', type: 'threshold' as const }
      ],
      business_impact: 'Improved patient outcomes and regulatory compliance'
    },
    {
      title: 'HIPAA Compliance Monitoring',
      description: 'Ensure continuous HIPAA compliance across all systems',
      category: 'compliance',
      metrics: [
        { name: 'Compliance Score', description: 'HIPAA compliance percentage', target_value: '100', unit: '%', type: 'threshold' as const },
        { name: 'Security Incidents', description: 'Number of security incidents per month', target_value: '0', unit: 'incidents', type: 'threshold' as const }
      ],
      business_impact: 'Maintained regulatory compliance and patient trust'
    }
  ],
  finance: [
    {
      title: 'Risk Management',
      description: 'Monitor and control financial risk exposure',
      category: 'compliance',
      metrics: [
        { name: 'Risk Score', description: 'Overall portfolio risk rating', target_value: '3', unit: '/10', type: 'threshold' as const },
        { name: 'Regulatory Compliance', description: 'Percentage of regulatory requirements met', target_value: '100', unit: '%', type: 'threshold' as const }
      ],
      business_impact: 'Reduced financial risk and regulatory compliance'
    },
    {
      title: 'Fraud Detection',
      description: 'Detect and prevent fraudulent transactions',
      category: 'security',
      metrics: [
        { name: 'Detection Rate', description: 'Percentage of fraud cases detected', target_value: '99.5', unit: '%', type: 'kpi' as const },
        { name: 'False Positive Rate', description: 'Percentage of false fraud alerts', target_value: '2', unit: '%', type: 'threshold' as const }
      ],
      business_impact: 'Reduced fraud losses and improved customer experience'
    }
  ],
  manufacturing: [
    {
      title: 'Production Efficiency',
      description: 'Optimize manufacturing processes and reduce waste',
      category: 'performance',
      metrics: [
        { name: 'Overall Equipment Effectiveness', description: 'OEE percentage', target_value: '85', unit: '%', type: 'kpi' as const },
        { name: 'Defect Rate', description: 'Percentage of defective products', target_value: '0.5', unit: '%', type: 'threshold' as const }
      ],
      business_impact: 'Increased production efficiency and reduced operational costs'
    },
    {
      title: 'Supply Chain Optimization',
      description: 'Monitor and optimize supply chain performance',
      category: 'cost_optimization',
      metrics: [
        { name: 'On-Time Delivery Rate', description: 'Percentage of deliveries on time', target_value: '98', unit: '%', type: 'kpi' as const },
        { name: 'Inventory Turnover', description: 'Number of inventory cycles per year', target_value: '12', unit: 'cycles', type: 'kpi' as const }
      ],
      business_impact: 'Improved supply chain efficiency and reduced inventory costs'
    }
  ]
}

export default function CreateObjectiveModal({ isOpen, onClose, onSuccess, templates }: CreateObjectiveModalProps) {
  const [step, setStep] = useState<'domain' | 'template' | 'form'>('domain')
  const [selectedDomain, setSelectedDomain] = useState<string>('')
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  
  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium' as 'high' | 'medium' | 'low',
    category: 'data_quality' as any,
    business_impact: '',
    stakeholders: [''],
    technical_requirements: [''],
    start_date: new Date().toISOString().split('T')[0],
    target_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  })
  
  const [metrics, setMetrics] = useState<MetricForm[]>([
    { name: '', description: '', target_value: '', unit: '', type: 'kpi' }
  ])

  const handleDomainSelect = (domain: string) => {
    setSelectedDomain(domain)
    setStep('template')
  }

  const handleTemplateSelect = (template: any) => {
    setSelectedTemplate(template)
    setFormData(prev => ({
      ...prev,
      title: template.title,
      description: template.description,
      category: template.category,
      business_impact: template.business_impact
    }))
    setMetrics(template.metrics)
    setStep('form')
  }

  const handleCustomForm = () => {
    setSelectedTemplate(null)
    setStep('form')
  }

  const addMetric = () => {
    setMetrics([...metrics, { name: '', description: '', target_value: '', unit: '', type: 'kpi' }])
  }

  const removeMetric = (index: number) => {
    if (metrics.length > 1) {
      setMetrics(metrics.filter((_, i) => i !== index))
    }
  }

  const updateMetric = (index: number, field: keyof MetricForm, value: string) => {
    const updated = [...metrics]
    updated[index] = { ...updated[index], [field]: value }
    setMetrics(updated)
  }

  const addStakeholder = () => {
    setFormData(prev => ({ ...prev, stakeholders: [...prev.stakeholders, ''] }))
  }

  const updateStakeholder = (index: number, value: string) => {
    const updated = [...formData.stakeholders]
    updated[index] = value
    setFormData(prev => ({ ...prev, stakeholders: updated }))
  }

  const removeStakeholder = (index: number) => {
    if (formData.stakeholders.length > 1) {
      setFormData(prev => ({
        ...prev,
        stakeholders: prev.stakeholders.filter((_, i) => i !== index)
      }))
    }
  }

  const addTechnicalRequirement = () => {
    setFormData(prev => ({ ...prev, technical_requirements: [...prev.technical_requirements, ''] }))
  }

  const updateTechnicalRequirement = (index: number, value: string) => {
    const updated = [...formData.technical_requirements]
    updated[index] = value
    setFormData(prev => ({ ...prev, technical_requirements: updated }))
  }

  const removeTechnicalRequirement = (index: number) => {
    if (formData.technical_requirements.length > 1) {
      setFormData(prev => ({
        ...prev,
        technical_requirements: prev.technical_requirements.filter((_, i) => i !== index)
      }))
    }
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      
      const objectiveData = {
        ...formData,
        metrics: metrics.map((metric, index) => ({
          id: `metric-${Date.now()}-${index}`,
          name: metric.name,
          description: metric.description,
          target_value: parseFloat(metric.target_value) || 0,
          current_value: 0,
          unit: metric.unit,
          type: metric.type
        })),
        timeline: {
          start_date: formData.start_date,
          target_date: formData.target_date
        },
        stakeholders: formData.stakeholders.filter(s => s.trim() !== ''),
        technical_requirements: formData.technical_requirements.filter(r => r.trim() !== '')
      }

      const newObjective = await mockDataService.createObjective(objectiveData)
      onSuccess(newObjective)
      handleClose()
    } catch (error) {
      console.error('Error creating objective:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setStep('domain')
    setSelectedDomain('')
    setSelectedTemplate(null)
    setFormData({
      title: '',
      description: '',
      priority: 'medium',
      category: 'data_quality',
      business_impact: '',
      stakeholders: [''],
      technical_requirements: [''],
      start_date: new Date().toISOString().split('T')[0],
      target_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    })
    setMetrics([{ name: '', description: '', target_value: '', unit: '', type: 'kpi' }])
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-200 flex-shrink-0">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center">
              <Target className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-neutral-900">Create Business Objective</h2>
              <p className="text-sm text-neutral-600">
                {step === 'domain' && 'Choose your business domain'}
                {step === 'template' && 'Select a template or create custom'}
                {step === 'form' && 'Define your objective details'}
              </p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="w-8 h-8 bg-neutral-100 hover:bg-neutral-200 rounded-lg flex items-center justify-center transition-colors"
          >
            <X className="w-4 h-4 text-neutral-600" />
          </button>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto">
          {step === 'domain' && (
            <div className="p-6">
              <h3 className="text-lg font-medium text-neutral-900 mb-6">Select Your Business Domain</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(domainTemplates).map(([domain, templates]) => (
                  <button
                    key={domain}
                    onClick={() => handleDomainSelect(domain)}
                    className="p-6 border-2 border-neutral-200 rounded-xl hover:border-primary-300 hover:bg-primary-50 transition-all text-left group"
                  >
                    <h4 className="font-medium text-neutral-900 capitalize mb-2 group-hover:text-primary-700">
                      {domain}
                    </h4>
                    <p className="text-sm text-neutral-600 mb-3">
                      {templates.length} objective templates available
                    </p>
                    <div className="text-xs text-neutral-500">
                      {templates.map(t => t.title).join(', ')}
                    </div>
                  </button>
                ))}
              </div>
              
              <div className="mt-6 pt-6 border-t border-neutral-200">
                <button
                  onClick={handleCustomForm}
                  className="w-full p-4 border-2 border-dashed border-neutral-300 rounded-xl hover:border-primary-300 hover:bg-primary-50 transition-all"
                >
                  <div className="text-center">
                    <Plus className="w-6 h-6 text-neutral-400 mx-auto mb-2" />
                    <p className="font-medium text-neutral-700">Create Custom Objective</p>
                    <p className="text-sm text-neutral-500">Start from scratch with a blank form</p>
                  </div>
                </button>
              </div>
            </div>
          )}

          {step === 'template' && selectedDomain && (
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-neutral-900 capitalize">
                  {selectedDomain} Objectives
                </h3>
                <button
                  onClick={() => setStep('domain')}
                  className="text-sm text-primary-600 hover:text-primary-700"
                >
                  ← Back to domains
                </button>
              </div>
              
              <div className="space-y-4">
                {domainTemplates[selectedDomain as keyof typeof domainTemplates].map((template, index) => (
                  <button
                    key={index}
                    onClick={() => handleTemplateSelect(template)}
                    className="w-full p-6 border-2 border-neutral-200 rounded-xl hover:border-primary-300 hover:bg-primary-50 transition-all text-left group"
                  >
                    <h4 className="font-medium text-neutral-900 mb-2 group-hover:text-primary-700">
                      {template.title}
                    </h4>
                    <p className="text-sm text-neutral-600 mb-3">{template.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs px-2 py-1 bg-neutral-100 text-neutral-700 rounded capitalize">
                        {template.category.replace('_', ' ')}
                      </span>
                      <span className="text-xs text-neutral-500">
                        {template.metrics.length} metrics included
                      </span>
                    </div>
                  </button>
                ))}
              </div>
              
              <div className="mt-6 pt-6 border-t border-neutral-200">
                <button
                  onClick={handleCustomForm}
                  className="w-full p-4 border-2 border-dashed border-neutral-300 rounded-xl hover:border-primary-300 hover:bg-primary-50 transition-all"
                >
                  <div className="text-center">
                    <Plus className="w-6 h-6 text-neutral-400 mx-auto mb-2" />
                    <p className="font-medium text-neutral-700">Create Custom Objective</p>
                  </div>
                </button>
              </div>
            </div>
          )}

          {step === 'form' && (
            <div className="p-6 space-y-6">
              {selectedTemplate && (
                <div className="mb-6 p-4 bg-primary-50 border border-primary-200 rounded-xl">
                  <div className="flex items-center space-x-2 mb-2">
                    <CheckCircle className="w-4 h-4 text-primary-600" />
                    <span className="text-sm font-medium text-primary-700">Template Selected</span>
                  </div>
                  <p className="text-sm text-primary-600">{selectedTemplate.title}</p>
                </div>
              )}

              {/* Basic Information */}
              <div className="space-y-4">
                <h4 className="font-medium text-neutral-900">Basic Information</h4>
                
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Objective Title
                    </label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                      className="input w-full"
                      placeholder="Enter objective title"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Priority
                    </label>
                    <select
                      value={formData.priority}
                      onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as any }))}
                      className="input w-full"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    className="input w-full"
                    placeholder="Describe the objective and its goals"
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Category
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value as any }))}
                      className="input w-full"
                    >
                      <option value="data_quality">Data Quality</option>
                      <option value="performance">Performance</option>
                      <option value="compliance">Compliance</option>
                      <option value="cost_optimization">Cost Optimization</option>
                      <option value="analytics">Analytics</option>
                      <option value="security">Security</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Business Impact
                    </label>
                    <input
                      type="text"
                      value={formData.business_impact}
                      onChange={(e) => setFormData(prev => ({ ...prev, business_impact: e.target.value }))}
                      className="input w-full"
                      placeholder="Expected business impact"
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Start Date
                    </label>
                    <input
                      type="date"
                      value={formData.start_date}
                      onChange={(e) => setFormData(prev => ({ ...prev, start_date: e.target.value }))}
                      className="input w-full"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Target Date
                    </label>
                    <input
                      type="date"
                      value={formData.target_date}
                      onChange={(e) => setFormData(prev => ({ ...prev, target_date: e.target.value }))}
                      className="input w-full"
                    />
                  </div>
                </div>
              </div>

              {/* Metrics */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-neutral-900">Success Metrics</h4>
                  <button
                    type="button"
                    onClick={addMetric}
                    className="btn-secondary-sm flex items-center space-x-1"
                  >
                    <Plus className="w-3 h-3" />
                    <span>Add Metric</span>
                  </button>
                </div>
                
                {metrics.map((metric, index) => (
                  <div key={index} className="p-4 border border-neutral-200 rounded-lg space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-neutral-700">Metric {index + 1}</span>
                      {metrics.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeMetric(index)}
                          className="text-error-500 hover:text-error-600"
                        >
                          <Minus className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                    
                    <div className="grid md:grid-cols-2 gap-3">
                      <input
                        type="text"
                        value={metric.name}
                        onChange={(e) => updateMetric(index, 'name', e.target.value)}
                        placeholder="Metric name"
                        className="input w-full"
                      />
                      <input
                        type="text"
                        value={metric.description}
                        onChange={(e) => updateMetric(index, 'description', e.target.value)}
                        placeholder="Description"
                        className="input w-full"
                      />
                    </div>
                    
                    <div className="grid grid-cols-3 gap-3">
                      <input
                        type="number"
                        value={metric.target_value}
                        onChange={(e) => updateMetric(index, 'target_value', e.target.value)}
                        placeholder="Target value"
                        className="input w-full"
                        step="0.01"
                      />
                      <input
                        type="text"
                        value={metric.unit}
                        onChange={(e) => updateMetric(index, 'unit', e.target.value)}
                        placeholder="Unit (%, USD, etc.)"
                        className="input w-full"
                      />
                      <select
                        value={metric.type}
                        onChange={(e) => updateMetric(index, 'type', e.target.value)}
                        className="input w-full"
                      >
                        <option value="kpi">KPI</option>
                        <option value="sla">SLA</option>
                        <option value="threshold">Threshold</option>
                      </select>
                    </div>
                  </div>
                ))}
              </div>

              {/* Stakeholders */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-neutral-900">Stakeholders</h4>
                  <button
                    type="button"
                    onClick={addStakeholder}
                    className="btn-secondary-sm flex items-center space-x-1"
                  >
                    <Plus className="w-3 h-3" />
                    <span>Add Stakeholder</span>
                  </button>
                </div>
                
                {formData.stakeholders.map((stakeholder, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-neutral-400" />
                    <input
                      type="text"
                      value={stakeholder}
                      onChange={(e) => updateStakeholder(index, e.target.value)}
                      placeholder="Stakeholder name or team"
                      className="input flex-1"
                    />
                    {formData.stakeholders.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeStakeholder(index)}
                        className="text-error-500 hover:text-error-600"
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>

              {/* Technical Requirements */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-neutral-900">Technical Requirements</h4>
                  <button
                    type="button"
                    onClick={addTechnicalRequirement}
                    className="btn-secondary-sm flex items-center space-x-1"
                  >
                    <Plus className="w-3 h-3" />
                    <span>Add Requirement</span>
                  </button>
                </div>
                
                {formData.technical_requirements.map((requirement, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-neutral-400" />
                    <input
                      type="text"
                      value={requirement}
                      onChange={(e) => updateTechnicalRequirement(index, e.target.value)}
                      placeholder="Technical requirement"
                      className="input flex-1"
                    />
                    {formData.technical_requirements.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeTechnicalRequirement(index)}
                        className="text-error-500 hover:text-error-600"
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Fixed Footer - only show for domain/template steps with navigation */}
        {step === 'domain' && (
          <div className="p-6 border-t border-neutral-200 bg-neutral-50 flex-shrink-0">
            <div className="flex justify-end">
              <button
                onClick={handleClose}
                className="btn-secondary"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        {step === 'template' && (
          <div className="p-6 border-t border-neutral-200 bg-neutral-50 flex-shrink-0">
            <div className="flex items-center justify-between">
              <button
                onClick={() => setStep('domain')}
                className="btn-secondary"
              >
                Back
              </button>
              <button
                onClick={handleClose}
                className="btn-secondary"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        {/* Footer for form step */}
        {step === 'form' && (
          <div className="flex items-center justify-between p-6 border-t border-neutral-200 bg-neutral-50 flex-shrink-0">
            <button
              onClick={() => selectedTemplate ? setStep('template') : setStep('domain')}
              className="btn-secondary"
            >
              Back
            </button>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleClose}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                disabled={loading || !formData.title || !formData.description}
                className="btn-gradient flex items-center space-x-2"
              >
                {loading ? (
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                ) : (
                  <Target className="w-4 h-4" />
                )}
                <span>{loading ? 'Creating...' : 'Create Objective'}</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
