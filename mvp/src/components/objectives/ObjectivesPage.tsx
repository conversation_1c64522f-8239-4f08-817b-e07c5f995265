'use client'
import { useState, useEffect } from 'react'
import { 
  Target, 
  Plus, 
  Search, 
  Filter, 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  Users, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  BarChart3,
  Lightbulb,
  Edit,
  Trash2,
  Eye,
  Star,
  Flag
} from 'lucide-react'
import { BusinessObjective, ObjectiveTemplate } from '@/types/api'
import { mockDataService } from '@/lib/mockData'
import { cn } from '@/lib/utils'
import CreateObjectiveModal from './CreateObjectiveModal'

export default function ObjectivesPage() {
  const [objectives, setObjectives] = useState<BusinessObjective[]>([])
  const [templates, setTemplates] = useState<ObjectiveTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedObjective, setSelectedObjective] = useState<BusinessObjective | null>(null)

  useEffect(() => {
    loadData()
  }, [])

  const handleObjectiveCreated = (newObjective: BusinessObjective) => {
    setObjectives(prev => [newObjective, ...prev])
  }

  const loadData = async () => {
    try {
      setLoading(true)
      const [objectivesData, templatesData] = await Promise.all([
        mockDataService.getObjectives(),
        mockDataService.getObjectiveTemplates()
      ])
      setObjectives(objectivesData)
      setTemplates(templatesData)
    } catch (error) {
      console.error('Error loading objectives:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredObjectives = objectives.filter(objective => {
    const matchesSearch = objective.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      objective.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = filterStatus === 'all' || objective.status === filterStatus
    const matchesCategory = filterCategory === 'all' || objective.category === filterCategory
    
    return matchesSearch && matchesStatus && matchesCategory
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-success-100 text-success-800 border-success-200'
      case 'completed':
        return 'bg-primary-100 text-primary-800 border-primary-200'
      case 'draft':
        return 'bg-neutral-100 text-neutral-700 border-neutral-200'
      default:
        return 'bg-neutral-100 text-neutral-700 border-neutral-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-error-600'
      case 'medium':
        return 'text-warning-600'
      case 'low':
        return 'text-success-600'
      default:
        return 'text-neutral-600'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'data_quality':
        return <CheckCircle className="w-5 h-5" />
      case 'performance':
        return <TrendingUp className="w-5 h-5" />
      case 'compliance':
        return <Flag className="w-5 h-5" />
      case 'cost_optimization':
        return <BarChart3 className="w-5 h-5" />
      case 'analytics':
        return <Target className="w-5 h-5" />
      case 'security':
        return <AlertCircle className="w-5 h-5" />
      default:
        return <Target className="w-5 h-5" />
    }
  }

  const getMetricProgress = (metric: any) => {
    const progress = (metric.current_value / metric.target_value) * 100
    return Math.min(progress, 100)
  }

  const stats = {
    total: objectives.length,
    active: objectives.filter(obj => obj.status === 'active').length,
    completed: objectives.filter(obj => obj.status === 'completed').length,
    draft: objectives.filter(obj => obj.status === 'draft').length
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-neutral-200 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-neutral-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-48 bg-neutral-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6 relative">
      {/* Modern background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-50/30 via-transparent to-secondary-50/20 pointer-events-none" />
      
      {/* Header */}
      <div className="flex items-center justify-between relative z-10">
        <div>
          <h1 className="text-3xl font-bold text-gradient-primary">Business Objectives</h1>
          <p className="text-neutral-600 mt-2">AI-driven pipeline generation based on business goals</p>
        </div>
        <div className="flex items-center space-x-3">
          <button 
            onClick={() => setShowCreateModal(true)}
            className="btn-gradient flex items-center space-x-2 hover-lift"
          >
            <Plus className="w-4 h-4" />
            <span>New Objective</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 relative z-10">
        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">Total Objectives</p>
              <p className="text-2xl font-bold text-neutral-900">{stats.total}</p>
            </div>
            <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
              <Target className="w-6 h-6 text-primary-600" />
            </div>
          </div>
        </div>

        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">Active</p>
              <p className="text-2xl font-bold text-success-600">{stats.active}</p>
            </div>
            <div className="w-12 h-12 bg-success-100 rounded-xl flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-success-600" />
            </div>
          </div>
        </div>

        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">Completed</p>
              <p className="text-2xl font-bold text-primary-600">{stats.completed}</p>
            </div>
            <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-primary-600" />
            </div>
          </div>
        </div>

        <div className="card-gradient p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-neutral-600 mb-1">Draft</p>
              <p className="text-2xl font-bold text-neutral-600">{stats.draft}</p>
            </div>
            <div className="w-12 h-12 bg-neutral-100 rounded-xl flex items-center justify-center">
              <Clock className="w-6 h-6 text-neutral-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 relative z-10">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search objectives..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="input pl-10 w-full"
          />
        </div>

        {/* Status Filter */}
        <select 
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="input w-auto"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="completed">Completed</option>
          <option value="draft">Draft</option>
          <option value="archived">Archived</option>
        </select>

        {/* Category Filter */}
        <select 
          value={filterCategory}
          onChange={(e) => setFilterCategory(e.target.value)}
          className="input w-auto"
        >
          <option value="all">All Categories</option>
          <option value="data_quality">Data Quality</option>
          <option value="performance">Performance</option>
          <option value="compliance">Compliance</option>
          <option value="cost_optimization">Cost Optimization</option>
          <option value="analytics">Analytics</option>
          <option value="security">Security</option>
        </select>
      </div>

      {/* Objectives Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 relative z-10">
        {filteredObjectives.map((objective, index) => (
          <div 
            key={objective.id}
            className="card-gradient p-6 hover-lift group cursor-pointer relative overflow-hidden"
            style={{ animationDelay: `${index * 50}ms` }}
            onClick={() => setSelectedObjective(objective)}
          >
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className={cn(
                  "w-10 h-10 rounded-lg flex items-center justify-center",
                  objective.category === 'data_quality' && "bg-success-100 text-success-600",
                  objective.category === 'performance' && "bg-primary-100 text-primary-600",
                  objective.category === 'compliance' && "bg-warning-100 text-warning-600",
                  objective.category === 'cost_optimization' && "bg-info-100 text-info-600",
                  objective.category === 'analytics' && "bg-secondary-100 text-secondary-600",
                  objective.category === 'security' && "bg-error-100 text-error-600"
                )}>
                  {getCategoryIcon(objective.category)}
                </div>
                <div>
                  <h3 className="font-semibold text-neutral-900 group-hover:text-primary-600 transition-colors">
                    {objective.title}
                  </h3>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className={cn("badge", getStatusColor(objective.status))}>
                      {objective.status}
                    </span>
                    <Flag className={cn("w-3 h-3", getPriorityColor(objective.priority))} />
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button className="w-8 h-8 bg-neutral-100 hover:bg-neutral-200 rounded-lg flex items-center justify-center transition-colors">
                  <Eye className="w-4 h-4 text-neutral-600" />
                </button>
                <button className="w-8 h-8 bg-neutral-100 hover:bg-neutral-200 rounded-lg flex items-center justify-center transition-colors">
                  <Edit className="w-4 h-4 text-neutral-600" />
                </button>
              </div>
            </div>

            {/* Description */}
            <p className="text-neutral-600 text-sm mb-4 line-clamp-2">
              {objective.description}
            </p>

            {/* Metrics */}
            <div className="space-y-3 mb-4">
              {objective.metrics.slice(0, 2).map((metric) => (
                <div key={metric.id} className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-neutral-700">{metric.name}</span>
                    <span className="text-neutral-900 font-medium">
                      {metric.current_value}{metric.unit} / {metric.target_value}{metric.unit}
                    </span>
                  </div>
                  <div className="progress-bar">
                    <div 
                      className="progress-fill"
                      style={{ width: `${getMetricProgress(metric)}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>

            {/* Timeline */}
            <div className="flex items-center justify-between text-sm text-neutral-600">
              <div className="flex items-center space-x-1">
                <Calendar className="w-4 h-4" />
                <span>Due {new Date(objective.timeline.target_date).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Users className="w-4 h-4" />
                <span>{objective.stakeholders.length} stakeholders</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredObjectives.length === 0 && (
        <div className="text-center py-12 relative z-10">
          <div className="w-24 h-24 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Target className="w-12 h-12 text-neutral-400" />
          </div>
          <h3 className="text-lg font-medium text-neutral-900 mb-2">No objectives found</h3>
          <p className="text-neutral-600 mb-6">
            {searchQuery || filterStatus !== 'all' || filterCategory !== 'all' 
              ? 'Try adjusting your search or filters'
              : 'Create your first business objective to get started'
            }
          </p>
          <button 
            onClick={() => setShowCreateModal(true)}
            className="btn-gradient"
          >
            Create Objective
          </button>
        </div>
      )}

      {/* Templates Section */}
      <div className="relative z-10">
        <div className="flex items-center space-x-2 mb-6">
          <Lightbulb className="w-5 h-5 text-warning-500" />
          <h2 className="text-xl font-semibold text-neutral-900">Quick Start Templates</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {templates.map((template) => (
            <div 
              key={template.id}
              className="card p-4 hover:shadow-lg transition-all duration-200 cursor-pointer border-2 border-transparent hover:border-primary-200"
            >
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-medium text-neutral-900">{template.name}</h4>
                  <p className="text-sm text-neutral-600 mt-1">{template.description}</p>
                </div>
                <span className="badge badge-neutral text-xs">
                  {template.estimated_timeline} days
                </span>
              </div>
              
              <div className="text-xs text-neutral-500">
                {template.metrics.length} metrics included
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Create Objective Modal */}
      <CreateObjectiveModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={handleObjectiveCreated}
        templates={templates}
      />
    </div>
  )
}
