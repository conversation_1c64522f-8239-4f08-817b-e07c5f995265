{"name": "aim-offline-demo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^2.30.0", "immer": "^10.1.1", "lucide-react": "^0.294.0", "next": "14.0.4", "next-auth": "^4.24.5", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-vertical-timeline-component": "^3.6.0", "recharts": "^2.8.0", "tailwind-merge": "^2.6.0", "yup": "^1.4.0", "zustand": "^4.5.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-vertical-timeline-component": "^3.3.6", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}