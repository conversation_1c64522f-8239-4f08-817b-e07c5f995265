{"openapi": "3.1.0", "info": {"title": "User Account Service for AIM", "description": "FastAPI User Account Service with Supabase authentication and Airbyte workspace integration", "version": "1.0.0"}, "paths": {"/signup": {"post": {"tags": ["authentication"], "summary": "Signup", "description": "Register a new user with Airbyte workspace creation.", "operationId": "signup_signup_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSignup"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/login": {"post": {"tags": ["authentication"], "summary": "<PERSON><PERSON>", "description": "Login user and return authentication tokens.", "operationId": "login_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLogin"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/refresh": {"post": {"tags": ["authentication"], "summary": "Refresh <PERSON>", "description": "Refresh access token.", "operationId": "refresh_token_refresh_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshToken"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/forgot-password": {"post": {"tags": ["authentication"], "summary": "Forgot Password", "description": "Send password reset email.", "operationId": "forgot_password_forgot_password_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPassword"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/resend-verification": {"post": {"tags": ["authentication"], "summary": "Resend Verification", "description": "Resend verification email.", "operationId": "resend_verification_resend_verification_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResendVerification"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/": {"get": {"summary": "Root", "description": "Root endpoint.", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"summary": "Health Check", "description": "Health check endpoint.", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"AuthResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "refresh_token": {"type": "string", "title": "Refresh <PERSON>"}, "token_type": {"type": "string", "title": "Token Type", "default": "bearer"}, "expires_in": {"type": "integer", "title": "Expires In"}, "user_id": {"type": "string", "title": "User Id"}}, "type": "object", "required": ["access_token", "refresh_token", "expires_in", "user_id"], "title": "AuthResponse"}, "ForgotPassword": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}}, "type": "object", "required": ["email"], "title": "ForgotPassword"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "MessageResponse": {"properties": {"message": {"type": "string", "title": "Message"}, "success": {"type": "boolean", "title": "Success", "default": true}}, "type": "object", "required": ["message"], "title": "MessageResponse"}, "RefreshToken": {"properties": {"refresh_token": {"type": "string", "title": "Refresh <PERSON>"}}, "type": "object", "required": ["refresh_token"], "title": "RefreshToken"}, "ResendVerification": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}}, "type": "object", "required": ["email"], "title": "ResendVerification"}, "UserLogin": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "password": {"type": "string", "title": "Password"}}, "type": "object", "required": ["email", "password"], "title": "UserLogin"}, "UserSignup": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "password": {"type": "string", "title": "Password"}}, "type": "object", "required": ["email", "password"], "title": "UserSignup"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}